#!/bin/bash

# OSDAN Production Deployment Script
# This script handles the deployment of the OSDAN application to production

set -e  # Exit on any error

echo "🚀 Starting OSDAN Deployment..."

# Configuration
APP_NAME="OSDAN"
DEPLOY_USER="www-data"
DEPLOY_PATH="/var/www/osdan"
BACKUP_PATH="/var/backups/osdan"
LOG_FILE="/var/log/osdan-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a $LOG_FILE
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a $LOG_FILE
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a $LOG_FILE
}

# Check if running as root or with sudo
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root or with sudo"
    fi
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Running pre-deployment checks..."
    
    # Check if PHP is installed
    if ! command -v php &> /dev/null; then
        error "PHP is not installed"
    fi
    
    # Check PHP version (requires 8.2+)
    PHP_VERSION=$(php -r "echo PHP_MAJOR_VERSION.'.'.PHP_MINOR_VERSION;")
    if [[ $(echo "$PHP_VERSION < 8.2" | bc -l) ]]; then
        error "PHP 8.2 or higher is required. Current version: $PHP_VERSION"
    fi
    
    # Check if Composer is installed
    if ! command -v composer &> /dev/null; then
        error "Composer is not installed"
    fi
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        error "npm is not installed"
    fi
    
    # Check if required PHP extensions are installed
    REQUIRED_EXTENSIONS=("pdo" "mysql" "redis" "gd" "curl" "mbstring" "xml" "zip")
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if ! php -m | grep -q "^$ext$"; then
            error "Required PHP extension '$ext' is not installed"
        fi
    done
    
    log "✅ All pre-deployment checks passed"
}

# Create backup
create_backup() {
    if [ -d "$DEPLOY_PATH" ]; then
        log "Creating backup of current deployment..."
        
        TIMESTAMP=$(date +%Y%m%d_%H%M%S)
        BACKUP_DIR="$BACKUP_PATH/backup_$TIMESTAMP"
        
        mkdir -p $BACKUP_PATH
        cp -r $DEPLOY_PATH $BACKUP_DIR
        
        # Backup database
        if [ ! -z "$DB_DATABASE" ] && [ ! -z "$DB_USERNAME" ]; then
            log "Creating database backup..."
            mysqldump -u$DB_USERNAME -p$DB_PASSWORD $DB_DATABASE > $BACKUP_DIR/database_backup.sql
        fi
        
        log "✅ Backup created at $BACKUP_DIR"
    else
        info "No existing deployment found, skipping backup"
    fi
}

# Install system dependencies
install_system_dependencies() {
    log "Installing system dependencies..."
    
    # Update package lists
    apt-get update
    
    # Install required packages
    apt-get install -y \
        nginx \
        mysql-server \
        redis-server \
        supervisor \
        certbot \
        python3-certbot-nginx \
        imagemagick \
        jpegoptim \
        optipng \
        pngquant \
        gifsicle \
        webp \
        unzip \
        curl \
        git
    
    log "✅ System dependencies installed"
}

# Setup application
setup_application() {
    log "Setting up application..."
    
    # Create deployment directory
    mkdir -p $DEPLOY_PATH
    
    # Clone or update repository
    if [ -d "$DEPLOY_PATH/.git" ]; then
        cd $DEPLOY_PATH
        git pull origin main
    else
        git clone https://github.com/your-username/osdan.git $DEPLOY_PATH
        cd $DEPLOY_PATH
    fi
    
    # Set proper ownership
    chown -R $DEPLOY_USER:$DEPLOY_USER $DEPLOY_PATH
    
    log "✅ Application code deployed"
}

# Install PHP dependencies
install_php_dependencies() {
    log "Installing PHP dependencies..."
    
    cd $DEPLOY_PATH
    
    # Install Composer dependencies for production
    sudo -u $DEPLOY_USER composer install \
        --no-dev \
        --optimize-autoloader \
        --no-interaction \
        --prefer-dist
    
    log "✅ PHP dependencies installed"
}

# Install Node.js dependencies and build assets
build_assets() {
    log "Installing Node.js dependencies and building assets..."
    
    cd $DEPLOY_PATH
    
    # Install npm dependencies
    sudo -u $DEPLOY_USER npm ci --only=production
    
    # Build production assets
    sudo -u $DEPLOY_USER npm run build
    
    # Clean up node_modules to save space
    rm -rf node_modules
    
    log "✅ Assets built successfully"
}

# Configure environment
configure_environment() {
    log "Configuring environment..."
    
    cd $DEPLOY_PATH
    
    # Copy environment file if it doesn't exist
    if [ ! -f .env ]; then
        cp .env.example .env
        info "Created .env file from example. Please configure it manually."
    fi
    
    # Generate application key if not set
    if ! grep -q "APP_KEY=base64:" .env; then
        sudo -u $DEPLOY_USER php artisan key:generate --force
    fi
    
    # Set proper permissions
    chmod 644 .env
    chown $DEPLOY_USER:$DEPLOY_USER .env
    
    log "✅ Environment configured"
}

# Setup database
setup_database() {
    log "Setting up database..."
    
    cd $DEPLOY_PATH
    
    # Run migrations
    sudo -u $DEPLOY_USER php artisan migrate --force
    
    # Seed database if it's a fresh installation
    if [ "$1" = "--seed" ]; then
        sudo -u $DEPLOY_USER php artisan db:seed --force
    fi
    
    log "✅ Database setup completed"
}

# Optimize application
optimize_application() {
    log "Optimizing application for production..."
    
    cd $DEPLOY_PATH
    
    # Cache configuration
    sudo -u $DEPLOY_USER php artisan config:cache
    
    # Cache routes
    sudo -u $DEPLOY_USER php artisan route:cache
    
    # Cache views
    sudo -u $DEPLOY_USER php artisan view:cache
    
    # Cache events
    sudo -u $DEPLOY_USER php artisan event:cache
    
    # Optimize autoloader
    sudo -u $DEPLOY_USER composer dump-autoload --optimize
    
    # Clear any existing caches
    sudo -u $DEPLOY_USER php artisan cache:clear
    sudo -u $DEPLOY_USER php artisan config:clear
    
    # Warm up application cache
    sudo -u $DEPLOY_USER php artisan cache:warmup || true
    
    log "✅ Application optimized"
}

# Setup file permissions
setup_permissions() {
    log "Setting up file permissions..."
    
    cd $DEPLOY_PATH
    
    # Set ownership
    chown -R $DEPLOY_USER:$DEPLOY_USER .
    
    # Set directory permissions
    find . -type d -exec chmod 755 {} \;
    
    # Set file permissions
    find . -type f -exec chmod 644 {} \;
    
    # Make storage and cache writable
    chmod -R 775 storage bootstrap/cache
    chown -R $DEPLOY_USER:www-data storage bootstrap/cache
    
    # Make artisan executable
    chmod +x artisan
    
    log "✅ File permissions configured"
}

# Configure web server
configure_nginx() {
    log "Configuring Nginx..."
    
    # Create Nginx configuration
    cat > /etc/nginx/sites-available/osdan << EOF
server {
    listen 80;
    listen [::]:80;
    server_name osdan.gh www.osdan.gh;
    root $DEPLOY_PATH/public;
    
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    
    index index.php;
    
    charset utf-8;
    
    # Security headers
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.paystack.co; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.paystack.co;";
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }
    
    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }
    
    error_page 404 /index.php;
    
    location ~ \.php\$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }
    
    location ~ /\.(?!well-known).* {
        deny all;
    }
    
    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|gz|zip|mp4|mov|avi|wmv|flv|swf)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files \$uri =404;
    }
}
EOF
    
    # Enable site
    ln -sf /etc/nginx/sites-available/osdan /etc/nginx/sites-enabled/
    
    # Remove default site
    rm -f /etc/nginx/sites-enabled/default
    
    # Test configuration
    nginx -t
    
    # Restart nginx
    systemctl restart nginx
    systemctl enable nginx
    
    log "✅ Nginx configured"
}

# Setup SSL certificate
setup_ssl() {
    log "Setting up SSL certificate..."
    
    # Obtain SSL certificate
    certbot --nginx -d osdan.gh -d www.osdan.gh --non-interactive --agree-tos --email <EMAIL> || warning "SSL setup failed, continuing without SSL"
    
    log "✅ SSL certificate configured"
}

# Setup queue worker
setup_queue_worker() {
    log "Setting up queue worker..."
    
    # Create supervisor configuration for queue worker
    cat > /etc/supervisor/conf.d/osdan-worker.conf << EOF
[program:osdan-worker]
process_name=%(program_name)s_%(process_num)02d
command=php $DEPLOY_PATH/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=$DEPLOY_USER
numprocs=2
redirect_stderr=true
stdout_logfile=$DEPLOY_PATH/storage/logs/worker.log
stopwaitsecs=3600
EOF
    
    # Update supervisor
    supervisorctl reread
    supervisorctl update
    supervisorctl start osdan-worker:*
    
    log "✅ Queue worker configured"
}

# Setup cron jobs
setup_cron() {
    log "Setting up cron jobs..."
    
    # Add Laravel scheduler to crontab
    (crontab -u $DEPLOY_USER -l 2>/dev/null; echo "* * * * * cd $DEPLOY_PATH && php artisan schedule:run >> /dev/null 2>&1") | crontab -u $DEPLOY_USER -
    
    log "✅ Cron jobs configured"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check if application is responding
    if curl -f http://localhost > /dev/null 2>&1; then
        log "✅ Application is responding"
    else
        error "❌ Application health check failed"
    fi
    
    # Check queue worker
    if supervisorctl status osdan-worker:* | grep -q RUNNING; then
        log "✅ Queue worker is running"
    else
        warning "⚠️ Queue worker is not running"
    fi
    
    # Check services
    systemctl is-active --quiet nginx && log "✅ Nginx is running" || warning "⚠️ Nginx is not running"
    systemctl is-active --quiet mysql && log "✅ MySQL is running" || warning "⚠️ MySQL is not running"
    systemctl is-active --quiet redis && log "✅ Redis is running" || warning "⚠️ Redis is not running"
}

# Main deployment function
main() {
    log "🚀 Starting $APP_NAME deployment..."
    
    check_permissions
    pre_deployment_checks
    create_backup
    install_system_dependencies
    setup_application
    install_php_dependencies
    build_assets
    configure_environment
    setup_database $1
    optimize_application
    setup_permissions
    configure_nginx
    setup_ssl
    setup_queue_worker
    setup_cron
    health_check
    
    log "🎉 Deployment completed successfully!"
    log "Your application should now be available at: https://osdan.gh"
    log ""
    log "Next steps:"
    log "1. Configure your .env file with production settings"
    log "2. Set up your Paystack API keys"
    log "3. Configure your email settings"
    log "4. Set up monitoring and backups"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --seed)
            SEED_DATABASE="--seed"
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --seed    Seed the database after migration"
            echo "  --help    Show this help message"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            ;;
    esac
done

# Run main deployment
main $SEED_DATABASE 