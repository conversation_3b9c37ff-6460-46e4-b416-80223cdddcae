# Cloudflare R2 Photo Storage - Usage Examples

This document provides practical examples of how to use the new Cloudflare R2 photo storage system in your Laravel hostel management application.

## Backend Usage Examples

### 1. Creating a Hostel with Photos

```php
// In your controller
use App\Services\PhotoUploadService;

public function store(Request $request, PhotoUploadService $photoService)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'photos' => 'nullable|array|max:10',
        'photos.*' => 'image|mimes:jpeg,jpg,png,webp|max:51200', // 50MB
    ]);

    $data = $request->only(['name', 'description', 'address']);

    // Upload photos to R2 with multiple sizes
    if ($request->hasFile('photos')) {
        $uploadedPhotos = $photoService->uploadPhotos(
            $request->file('photos'), 
            'hostels'
        );
        $data['photos'] = $uploadedPhotos;
    }

    $hostel = Hostel::create($data);
    
    return response()->json($hostel);
}
```

### 2. Using Hostel Model Helper Methods

```php
// Get a hostel
$hostel = Hostel::find(1);

// Get thumbnail URL for listing pages
$thumbnailUrl = $hostel->thumbnail;

// Get featured image URL for detail pages
$featuredImageUrl = $hostel->featured_image;

// Get all photos in medium size
$mediumPhotos = $hostel->getOptimizedPhotos('medium');

// Get specific photo URL
$firstPhotoLarge = $hostel->getPhotoUrl(0, 'large');

// Check if hostel has photos
if ($hostel->hasPhotos()) {
    echo "This hostel has {$hostel->photo_count} photos";
}
```

### 3. API Response Example

```php
// In your API controller
public function show(Hostel $hostel)
{
    return response()->json([
        'id' => $hostel->id,
        'name' => $hostel->name,
        'thumbnail' => $hostel->thumbnail,
        'featured_image' => $hostel->featured_image,
        'photo_count' => $hostel->photo_count,
        'photos' => $hostel->photos, // Full photo data for frontend
    ]);
}
```

## Frontend Usage Examples

### 1. Basic OptimizedImage Usage

```tsx
import OptimizedImage from '@/components/OptimizedImage';

// Simple usage
<OptimizedImage 
    photo={hostel.photos[0]} 
    alt="Hostel photo"
    size="medium"
    className="w-full h-48 object-cover rounded-lg"
/>
```

### 2. Hostel Listing Card

```tsx
function HostelCard({ hostel }) {
    return (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <OptimizedImage
                photo={hostel.photos[0]}
                alt={hostel.name}
                size="thumbnail"
                className="w-full h-48 object-cover"
                loading="lazy"
            />
            <div className="p-4">
                <h3 className="text-lg font-semibold">{hostel.name}</h3>
                <p className="text-gray-600">{hostel.city}</p>
            </div>
        </div>
    );
}
```

### 3. Photo Gallery

```tsx
function PhotoGallery({ hostel }) {
    const [selectedSize, setSelectedSize] = useState('medium');
    
    return (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {hostel.photos.map((photo, index) => (
                <OptimizedImage
                    key={photo.id}
                    photo={photo}
                    alt={`${hostel.name} photo ${index + 1}`}
                    size={selectedSize}
                    className="w-full h-32 object-cover rounded-lg cursor-pointer hover:opacity-80"
                    onClick={() => openLightbox(photo)}
                />
            ))}
        </div>
    );
}
```

### 4. Responsive Image Sizes

```tsx
function ResponsiveHostelImage({ hostel }) {
    return (
        <div className="w-full">
            {/* Mobile: thumbnail */}
            <div className="block sm:hidden">
                <OptimizedImage
                    photo={hostel.photos[0]}
                    alt={hostel.name}
                    size="thumbnail"
                    className="w-full h-48 object-cover"
                />
            </div>
            
            {/* Tablet: medium */}
            <div className="hidden sm:block lg:hidden">
                <OptimizedImage
                    photo={hostel.photos[0]}
                    alt={hostel.name}
                    size="medium"
                    className="w-full h-64 object-cover"
                />
            </div>
            
            {/* Desktop: large */}
            <div className="hidden lg:block">
                <OptimizedImage
                    photo={hostel.photos[0]}
                    alt={hostel.name}
                    size="large"
                    className="w-full h-80 object-cover"
                />
            </div>
        </div>
    );
}
```

### 5. Error Handling and Loading States

```tsx
function HostelImage({ hostel }) {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);
    
    return (
        <div className="relative">
            <OptimizedImage
                photo={hostel.photos[0]}
                alt={hostel.name}
                size="medium"
                className="w-full h-48 object-cover"
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
            />
            
            {!imageLoaded && !imageError && (
                <div className="absolute inset-0 bg-gray-200 animate-pulse" />
            )}
            
            {imageError && (
                <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                    <span className="text-gray-500">Image unavailable</span>
                </div>
            )}
        </div>
    );
}
```

## Migration Examples

### 1. Migrate Existing Photos

```bash
# Dry run to see what would be migrated
php artisan photos:migrate-to-r2 --dry-run

# Actually migrate the photos
php artisan photos:migrate-to-r2
```

### 2. Check Migration Status

```php
// Check if a hostel has been migrated
$hostel = Hostel::find(1);

if (is_string($hostel->photos[0] ?? null)) {
    echo "This hostel still uses old photo format";
} else {
    echo "This hostel uses new R2 photo format";
}
```

## Configuration Examples

### 1. Environment Configuration

```env
# .env file
FILESYSTEM_DISK=r2

# Cloudflare R2 Configuration
R2_ACCESS_KEY_ID=your_access_key_here
R2_SECRET_ACCESS_KEY=your_secret_key_here
R2_DEFAULT_REGION=auto
R2_BUCKET=your-bucket-name
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_URL=https://cdn.yourdomain.com
```

### 2. Custom Photo Sizes

```php
// If you need to customize photo sizes, modify PhotoUploadService
private array $imageSizes = [
    'original' => null,
    'xlarge' => ['width' => 1920, 'height' => 1280],
    'large' => ['width' => 1200, 'height' => 800],
    'medium' => ['width' => 600, 'height' => 400],
    'small' => ['width' => 400, 'height' => 267],
    'thumbnail' => ['width' => 300, 'height' => 200],
    'tiny' => ['width' => 150, 'height' => 100],
];
```

## Performance Tips

### 1. Lazy Loading

```tsx
// Use lazy loading for images below the fold
<OptimizedImage
    photo={photo}
    alt="Hostel photo"
    size="medium"
    loading="lazy"
    className="w-full h-48 object-cover"
/>
```

### 2. Preload Critical Images

```tsx
// Preload hero images
<OptimizedImage
    photo={hostel.photos[0]}
    alt={hostel.name}
    size="large"
    loading="eager"
    className="w-full h-96 object-cover"
/>
```

### 3. Size Selection Strategy

- **Thumbnail (300x200)**: Listing cards, small previews
- **Medium (600x400)**: Detail pages, galleries
- **Large (1200x800)**: Full-screen views, hero images
- **Original**: Downloads, maximum quality needs

## Troubleshooting Examples

### 1. Test R2 Connection

```php
// In tinker or a test route
use Illuminate\Support\Facades\Storage;

// Test basic connection
Storage::disk('r2')->put('test.txt', 'Hello R2');
echo Storage::disk('r2')->get('test.txt');

// List files
$files = Storage::disk('r2')->files('hostels');
dd($files);
```

### 2. Debug Photo Upload

```php
// Add logging to see what's happening
use Illuminate\Support\Facades\Log;

try {
    $photoData = $photoService->uploadSinglePhoto($file, 'hostels');
    Log::info('Photo uploaded successfully', ['photo_data' => $photoData]);
} catch (\Exception $e) {
    Log::error('Photo upload failed', [
        'error' => $e->getMessage(),
        'file' => $file->getClientOriginalName()
    ]);
}
```

This system provides a robust, scalable solution for handling photos with automatic optimization and CDN delivery through Cloudflare R2!