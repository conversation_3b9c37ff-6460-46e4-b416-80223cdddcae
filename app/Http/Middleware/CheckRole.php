<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        $user = auth()->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Convert roles to array if single role passed
        if (!is_array($roles)) {
            $roles = [$roles];
        }

        // Check if user has any of the required roles
        if (in_array($user->role, $roles)) {
            return $next($request);
        }

        // Redirect based on user role if access denied
        switch ($user->role) {
            case 'student':
                return redirect()->route('dashboard')->with('error', 'Access denied. You do not have permission to access this area.');
            case 'hostel_admin':
                return redirect()->route('admin.dashboard')->with('error', 'Access denied. You do not have permission to access this area.');
            case 'super_admin':
                return redirect()->route('dashboard')->with('error', 'Access denied. You do not have permission to access this area.');
            default:
                return redirect()->route('dashboard')->with('error', 'Access denied.');
        }
    }
}
