<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateHostelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && 
               auth()->user()->isHostelAdmin() &&
               $this->route('hostel')->owner_id === auth()->id();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string|min:50',
            'address' => 'sometimes|required|string|max:500',
            'city' => 'sometimes|required|string|max:100',
            'state' => 'sometimes|required|string|max:100',
            'country' => 'sometimes|required|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string|max:100',
            'photos' => 'nullable|array|max:10',
            'photos.*' => 'image|mimes:jpeg,png,jpg,webp|max:51200', // 50MB for R2
            'check_in_time' => 'sometimes|required|string|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
            'check_out_time' => 'sometimes|required|string|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
            'house_rules' => 'nullable|string|max:2000',
            'cancellation_policy' => 'nullable|string|max:2000',
            'is_active' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The hostel name is required.',
            'description.required' => 'Please provide a description for your hostel.',
            'description.min' => 'The description must be at least 50 characters long.',
            'check_in_time.regex' => 'Check-in time must be in HH:MM format.',
            'check_out_time.regex' => 'Check-out time must be in HH:MM format.',
            'photos.*.image' => 'Each photo must be an image file.',
            'photos.*.max' => 'Each photo must be less than 50MB.',
            'photos.max' => 'You can upload a maximum of 10 photos.',
        ];
    }
}
