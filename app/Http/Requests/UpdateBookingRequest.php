<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBookingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();
        $booking = $this->route('booking');

        // Super admin can always update
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Students can update their own bookings (with restrictions)
        if ($user->isStudent() && $booking->user_id === $user->id) {
            return true;
        }

        // Hostel admins can update bookings for their hostels
        if ($user->isHostelAdmin() && $booking->hostel->owner_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $booking = $this->route('booking');
        
        return [
            'semester' => 'sometimes|in:first,second,full_year',
            'academic_year' => 'sometimes|string|regex:/^\d{4}\/\d{4}$/|max:9',
            'students' => 'sometimes|integer|min:1|max:10',
            'room_id' => 'sometimes|nullable|uuid|exists:rooms,id',
            'special_requests' => 'sometimes|nullable|string|max:1000',
            'status' => 'sometimes|in:confirmed,moved_in,active,moved_out,cancelled',
            'cancellation_reason' => 'sometimes|nullable|string|max:500',
            'payment_status' => 'sometimes|in:pending,partially_paid,paid,refunded,failed',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $user = auth()->user();
            $booking = $this->route('booking');
            $newStatus = $this->input('status');

            // Status transition rules
            if ($newStatus && $newStatus !== $booking->status) {
                $allowedTransitions = $this->getAllowedStatusTransitions($booking->status, $user);
                
                if (!in_array($newStatus, $allowedTransitions)) {
                    $validator->errors()->add('status', 'Invalid status transition.');
                }
            }

            // Only hostel admins and super admins can confirm bookings
            if ($newStatus === 'confirmed' && !$user->isHostelAdmin() && !$user->isSuperAdmin()) {
                $validator->errors()->add('status', 'Only hostel administrators can confirm bookings.');
            }

            // Only hostel admins and super admins can move students in/out
            if (in_array($newStatus, ['moved_in', 'active', 'moved_out']) && 
                !$user->isHostelAdmin() && !$user->isSuperAdmin()) {
                $validator->errors()->add('status', 'Only hostel administrators can manage student move-in/out.');
            }

            // Payment amount validation
            if ($this->filled('amount_paid')) {
                $maxAmount = $booking->total_amount;
                if ($this->amount_paid > $maxAmount) {
                    $validator->errors()->add('amount_paid', "Amount cannot exceed total booking amount of ₵{$maxAmount}");
                }
            }
        });
    }

    /**
     * Get allowed status transitions based on current status and user role
     */
    private function getAllowedStatusTransitions(string $currentStatus, $user): array
    {
        $transitions = [
            'pending' => ['confirmed', 'cancelled'],
            'confirmed' => ['moved_in', 'cancelled'],
            'moved_in' => ['active', 'moved_out'],
            'active' => ['moved_out'],
            'moved_out' => [], // Final state
            'cancelled' => [], // Final state
        ];

        $allowed = $transitions[$currentStatus] ?? [];

        // Students can only cancel their own bookings
        if ($user->isStudent()) {
            return array_intersect($allowed, ['cancelled']);
        }

        // Hostel admins and super admins can make all allowed transitions
        return $allowed;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'semester.in' => 'Please select a valid semester.',
            'academic_year.regex' => 'Academic year must be in the format YYYY/YYYY (e.g., 2024/2025).',
            'students.min' => 'At least 1 student is required.',
            'students.max' => 'Maximum 10 students allowed per booking.',
            'room_id.exists' => 'Selected room is not valid.',
            'special_requests.max' => 'Special requests cannot exceed 1000 characters.',
            'status.in' => 'Invalid booking status.',
            'cancellation_reason.max' => 'Cancellation reason cannot exceed 500 characters.',
            'payment_status.in' => 'Invalid payment status.',
        ];
    }
}
