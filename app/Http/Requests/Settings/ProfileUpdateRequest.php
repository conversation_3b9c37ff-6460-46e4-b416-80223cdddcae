<?php

namespace App\Http\Requests\Settings;

use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->user();
        $isStudent = $user->role === 'student';

        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($user->id),
            ],
            'phone' => ['nullable', 'string', 'max:20'],
            'date_of_birth' => ['nullable', 'date', 'before:today'],
            'gender' => ['nullable', 'in:male,female,other,prefer_not_to_say'],
            'bio' => ['nullable', 'string', 'max:500'],
            'emergency_contact_name' => ['nullable', 'string', 'max:255'],
            'emergency_contact_phone' => ['nullable', 'string', 'max:20'],
        ];

        // Add student-specific validation rules
        if ($isStudent) {
            $rules = array_merge($rules, [
                'university' => ['nullable', 'string', 'max:255'],
                'student_id' => [
                    'nullable', 
                    'string', 
                    'max:50',
                    Rule::unique(User::class)->ignore($user->id),
                ],
                'program' => ['nullable', 'string', 'max:255'],
                'year_of_study' => ['nullable', 'string', 'max:50'],
            ]);
        }

        return $rules;
    }
}
