<?php

namespace App\Http\Requests;

use App\Models\Room;
use App\Models\RoomType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreBookingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'hostel_id' => 'required|uuid|exists:hostels,id',
            'room_type_id' => [
                'required',
                'uuid',
                'exists:room_types,id',
                Rule::exists('room_types', 'id')->where(function ($query) {
                    $query->where('hostel_id', $this->hostel_id)
                          ->where('is_active', true);
                }),
            ],
            'semester' => 'required|in:first,second,full_year',
            'academic_year' => ['required', 'regex:/^\d{4}\/\d{4}$/'],
            'students' => 'required|integer|min:1|max:1',
            'payment_type' => 'required|in:full,deposit',
            'special_requests' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check for overlapping bookings for the same user (based on semester and academic year)
            if ($this->academic_year && $this->semester) {
                $existingBooking = \App\Models\Booking::where('user_id', auth()->id())
                    ->where('status', '!=', 'cancelled')
                    ->where('academic_year', $this->academic_year)
                    ->where(function ($query) {
                        if ($this->semester === 'full_year') {
                            // Full year conflicts with any semester in same academic year
                            $query->whereIn('semester', ['first', 'second', 'full_year']);
                        } else {
                            // Specific semester conflicts with same semester or full year
                            $query->whereIn('semester', [$this->semester, 'full_year']);
                        }
                    })
                    ->exists();

                if ($existingBooking) {
                    $validator->errors()->add('semester', 
                        'You already have a booking for this semester/academic year.'
                    );
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'hostel_id.required' => 'Please select a hostel.',
            'hostel_id.exists' => 'The selected hostel is not valid.',
            'room_type_id.required' => 'Please select a room type.',
            'room_type_id.exists' => 'The selected room type is not available.',

            'semester.required' => 'Please select a semester.',
            'semester.in' => 'Please select a valid semester option.',
            'academic_year.required' => 'Please specify the academic year.',
            'academic_year.regex' => 'Academic year must be in format YYYY/YYYY (e.g., 2024/2025).',
            'students.required' => 'Please specify the number of students.',
            'students.min' => 'At least 1 student is required.',
            'students.max' => 'Only 1 student allowed per booking.',
            'payment_type.required' => 'Please select a payment option.',
            'payment_type.in' => 'Please select a valid payment option.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'user_id' => auth()->id(),
        ]);
    }
}
