<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        $genders = [
            ['value' => 'male', 'label' => 'Male'],
            ['value' => 'female', 'label' => 'Female'],
        ];
        $yearsOfStudy = [
            ['value' => '1', 'label' => '1st Year'],
            ['value' => '2', 'label' => '2nd Year'],
            ['value' => '3', 'label' => '3rd Year'],
            ['value' => '4', 'label' => '4th Year'],
            ['value' => '5', 'label' => '5th Year'],
            ['value' => '6', 'label' => '6th Year'],
            ['value' => 'graduate', 'label' => 'Graduate Student'],
            ['value' => 'other', 'label' => 'Other'],
        ];
        $universities = \App\Models\University::orderBy('name')->get(['id', 'name']);
        return Inertia::render('auth/register', [
            'genders' => $genders,
            'years_of_study' => $yearsOfStudy,
            'universities' => $universities,
        ]);
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        // Base validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => 'required|in:student,hostel_admin',
            'phone' => 'required|string|max:20',
            'gender' => 'required|in:male,female,other,prefer_not_to_say',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
        ];

        // Add student-specific validation rules
        if ($request->role === 'student') {
            $rules = array_merge($rules, [
                'university' => 'required|string|max:255',
                'student_id' => 'required|string|max:50|unique:users,student_id',
                'year_of_study' => 'required|string|max:50',
            ]);
        }

        $validated = $request->validate($rules);

        // Prepare user data
        $userData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => $validated['role'],
            'phone' => $validated['phone'],
            'gender' => $validated['gender'],
            'emergency_contact_name' => $validated['emergency_contact_name'],
            'emergency_contact_phone' => $validated['emergency_contact_phone'],
        ];

        // Add student-specific fields if role is student
        if ($validated['role'] === 'student') {
            $userData = array_merge($userData, [
                'university' => $validated['university'],
                'student_id' => $validated['student_id'],
                'year_of_study' => $validated['year_of_study'],
            ]);
        }

        $user = User::create($userData);

        event(new Registered($user));

        Auth::login($user);

        // Redirect based on role
        $redirectRoute = match($user->role) {
            'hostel_admin' => 'admin.dashboard',
            'student' => 'dashboard',
            default => 'dashboard'
        };

        return redirect()->intended(route($redirectRoute, absolute: false));
    }
}
