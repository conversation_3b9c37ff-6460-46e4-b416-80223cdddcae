<?php

namespace App\Http\Controllers\HostelAdmin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Hostel;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the hostel admin dashboard.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Ensure user is a hostel admin
        if (!$user->isHostelAdmin()) {
            abort(403, 'Access denied. Hostel admin privileges required.');
        }

        // Get hostels managed by this admin
        $hostels = $user->hostels()->with(['roomTypes', 'bookings'])->get();
        
        // Get current academic year and semester
        $currentAcademicYear = $this->getCurrentAcademicYear();
        $currentSemester = $this->getCurrentSemester();
        
        // Calculate dashboard statistics
        $stats = $this->calculateDashboardStats($hostels, $currentAcademicYear, $currentSemester);
        
        // Get recent bookings
        $recentBookings = $this->getRecentBookings($hostels, 10);
        
        // Get upcoming move-ins
        $upcomingMoveIns = $this->getUpcomingMoveIns($hostels, 7);
        
        // Get bed occupancy data
        $bedOccupancy = $this->getBedOccupancyData($hostels);
        
        return Inertia::render('HostelAdmin/Dashboard', [
            'stats' => $stats,
            'hostels' => $hostels,
            'recentBookings' => $recentBookings,
            'upcomingMoveIns' => $upcomingMoveIns,
            'bedOccupancy' => $bedOccupancy,
            'currentAcademicYear' => $currentAcademicYear,
            'currentSemester' => $currentSemester,
        ]);
    }

    /**
     * Calculate dashboard statistics
     */
    private function calculateDashboardStats($hostels, $academicYear, $semester)
    {
        $hostelIds = $hostels->pluck('id');
        
        // Total beds across all hostels
        $totalBeds = $hostels->sum(function ($hostel) {
            return $hostel->roomTypes->sum(function ($roomType) {
                return $roomType->rooms->sum(function ($room) {
                    return $room->beds()->count();
                });
            });
        });
        
        // Occupied beds
        $occupiedBeds = $hostels->sum(function ($hostel) {
            return $hostel->roomTypes->sum(function ($roomType) {
                return $roomType->rooms->sum(function ($room) {
                    return $room->beds()->where('status', 'occupied')->count();
                });
            });
        });
        
        // Current semester bookings
        $currentBookings = Booking::whereIn('hostel_id', $hostelIds)
            ->where('academic_year', $academicYear)
            ->where('semester', $semester)
            ->whereIn('status', ['confirmed', 'moved_in', 'active'])
            ->count();
        
        // Pending applications
        $pendingBookings = Booking::whereIn('hostel_id', $hostelIds)
            ->where('status', 'pending')
            ->count();
        
        // Revenue for current semester
        $semesterRevenue = Booking::whereIn('hostel_id', $hostelIds)
            ->where('academic_year', $academicYear)
            ->where('semester', $semester)
            ->where('payment_status', 'paid')
            ->sum('total_amount');
        
        return [
            'total_beds' => $totalBeds,
            'occupied_beds' => $occupiedBeds,
            'occupancy_rate' => $totalBeds > 0 ? round(($occupiedBeds / $totalBeds) * 100, 1) : 0,
            'current_bookings' => $currentBookings,
            'pending_applications' => $pendingBookings,
            'semester_revenue' => $semesterRevenue,
        ];
    }
    
    /**
     * Get recent bookings
     */
    private function getRecentBookings($hostels, $limit = 10)
    {
        $hostelIds = $hostels->pluck('id');
        
        return Booking::whereIn('hostel_id', $hostelIds)
            ->with(['user', 'hostel', 'roomType', 'room', 'bed'])
            ->latest()
            ->take($limit)
            ->get()
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'booking_reference' => $booking->booking_reference,
                    'student_name' => $booking->user->name,
                    'student_id' => $booking->user->student_id,
                    'hostel_name' => $booking->hostel->name,
                    'room_type' => $booking->roomType->name,
                    'room_number' => $booking->room?->room_number,
                    'bed_number' => $booking->bed?->bed_number,
                    'semester' => $booking->semester,
                    'academic_year' => $booking->academic_year,
                    'status' => $booking->status,
                    'payment_status' => $booking->payment_status,
                    'total_amount' => $booking->total_amount,
                    'created_at' => $booking->created_at,
                ];
            });
    }
    
    /**
     * Get upcoming move-ins (confirmed bookings for current semester)
     */
    private function getUpcomingMoveIns($hostels, $days = 7)
    {
        $hostelIds = $hostels->pluck('id');
        $currentAcademicYear = $this->getCurrentAcademicYear();
        $currentSemester = $this->getCurrentSemester();
        
        return Booking::whereIn('hostel_id', $hostelIds)
            ->where('status', 'confirmed')
            ->where('academic_year', $currentAcademicYear)
            ->where('semester', $currentSemester)
            ->with(['user', 'hostel', 'roomType', 'room', 'bed'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'student_name' => $booking->user->name,
                    'student_id' => $booking->user->student_id,
                    'student_phone' => $booking->user->phone,
                    'hostel_name' => $booking->hostel->name,
                    'room_type' => $booking->roomType->name,
                    'room_number' => $booking->room?->room_number,
                    'bed_number' => $booking->bed?->bed_number,
                    'semester' => $booking->semester,
                    'academic_year' => $booking->academic_year,
                ];
            });
    }
    
    /**
     * Get bed occupancy data for charts
     */
    private function getBedOccupancyData($hostels)
    {
        return $hostels->map(function ($hostel) {
            $totalBeds = $hostel->roomTypes->sum(function ($roomType) {
                return $roomType->rooms->sum(function ($room) {
                    return $room->beds()->count();
                });
            });
            
            $occupiedBeds = $hostel->roomTypes->sum(function ($roomType) {
                return $roomType->rooms->sum(function ($room) {
                    return $room->beds()->where('status', 'occupied')->count();
                });
            });
            
            return [
                'hostel_name' => $hostel->name,
                'total_beds' => $totalBeds,
                'occupied_beds' => $occupiedBeds,
                'available_beds' => $totalBeds - $occupiedBeds,
                'occupancy_rate' => $totalBeds > 0 ? round(($occupiedBeds / $totalBeds) * 100, 1) : 0,
            ];
        });
    }
    
    /**
     * Get current academic year
     */
    private function getCurrentAcademicYear()
    {
        $now = Carbon::now();
        $year = $now->year;
        
        // If we're in the first half of the year (Jan-Aug), it's the second semester
        // of the previous academic year
        if ($now->month <= 8) {
            return ($year - 1) . '/' . $year;
        }
        
        // Otherwise, it's the first semester of the current academic year
        return $year . '/' . ($year + 1);
    }
    
    /**
     * Get current semester
     */
    private function getCurrentSemester()
    {
        $now = Carbon::now();
        
        // First semester: September - December
        // Second semester: January - May
        // Vacation: June - August
        
        if ($now->month >= 9 || $now->month <= 12) {
            return 'first';
        } elseif ($now->month >= 1 && $now->month <= 5) {
            return 'second';
        }
        
        return 'vacation'; // June-August
    }
} 