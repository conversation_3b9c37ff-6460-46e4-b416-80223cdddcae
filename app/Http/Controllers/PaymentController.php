<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Services\PaystackService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Inertia\Inertia;

class PaymentController extends Controller
{
    use AuthorizesRequests;
    
    private PaystackService $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Initialize payment for a booking
     */
    public function initialize(Booking $booking)
    {
        $this->authorize('view', $booking);

        if ($booking->payment_status === 'paid') {
            return redirect()->route('bookings.show', $booking)
                ->with('info', 'This booking has already been paid.');
        }

        if (!in_array($booking->status, ['pending'])) {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'Payment can only be made for pending bookings.');
        }

        try {
            $result = $this->paystackService->initializePayment($booking);

            if (!$result['status']) {
                return redirect()->back()
                    ->with('error', $result['message'] ?? 'Payment initialization failed.');
            }

            // Redirect to Paystack payment page
            return redirect($result['authorization_url']);

        } catch (\Exception $e) {
            Log::error('Payment initialization error', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->with('error', 'An error occurred while initializing payment. Please try again.');
        }
    }

    /**
     * Handle payment callback from Paystack
     */
    public function callback(Request $request)
    {
        $reference = $request->query('reference');
        $trxref = $request->query('trxref');

        if (!$reference && !$trxref) {
            return redirect()->route('dashboard')
                ->with('error', 'Invalid payment reference.');
        }

        // Use reference or trxref
        $paymentReference = $reference ?: $trxref;

        try {
            $verification = $this->paystackService->verifyPayment($paymentReference);

            if (!$verification['status']) {
                return redirect()->route('dashboard')
                    ->with('error', 'Payment verification failed. Please contact support if you were charged.');
            }

            $paymentData = $verification['data'];
            $bookingId = $paymentData['metadata']['booking_id'] ?? null;

            if (!$bookingId) {
                return redirect()->route('dashboard')
                    ->with('error', 'Booking information not found in payment data.');
            }

            $booking = Booking::find($bookingId);

            if (!$booking) {
                return redirect()->route('dashboard')
                    ->with('error', 'Booking not found.');
            }

            // Process the payment
            if ($paymentData['status'] === 'success') {
                return $this->handleSuccessfulPayment($booking, $paymentData);
            } else {
                return $this->handleFailedPayment($booking, $paymentData);
            }

        } catch (\Exception $e) {
            Log::error('Payment callback error', [
                'reference' => $paymentReference,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('dashboard')
                ->with('error', 'An error occurred while processing your payment. Please contact support.');
        }
    }

    /**
     * Handle successful payment
     */
    private function handleSuccessfulPayment(Booking $booking, array $paymentData)
    {
        try {
            DB::transaction(function () use ($booking, $paymentData) {
                $amountPaid = $paymentData['amount'] / 100; // Convert from kobo

                $paymentStatus = $booking->payment_type === 'deposit' && $amountPaid < $booking->total_amount 
                    ? 'partially_paid' 
                    : 'paid';
                
                $bookingStatus = $paymentStatus === 'paid' ? 'confirmed' : 'pending';

                $booking->update([
                    'payment_reference' => $paymentData['reference'],
                    'payment_data' => $paymentData,
                    'amount_paid' => $amountPaid,
                    'payment_status' => $paymentStatus,
                    'status' => $bookingStatus,
                    'amount_due' => max(0, $booking->total_amount - $amountPaid),
                    'confirmed_at' => $paymentStatus === 'paid' ? now() : null,
                ]);

                Log::info('Payment processed successfully in callback', [
                    'booking_id' => $booking->id,
                    'amount_paid' => $amountPaid,
                    'payment_status' => $booking->payment_status,
                ]);
            });

            $message = $booking->payment_type === 'deposit' 
                ? 'Deposit payment successful! Your booking is now confirmed.'
                : 'Payment successful! Your booking is now confirmed.';

            return redirect()->route('bookings.show', $booking)
                ->with('success', $message);

        } catch (\Exception $e) {
            Log::error('Error handling successful payment in callback', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('bookings.show', $booking)
                ->with('warning', 'Payment was successful, but there was an issue updating your booking. Please contact support.');
        }
    }

    /**
     * Handle failed payment
     */
    private function handleFailedPayment(Booking $booking, array $paymentData)
    {
        $booking->update([
            'payment_reference' => $paymentData['reference'],
            'payment_data' => $paymentData,
            'payment_status' => 'failed',
        ]);

        $reason = $paymentData['gateway_response'] ?? 'Payment was not successful';

        return redirect()->route('bookings.payment', $booking)
            ->with('error', "Payment failed: {$reason}. Please try again.");
    }

    /**
     * Handle Paystack webhooks
     */
    public function webhook(Request $request)
    {
        // Verify webhook signature
        $signature = $request->header('x-paystack-signature');
        $payload = $request->getContent();

        if (!$this->paystackService->verifyWebhookSignature($payload, $signature)) {
            Log::warning('Invalid webhook signature', [
                'signature' => $signature,
                'ip' => $request->ip(),
            ]);
            return response('Invalid signature', 400);
        }

        $data = json_decode($payload, true);

        if (!$data) {
            Log::error('Invalid webhook payload', ['payload' => $payload]);
            return response('Invalid payload', 400);
        }

        Log::info('Paystack webhook received', [
            'event' => $data['event'] ?? 'unknown',
            'reference' => $data['data']['reference'] ?? 'unknown',
        ]);

        // Process the webhook
        $processed = $this->paystackService->processWebhook($data);

        return response($processed ? 'OK' : 'Error', $processed ? 200 : 500);
    }

    /**
     * Retry failed payment
     */
    public function retry(Booking $booking)
    {
        $this->authorize('view', $booking);

        if ($booking->payment_status === 'paid') {
            return redirect()->route('bookings.show', $booking)
                ->with('info', 'This booking has already been paid.');
        }

        // Reset payment status to allow retry
        $booking->update([
            'payment_status' => 'pending',
            'payment_reference' => null,
            'payment_data' => null,
        ]);

        return redirect()->route('payments.initialize', $booking);
    }

    /**
     * Get payment status
     */
    public function status(Booking $booking)
    {
        $this->authorize('view', $booking);

        return response()->json([
            'booking_id' => $booking->id,
            'booking_reference' => $booking->booking_reference,
            'payment_status' => $booking->payment_status,
            'amount_paid' => $booking->amount_paid,
            'amount_due' => $booking->amount_due,
            'total_amount' => $booking->total_amount,
            'payment_reference' => $booking->payment_reference,
        ]);
    }

    /**
     * Process refund for cancelled booking
     */
    public function refund(Request $request, Booking $booking)
    {
        $this->authorize('update', $booking);

        if ($booking->payment_status !== 'paid' && $booking->payment_status !== 'partially_paid') {
            return redirect()->back()
                ->with('error', 'Only paid bookings can be refunded.');
        }

        if (!$booking->payment_reference) {
            return redirect()->back()
                ->with('error', 'No payment reference found for this booking.');
        }

        $request->validate([
            'refund_amount' => 'nullable|numeric|min:0|max:' . $booking->amount_paid,
            'reason' => 'required|string|max:500',
        ]);

        try {
            $refundAmount = $request->refund_amount ?: $booking->amount_paid;
            
            $result = $this->paystackService->refundPayment(
                $booking->payment_reference,
                $refundAmount
            );

            if (!$result['status']) {
                return redirect()->back()
                    ->with('error', $result['message'] ?? 'Refund failed.');
            }

            // Update booking status
            $booking->update([
                'payment_status' => 'refunded',
                'amount_paid' => $booking->amount_paid - $refundAmount,
                'amount_due' => $booking->total_amount - ($booking->amount_paid - $refundAmount),
            ]);

            Log::info('Refund processed', [
                'booking_id' => $booking->id,
                'refund_amount' => $refundAmount,
                'reason' => $request->reason,
            ]);

            return redirect()->route('bookings.show', $booking)
                ->with('success', "Refund of ₦{$refundAmount} has been processed successfully.");

        } catch (\Exception $e) {
            Log::error('Refund processing error', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->with('error', 'An error occurred while processing the refund. Please try again.');
        }
    }

    /**
     * Get Paystack public key for frontend
     */
    public function publicKey()
    {
        try {
            $publicKey = $this->paystackService->getPublicKey();
            
            if (empty($publicKey)) {
                Log::error('Paystack public key is empty or not configured');
                return response()->json([
                    'error' => 'Payment system configuration error. Please contact support.'
                ], 500);
            }
            
            return response()->json([
                'public_key' => $publicKey,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting Paystack public key', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'error' => 'Payment system unavailable. Please try again later.'
            ], 500);
        }
    }
}
