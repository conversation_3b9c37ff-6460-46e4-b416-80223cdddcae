<?php

namespace App\Http\Controllers;

use App\Helpers\ImageHelper;
use App\Models\Hostel;
use App\Models\RoomType;
use App\Models\University;
use App\Models\AcademicYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class SearchController extends Controller
{
    /**
     * Search hostels with filters
     */
    public function search(Request $request)
    {
        $request->validate([
            'location' => 'nullable|string|max:255',
            'check_in' => 'nullable|date|after_or_equal:today',
            'check_out' => 'nullable|date|after:check_in',
            'guests' => 'nullable|integer|min:1|max:20',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'room_type' => 'nullable|in:dorm,private,family',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string',
            'sort_by' => 'nullable|in:price_low,price_high,rating,distance,newest',
            'per_page' => 'nullable|integer|min:6|max:24',
        ]);

        $query = Hostel::active()
            ->verified()
            ->with(['owner', 'activeRoomTypes']);

        // Location-based search
        if ($request->filled('location')) {
            $location = $request->location;
            $query->where(function ($q) use ($location) {
                $q->where('city', 'LIKE', "%{$location}%")
                  ->orWhere('state', 'LIKE', "%{$location}%")
                  ->orWhere('name', 'LIKE', "%{$location}%")
                  ->orWhere('address', 'LIKE', "%{$location}%");
            });
        }

        // Guest capacity filter
        if ($request->filled('guests')) {
            $guests = $request->guests;
            $query->whereHas('activeRoomTypes', function ($q) use ($guests) {
                $q->where('capacity', '>=', $guests);
            });
        }

        // Room type filter
        if ($request->filled('room_type')) {
            $query->whereHas('activeRoomTypes', function ($q) use ($request) {
                $q->where('category', $request->room_type);
            });
        }

        // Price range filter
        if ($request->filled('min_price') || $request->filled('max_price')) {
            $query->whereHas('activeRoomTypes', function ($q) use ($request) {
                if ($request->filled('min_price')) {
                    $q->where('price_per_night', '>=', $request->min_price);
                }
                if ($request->filled('max_price')) {
                    $q->where('price_per_night', '<=', $request->max_price);
                }
            });
        }

        // Amenities filter
        if ($request->filled('amenities')) {
            foreach ($request->amenities as $amenity) {
                $query->whereJsonContains('amenities', $amenity);
            }
        }

        // Date-based availability filter
        if ($request->filled('check_in') && $request->filled('check_out')) {
            $checkIn = $request->check_in;
            $checkOut = $request->check_out;
            
            $query->whereHas('activeRoomTypes', function ($q) use ($checkIn, $checkOut) {
                $q->whereHas('rooms', function ($roomQuery) use ($checkIn, $checkOut) {
                    $roomQuery->where('status', 'available')
                             ->whereDoesntHave('bookings', function ($bookingQuery) use ($checkIn, $checkOut) {
                                 $bookingQuery->whereIn('status', ['confirmed', 'checked_in'])
                                             ->where(function ($dateQuery) use ($checkIn, $checkOut) {
                                                 $dateQuery->whereBetween('check_in_date', [$checkIn, $checkOut])
                                                          ->orWhereBetween('check_out_date', [$checkIn, $checkOut])
                                                          ->orWhere(function ($overlapQuery) use ($checkIn, $checkOut) {
                                                              $overlapQuery->where('check_in_date', '<=', $checkIn)
                                                                          ->where('check_out_date', '>=', $checkOut);
                                                          });
                                             });
                             });
                });
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'newest');
        switch ($sortBy) {
            case 'price_low':
                $query->join('room_types', 'hostels.id', '=', 'room_types.hostel_id')
                      ->where('room_types.is_active', true)
                      ->select('hostels.*', DB::raw('MIN(room_types.price_per_night) as min_price'))
                      ->groupBy('hostels.id')
                      ->orderBy('min_price', 'asc');
                break;
            case 'price_high':
                $query->join('room_types', 'hostels.id', '=', 'room_types.hostel_id')
                      ->where('room_types.is_active', true)
                      ->select('hostels.*', DB::raw('MIN(room_types.price_per_night) as min_price'))
                      ->groupBy('hostels.id')
                      ->orderBy('min_price', 'desc');
                break;
            case 'rating':
                $query->orderBy('average_rating', 'desc')
                      ->orderBy('total_reviews', 'desc');
                break;
            case 'distance':
                // TODO: Implement distance-based sorting when coordinates are provided
                $query->orderBy('city', 'asc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        $perPage = $request->get('per_page', 12);
        $hostels = $query->paginate($perPage);

        // Add minimum price for each hostel
        $hostels->getCollection()->transform(function ($hostel) {
            $hostel->min_price = $hostel->activeRoomTypes->min('price_per_night');
            return $hostel;
        });

        return response()->json([
            'hostels' => $hostels,
            'filters' => [
                'location' => $request->location,
                'check_in' => $request->check_in,
                'check_out' => $request->check_out,
                'guests' => $request->guests,
                'min_price' => $request->min_price,
                'max_price' => $request->max_price,
                'room_type' => $request->room_type,
                'amenities' => $request->amenities ?? [],
                'sort_by' => $sortBy,
            ],
        ]);
    }

    /**
     * Get search suggestions for locations
     */
    public function suggestions(Request $request)
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
        ]);

        $query = $request->query;
        
        $suggestions = Hostel::active()
            ->verified()
            ->where(function ($q) use ($query) {
                $q->where('city', 'LIKE', "%{$query}%")
                  ->orWhere('state', 'LIKE', "%{$query}%")
                  ->orWhere('name', 'LIKE', "%{$query}%");
            })
            ->select('city', 'state', 'name')
            ->distinct()
            ->limit(10)
            ->get()
            ->map(function ($hostel) {
                return [
                    'location' => "{$hostel->city}, {$hostel->state}",
                    'type' => 'city',
                    'hostel_name' => $hostel->name,
                ];
            })
            ->unique('location')
            ->values();

        return response()->json($suggestions);
    }

    /**
     * Get popular destinations
     */
    public function popularDestinations()
    {
        $destinations = Hostel::active()
            ->verified()
            ->select('city', 'state', DB::raw('COUNT(*) as hostel_count'))
            ->groupBy('city', 'state')
            ->orderBy('hostel_count', 'desc')
            ->limit(6)
            ->get()
            ->map(function ($destination) {
                return [
                    'name' => "{$destination->city}, {$destination->state}",
                    'city' => $destination->city,
                    'state' => $destination->state,
                    'hostel_count' => $destination->hostel_count,
                ];
            });

        return response()->json($destinations);
    }

    /**
     * Get price range statistics
     */
    public function priceRange(Request $request)
    {
        $query = RoomType::whereHas('hostel', function ($q) {
            $q->active()->verified();
        })->where('is_active', true);

        // Apply location filter if provided
        if ($request->filled('location')) {
            $location = $request->location;
            $query->whereHas('hostel', function ($q) use ($location) {
                $q->where(function ($subQ) use ($location) {
                    $subQ->where('city', 'LIKE', "%{$location}%")
                         ->orWhere('state', 'LIKE', "%{$location}%");
                });
            });
        }

        $stats = $query->selectRaw('
            MIN(price_per_night) as min_price,
            MAX(price_per_night) as max_price,
            AVG(price_per_night) as avg_price,
            COUNT(*) as total_rooms
        ')->first();

        return response()->json([
            'min_price' => (int) $stats->min_price,
            'max_price' => (int) $stats->max_price,
            'avg_price' => (int) $stats->avg_price,
            'total_rooms' => $stats->total_rooms,
        ]);
    }

    /**
     * Check availability for specific dates and location
     */
    public function checkAvailability(Request $request)
    {
        $request->validate([
            'location' => 'required|string',
            'check_in' => 'required|date|after_or_equal:today',
            'check_out' => 'required|date|after:check_in',
            'guests' => 'required|integer|min:1|max:20',
        ]);

        $availableHostels = Hostel::active()
            ->verified()
            ->where(function ($q) use ($request) {
                $location = $request->location;
                $q->where('city', 'LIKE', "%{$location}%")
                  ->orWhere('state', 'LIKE', "%{$location}%");
            })
            ->whereHas('activeRoomTypes', function ($q) use ($request) {
                $q->where('capacity', '>=', $request->guests)
                  ->whereHas('rooms', function ($roomQuery) use ($request) {
                      $roomQuery->where('status', 'available')
                               ->whereDoesntHave('bookings', function ($bookingQuery) use ($request) {
                                   $bookingQuery->whereIn('status', ['confirmed', 'checked_in'])
                                               ->where(function ($dateQuery) use ($request) {
                                                   $checkIn = $request->check_in;
                                                   $checkOut = $request->check_out;
                                                   $dateQuery->whereBetween('check_in_date', [$checkIn, $checkOut])
                                                            ->orWhereBetween('check_out_date', [$checkIn, $checkOut])
                                                            ->orWhere(function ($overlapQuery) use ($checkIn, $checkOut) {
                                                                $overlapQuery->where('check_in_date', '<=', $checkIn)
                                                                            ->where('check_out_date', '>=', $checkOut);
                                                            });
                                               });
                               });
                  });
            })
            ->with(['activeRoomTypes' => function ($query) use ($request) {
                $query->where('capacity', '>=', $request->guests);
            }])
            ->get();

        $results = $availableHostels->map(function ($hostel) use ($request) {
            $availableRoomTypes = $hostel->activeRoomTypes->map(function ($roomType) use ($request) {
                $availability = $roomType->checkAvailability(
                    $request->check_in,
                    $request->check_out,
                    $request->guests
                );
                
                return [
                    'id' => $roomType->id,
                    'name' => $roomType->name,
                    'category' => $roomType->category,
                    'capacity' => $roomType->capacity,
                    'price_per_night' => $roomType->price_per_night,
                    'available_rooms' => $availability,
                ];
            })->filter(function ($roomType) {
                return $roomType['available_rooms'] > 0;
            });

            return [
                'hostel' => $hostel,
                'available_room_types' => $availableRoomTypes->values(),
                'min_price' => $availableRoomTypes->min('price_per_night'),
            ];
        })->filter(function ($result) {
            return $result['available_room_types']->isNotEmpty();
        });

        return response()->json([
            'available_hostels' => $results->values(),
            'total_available' => $results->count(),
            'search_params' => [
                'location' => $request->location,
                'check_in' => $request->check_in,
                'check_out' => $request->check_out,
                'guests' => $request->guests,
            ],
        ]);
    }

    /**
     * Search page for frontend
     */
    public function index(Request $request)
    {
        $request->validate([
            'location' => 'nullable|string|max:255',
            'semester' => 'nullable|string|in:month,semester,year',
            'academic_year' => 'nullable|string',
            'students' => 'nullable|integer|min:1|max:20',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string',
            'university' => 'nullable|string',
            'distance_to_university' => 'nullable|numeric|min:0',
            'room_type' => 'nullable|string',
        ]);

        $query = Hostel::active()
            ->verified()
            ->with(['activeRoomTypes']);

        // Location-based search
        if ($request->filled('location')) {
            $location = $request->location;
            $query->where(function ($q) use ($location) {
                $q->where('city', 'LIKE', "%{$location}%")
                  ->orWhere('state', 'LIKE', "%{$location}%")
                  ->orWhere('name', 'LIKE', "%{$location}%")
                  ->orWhere('address', 'LIKE', "%{$location}%");
            });
        }

        // Student capacity filter
        if ($request->filled('students')) {
            $students = $request->students;
            $query->whereHas('activeRoomTypes', function ($q) use ($students) {
                $q->where('capacity', '>=', $students);
            });
        }

        // Price range filter (use hostel base price)
        if ($request->filled('min_price') || $request->filled('max_price')) {
            if ($request->filled('min_price')) {
                $query->where('base_price', '>=', $request->min_price);
            }
            if ($request->filled('max_price')) {
                $query->where('base_price', '<=', $request->max_price);
            }
        }

        // Amenities filter - need to map user-friendly names back to database values
        if ($request->filled('amenities') && !empty($request->amenities)) {
            // Create reverse mapping for filtering
            $amenityMapping = [
                'WiFi' => ['wifi', 'WiFi', 'internet'],
                'Air Conditioning' => ['air_conditioning', 'ac', 'Air Conditioning'],
                'Parking' => ['parking', 'Parking'],
                'Kitchen' => ['kitchen', 'Kitchen'],
                'Laundry' => ['laundry', 'Laundry'],
                '24/7 Security' => ['security', '24/7 Security', 'Security'],
                'Study Room' => ['study_room', 'Study Room'],
                'Common Area' => ['common_area', 'Common Area'],
                'Water Supply' => ['water_supply', 'Water Supply'],
                'Backup Power' => ['backup_power', 'generator', 'Backup Power', 'Backup Generator'],
                'CCTV Surveillance' => ['cctv', 'CCTV Surveillance', 'CCTV'],
                'Gym/Fitness Center' => ['gym', 'Gym/Fitness Center', 'Gym'],
                'Library' => ['library', 'Library'],
                'Cafeteria' => ['cafeteria', 'dining_hall', 'Cafeteria', 'Dining Hall'],
                'Cleaning Service' => ['cleaning_service', 'Cleaning Service'],
                'High-Speed Internet' => ['internet', 'wifi', 'High-Speed Internet'],
                'Fully Furnished' => ['furnished', 'Fully Furnished'],
                'Balcony' => ['balcony', 'Balcony'],
                'Private Bathroom' => ['private_bathroom', 'Private Bathroom'],
                'Shared Bathroom' => ['shared_bathroom', 'Shared Bathroom'],
                'Hot Water' => ['hot_water', 'Hot Water'],
                'Refrigerator' => ['refrigerator', 'Refrigerator'],
                'Microwave' => ['microwave', 'Microwave'],
                'Wardrobe' => ['wardrobe', 'Wardrobe'],
                'Study Desk' => ['desk', 'Study Desk'],
                'Study Chair' => ['chair', 'Study Chair'],
                'Comfortable Bed' => ['bed', 'Comfortable Bed'],
                'Quality Mattress' => ['mattress', 'Quality Mattress'],
                'Television' => ['tv', 'cable_tv', 'Television', 'Cable TV'],
                'Shuttle Service' => ['shuttle_service', 'Shuttle Service'],
                'Medical Facility' => ['medical_facility', 'Medical Facility'],
                'Fire Safety System' => ['fire_safety', 'Fire Safety System'],
                'Elevator' => ['elevator', 'Elevator'],
                'Wheelchair Accessible' => ['wheelchair_accessible', 'Wheelchair Accessible'],
            ];

            foreach ($request->amenities as $amenity) {
                $possibleValues = $amenityMapping[$amenity] ?? [$amenity];
                $query->where(function ($q) use ($possibleValues) {
                    foreach ($possibleValues as $value) {
                        $q->orWhereJsonContains('amenities', $value);
                    }
                });
            }
        }

        // University filter
        if ($request->filled('university') && $request->university !== 'All Universities') {
            $query->whereHas('university', function ($q) use ($request) {
                $q->where('name', $request->university);
            });
        }

        // Payment period filter
        if ($request->filled('semester')) {
            $query->where('payment_period', $request->semester);
        }

        // Room type filter
        if ($request->filled('room_type') && $request->room_type !== 'All Room Types') {
            $query->whereHas('activeRoomTypes', function ($q) use ($request) {
                $q->where('name', 'LIKE', "%{$request->room_type}%");
            });
        }



        // Sorting
        $query->orderBy('created_at', 'desc');

        $perPage = 12;
        $hostels = $query->paginate($perPage);

        // Create amenity mapping for display
        $displayAmenityMapping = [
            'wifi' => 'WiFi',
            'air_conditioning' => 'Air Conditioning',
            'ac' => 'Air Conditioning',
            'parking' => 'Parking',
            'kitchen' => 'Kitchen',
            'laundry' => 'Laundry',
            'security' => '24/7 Security',
            'study_room' => 'Study Room',
            'common_area' => 'Common Area',
            'water_supply' => 'Water Supply',
            'backup_power' => 'Backup Power',
            'generator' => 'Backup Generator',
            'cctv' => 'CCTV Surveillance',
            'gym' => 'Gym/Fitness Center',
            'library' => 'Library',
            'cafeteria' => 'Cafeteria',
            'dining_hall' => 'Dining Hall',
            'cleaning_service' => 'Cleaning Service',
            'internet' => 'High-Speed Internet',
            'furnished' => 'Fully Furnished',
            'balcony' => 'Balcony',
            'private_bathroom' => 'Private Bathroom',
            'shared_bathroom' => 'Shared Bathroom',
            'hot_water' => 'Hot Water',
            'refrigerator' => 'Refrigerator',
            'microwave' => 'Microwave',
            'wardrobe' => 'Wardrobe',
            'desk' => 'Study Desk',
            'chair' => 'Study Chair',
            'bed' => 'Comfortable Bed',
            'mattress' => 'Quality Mattress',
            'tv' => 'Television',
            'cable_tv' => 'Cable TV',
            'shuttle_service' => 'Shuttle Service',
            'medical_facility' => 'Medical Facility',
            'fire_safety' => 'Fire Safety System',
            'elevator' => 'Elevator',
            'wheelchair_accessible' => 'Wheelchair Accessible',
            'lounge' => 'Student Lounge',
            'recreation_room' => 'Recreation Room',
            'game_room' => 'Game Room',
            'outdoor_space' => 'Outdoor Space',
            'garden' => 'Garden Area',
            'terrace' => 'Terrace',
            'rooftop' => 'Rooftop Access',
            'reading_room' => 'Reading Room',
            'conference_room' => 'Conference Room',
            'prayer_room' => 'Prayer Room',
            'atm' => 'ATM',
            'shop' => 'Mini Shop',
            'vending_machine' => 'Vending Machine',
            'postal_service' => 'Postal Service',
        ];

        // Transform hostels data to match frontend expectations
        $hostels->getCollection()->transform(function ($hostel) use ($displayAmenityMapping) {
            $photoUrls = ImageHelper::getImageUrls($hostel->photos ?? []);

            // Transform amenities to user-friendly names
            $userFriendlyAmenities = collect($hostel->amenities ?? [])->map(function ($amenity) use ($displayAmenityMapping) {
                $key = strtolower(str_replace([' ', '-'], '_', $amenity));
                return $displayAmenityMapping[$key] ?? ucwords(str_replace(['_', '-'], ' ', $amenity));
            })->unique()->values()->toArray();

            return [
                'id' => $hostel->id,
                'name' => $hostel->name,
                'description' => $hostel->description ?? 'Comfortable student accommodation',
                'photos' => $photoUrls,
                'location' => $hostel->address ?? "{$hostel->city}, {$hostel->state}",
                'city' => $hostel->city,
                'state' => $hostel->state,
                'university_distance' => rand(1, 10), // TODO: Calculate actual distance
                'total_beds' => $hostel->activeRoomTypes->sum('capacity') ?? 0,
                'available_beds' => $hostel->activeRoomTypes->sum('capacity') ?? 0, // TODO: Calculate actual availability
                'amenities' => $userFriendlyAmenities,
                'payment_period' => $hostel->payment_period,
                'base_price' => $hostel->base_price,
                'service_fee' => $hostel->service_fee,
                'deposit_percentage' => $hostel->deposit_percentage,
                'cancellation_fee' => $hostel->cancellation_fee,
                'room_types' => $hostel->activeRoomTypes->map(function ($roomType) {
                    return [
                        'id' => $roomType->id,
                        'name' => $roomType->name,
                        'available_count' => $roomType->capacity, // TODO: Calculate actual availability
                    ];
                }),
            ];
        });

        // Get dynamic data for search filters
        $universities = University::orderBy('name')->get()->map(function ($university) {
            return [
                'value' => $university->name,
                'label' => $university->name,
            ];
        });

        // Add "All Universities" option at the beginning
        $universities->prepend([
            'value' => 'All Universities',
            'label' => 'All Universities',
        ]);

        $academicYears = AcademicYear::active()->orderBy('start_date', 'desc')->get()->map(function ($year) {
            return [
                'value' => $year->name,
                'label' => $year->display_name ?? $year->name,
            ];
        });

        // Get unique amenities from all hostels and make them user-friendly
        $rawAmenities = Hostel::active()
            ->whereNotNull('amenities')
            ->pluck('amenities')
            ->flatten()
            ->unique()
            ->sort()
            ->values()
            ->toArray();

        // Map amenities to user-friendly names
        $amenityMapping = [
            'wifi' => 'WiFi',
            'air_conditioning' => 'Air Conditioning',
            'ac' => 'Air Conditioning',
            'parking' => 'Parking',
            'kitchen' => 'Kitchen',
            'laundry' => 'Laundry',
            'security' => '24/7 Security',
            'study_room' => 'Study Room',
            'common_area' => 'Common Area',
            'water_supply' => 'Water Supply',
            'backup_power' => 'Backup Power',
            'generator' => 'Backup Generator',
            'cctv' => 'CCTV Surveillance',
            'gym' => 'Gym/Fitness Center',
            'library' => 'Library',
            'cafeteria' => 'Cafeteria',
            'dining_hall' => 'Dining Hall',
            'cleaning_service' => 'Cleaning Service',
            'internet' => 'High-Speed Internet',
            'furnished' => 'Fully Furnished',
            'balcony' => 'Balcony',
            'private_bathroom' => 'Private Bathroom',
            'shared_bathroom' => 'Shared Bathroom',
            'hot_water' => 'Hot Water',
            'refrigerator' => 'Refrigerator',
            'microwave' => 'Microwave',
            'wardrobe' => 'Wardrobe',
            'desk' => 'Study Desk',
            'chair' => 'Study Chair',
            'bed' => 'Comfortable Bed',
            'mattress' => 'Quality Mattress',
            'tv' => 'Television',
            'cable_tv' => 'Cable TV',
            'shuttle_service' => 'Shuttle Service',
            'medical_facility' => 'Medical Facility',
            'fire_safety' => 'Fire Safety System',
            'elevator' => 'Elevator',
            'wheelchair_accessible' => 'Wheelchair Accessible',
            'lounge' => 'Student Lounge',
            'recreation_room' => 'Recreation Room',
            'game_room' => 'Game Room',
            'outdoor_space' => 'Outdoor Space',
            'garden' => 'Garden Area',
            'terrace' => 'Terrace',
            'rooftop' => 'Rooftop Access',
            'reading_room' => 'Reading Room',
            'conference_room' => 'Conference Room',
            'prayer_room' => 'Prayer Room',
            'atm' => 'ATM',
            'shop' => 'Mini Shop',
            'vending_machine' => 'Vending Machine',
            'postal_service' => 'Postal Service',
        ];

        $allAmenities = collect($rawAmenities)->map(function ($amenity) use ($amenityMapping) {
            $key = strtolower(str_replace([' ', '-'], '_', $amenity));
            return $amenityMapping[$key] ?? ucwords(str_replace(['_', '-'], ' ', $amenity));
        })->unique()->sort()->values()->toArray();

        // Get unique room types from all room types
        $roomTypes = RoomType::whereHas('hostel', function ($q) {
            $q->active();
        })
        ->where('is_active', true)
        ->distinct()
        ->pluck('name')
        ->filter()
        ->sort()
        ->values()
        ->prepend('All Room Types')
        ->toArray();

        // Payment periods - only the three specified
        $paymentPeriods = [
            ['value' => 'month', 'label' => 'Per Month'],
            ['value' => 'semester', 'label' => 'Per Semester'],
            ['value' => 'year', 'label' => 'Per Academic Year'],
        ];

        return Inertia::render('search/index', [
            'hostels' => $hostels->items(),
            'total' => $hostels->total(),
            'currentPage' => $hostels->currentPage(),
            'lastPage' => $hostels->lastPage(),
            'filters' => [
                'location' => $request->location ?? '',
                'semester' => $request->semester ?? 'month',
                'academic_year' => $request->academic_year ?? ($academicYears->first()['value'] ?? '2024/2025'),
                'students' => $request->integer('students', 1),
                'min_price' => $request->min_price ?? 500,
                'max_price' => $request->max_price ?? 5000,
                'amenities' => $request->amenities ?? [],
                'university' => $request->university ?? 'All Universities',
                'distance_to_university' => $request->distance_to_university ?? 10,
                'room_type' => $request->room_type ?? 'All Room Types',
            ],
            'searchOptions' => [
                'universities' => $universities,
                'academicYears' => $academicYears,
                'amenities' => $allAmenities,
                'roomTypes' => $roomTypes,
                'paymentPeriods' => $paymentPeriods,
            ],
        ]);
    }
}
