<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Hostel;
use App\Models\Booking;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the dashboard based on user role
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Students should go directly to bookings instead of dashboard
        if ($user->role === 'student') {
            return redirect()->route('bookings.index');
        }

        // Super Admin Dashboard with real-time data
        if ($user->role === 'super_admin') {
            $period = $request->get('period', 30); // days
            
            $stats = [
                'platform_overview' => [
                    'total_users' => User::count(),
                    'total_hostels' => Hostel::count(),
                    'total_bookings' => Booking::count(),
                    'total_revenue' => Booking::where('payment_status', 'paid')->sum('total_amount'),
                    'active_hostels' => Hostel::where('is_active', true)->count(),
                    'verified_hostels' => Hostel::where('is_verified', true)->count(),
                ],
                'recent_activity' => [
                    'new_users' => User::whereDate('created_at', '>=', now()->subDays($period))->count(),
                    'new_hostels' => Hostel::whereDate('created_at', '>=', now()->subDays($period))->count(),
                    'new_bookings' => Booking::whereDate('created_at', '>=', now()->subDays($period))->count(),
                    'revenue_period' => Booking::where('payment_status', 'paid')
                        ->whereDate('created_at', '>=', now()->subDays($period))
                        ->sum('total_amount'),
                ],
                'booking_stats' => [
                    'pending' => Booking::where('status', 'pending')->count(),
                    'confirmed' => Booking::where('status', 'confirmed')->count(),
                    'checked_in' => Booking::where('status', 'checked_in')->count(),
                    'completed' => Booking::where('status', 'checked_out')->count(),
                    'cancelled' => Booking::where('status', 'cancelled')->count(),
                ],
                'payment_stats' => [
                    'paid' => Booking::where('payment_status', 'paid')->count(),
                    'pending' => Booking::where('payment_status', 'pending')->count(),
                    'failed' => Booking::where('payment_status', 'failed')->count(),
                ],
            ];

            // Chart data for bookings over time
            $bookingChartData = Booking::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->whereDate('created_at', '>=', now()->subDays($period))
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            // Revenue chart data
            $revenueChartData = Booking::selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue')
                ->where('payment_status', 'paid')
                ->whereDate('created_at', '>=', now()->subDays($period))
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return Inertia::render('dashboard', [
                'stats' => $stats,
                'bookingChartData' => $bookingChartData,
                'revenueChartData' => $revenueChartData,
                'period' => $period,
            ]);
        }

        // Hostel Admin Dashboard
        if ($user->role === 'hostel_admin') {
            return redirect()->route('admin.dashboard');
        }

        // Default dashboard for other roles
        return Inertia::render('dashboard');
    }
}
