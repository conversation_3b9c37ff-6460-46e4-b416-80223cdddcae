<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ErrorController extends Controller
{
    /**
     * Log client-side errors
     */
    public function logClientError(Request $request)
    {
        $request->validate([
            'error' => 'required|string|max:1000',
            'stack' => 'nullable|string|max:5000',
            'component_stack' => 'nullable|string|max:5000',
            'additional_info' => 'nullable|string|max:1000',
            'url' => 'required|string|max:500',
            'user_agent' => 'required|string|max:500',
            'timestamp' => 'required|date',
        ]);

        $errorId = Str::uuid();
        
        $logData = [
            'error_id' => $errorId,
            'user_id' => auth()->id(),
            'error_message' => $request->error,
            'stack_trace' => $request->stack,
            'component_stack' => $request->component_stack,
            'additional_info' => $request->additional_info,
            'url' => $request->url,
            'user_agent' => $request->user_agent,
            'client_timestamp' => $request->timestamp,
            'server_timestamp' => now(),
            'ip_address' => $request->ip(),
            'session_id' => session()->getId(),
        ];

        // Log the error
        Log::channel('single')->error('Client-side error reported', $logData);

        // In production, you might want to send to error tracking service
        if (app()->environment('production')) {
            // Example: Send to Sentry, Bugsnag, etc.
            // \Sentry\captureException(new \Exception($request->error));
        }

        return response()->json([
            'success' => true,
            'error_id' => $errorId,
            'message' => 'Error logged successfully'
        ]);
    }
} 