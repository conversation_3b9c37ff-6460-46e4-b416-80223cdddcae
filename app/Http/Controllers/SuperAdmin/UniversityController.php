<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\University;
use Illuminate\Http\Request;
use Inertia\Inertia;

class UniversityController extends Controller
{
    // List all universities
    public function index()
    {
        $universities = University::orderBy('name')->get();
        return Inertia::render('SuperAdmin/Universities', [
            'universities' => $universities,
        ]);
    }

    // Store a new university
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:universities,name',
        ]);
        $university = University::create(['name' => $request->name]);
        return redirect()->back()->with('success', 'University created successfully.');
    }

    // Update an existing university
    public function update(Request $request, University $university)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:universities,name,' . $university->id,
        ]);
        $university->update(['name' => $request->name]);
        return redirect()->back()->with('success', 'University updated successfully.');
    }

    // Delete a university
    public function destroy(University $university)
    {
        $university->delete();
        return redirect()->back()->with('success', 'University deleted successfully.');
    }
} 