<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class AcademicYearController extends Controller
{
    /**
     * Display a listing of academic years
     */
    public function index()
    {
        $academicYears = AcademicYear::orderBy('start_date', 'desc')->get();

        return Inertia::render('SuperAdmin/AcademicYears', [
            'academicYears' => $academicYears,
        ]);
    }

    /**
     * Store a newly created academic year
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:10|unique:academic_years,name',
            'display_name' => 'required|string|max:100',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'is_current' => 'boolean',
        ]);

        // If this is being set as current, unset any existing current
        if ($validated['is_current']) {
            AcademicYear::where('is_current', true)->update(['is_current' => false]);
        }

        AcademicYear::create($validated);

        return redirect()->route('admin.academic-years')
            ->with('success', 'Academic year created successfully.');
    }

    /**
     * Update the specified academic year
     */
    public function update(Request $request, AcademicYear $academicYear)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:10|unique:academic_years,name,' . $academicYear->id,
            'display_name' => 'required|string|max:100',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'is_current' => 'boolean',
        ]);

        // If this is being set as current, unset any existing current
        if ($validated['is_current']) {
            AcademicYear::where('is_current', true)
                ->where('id', '!=', $academicYear->id)
                ->update(['is_current' => false]);
        }

        $academicYear->update($validated);

        return redirect()->route('admin.academic-years')
            ->with('success', 'Academic year updated successfully.');
    }

    /**
     * Remove the specified academic year
     */
    public function destroy(AcademicYear $academicYear)
    {
        // Check if academic year has bookings
        if ($academicYear->bookings()->exists()) {
            return redirect()->route('admin.academic-years')
                ->with('error', 'Cannot delete academic year with existing bookings.');
        }

        $academicYear->delete();

        return redirect()->route('admin.academic-years')
            ->with('success', 'Academic year deleted successfully.');
    }

    /**
     * Set an academic year as current
     */
    public function setCurrent(AcademicYear $academicYear)
    {
        // Ensure the academic year is active
        if (!$academicYear->is_active) {
            return redirect()->route('admin.academic-years')
                ->with('error', 'Cannot set inactive academic year as current.');
        }

        DB::transaction(function () use ($academicYear) {
            // Unset any existing current academic year
            AcademicYear::where('is_current', true)->update(['is_current' => false]);
            
            // Set this academic year as current
            $academicYear->update(['is_current' => true]);
        });

        return redirect()->route('admin.academic-years')
            ->with('success', 'Current academic year updated successfully.');
    }
} 