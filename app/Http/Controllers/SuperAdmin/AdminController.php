<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Hostel;
use App\Models\Booking;
use App\Services\PhotoUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class AdminController extends Controller
{
    // Middleware is applied at route level in web.php

    /**
     * User Management Dashboard
     */
    public function users(Request $request)
    {
        $query = User::query();

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('email', 'like', "%{$request->search}%")
                  ->orWhere('student_id', 'like', "%{$request->search}%");
            });
        }

        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('university')) {
            $query->where('university', 'like', "%{$request->university}%");
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        $stats = [
            'total_users' => User::count(),
            'students' => User::where('role', 'student')->count(),
            'hostel_admins' => User::where('role', 'hostel_admin')->count(),
            'verified_users' => User::where('is_verified', true)->count(),
            'new_this_month' => User::whereMonth('created_at', now()->month)->count(),
        ];

        return Inertia::render('SuperAdmin/Users', [
            'users' => $users,
            'stats' => $stats,
            'filters' => $request->only(['search', 'role', 'university']),
            'universities' => User::whereNotNull('university')
                ->distinct()
                ->pluck('university')
                ->sort()
                ->values(),
        ]);
    }

    /**
     * Create new user
     */
    public function createUser(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => ['required', Rules\Password::defaults()],
            'role' => 'required|in:student,hostel_admin,super_admin',
            'phone' => 'nullable|string|max:20',
            'university' => 'nullable|string|max:255',
            'student_id' => 'nullable|string|unique:users',
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['is_verified'] = true;
        $validated['email_verified_at'] = now();

        User::create($validated);

        return redirect()->back()->with('success', 'User created successfully');
    }

    /**
     * Update user
     */
    public function updateUser(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'role' => 'required|in:student,hostel_admin,super_admin',
            'phone' => 'nullable|string|max:20',
            'university' => 'nullable|string|max:255',
            'student_id' => 'nullable|string|unique:users,student_id,' . $user->id,
            'is_verified' => 'boolean',
        ]);

        $user->update($validated);

        return redirect()->back()->with('success', 'User updated successfully');
    }

    /**
     * Toggle user verification
     */
    public function toggleUserVerification(User $user)
    {
        $user->update([
            'is_verified' => !$user->is_verified,
            'email_verified_at' => $user->is_verified ? null : now(),
        ]);

        $status = $user->is_verified ? 'verified' : 'unverified';
        return redirect()->back()->with('success', "User {$status} successfully");
    }

    /**
     * Delete user
     */
    public function deleteUser(User $user)
    {
        // Prevent deleting the last super admin
        if ($user->isSuperAdmin() && User::where('role', 'super_admin')->count() <= 1) {
            return redirect()->back()->with('error', 'Cannot delete the last super admin');
        }

        $user->delete();
        return redirect()->back()->with('success', 'User deleted successfully');
    }

    /**
     * Platform Analytics
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // days

        $stats = [
            'platform_overview' => [
                'total_users' => User::count(),
                'total_hostels' => Hostel::count(),
                'total_bookings' => Booking::count(),
                'total_revenue' => Booking::where('payment_status', 'paid')->sum('total_amount'),
                'active_hostels' => Hostel::where('is_active', true)->count(),
                'verified_hostels' => Hostel::where('is_verified', true)->count(),
            ],
            'recent_activity' => [
                'new_users' => User::whereDate('created_at', '>=', now()->subDays($period))->count(),
                'new_hostels' => Hostel::whereDate('created_at', '>=', now()->subDays($period))->count(),
                'new_bookings' => Booking::whereDate('created_at', '>=', now()->subDays($period))->count(),
                'revenue_period' => Booking::where('payment_status', 'paid')
                    ->whereDate('created_at', '>=', now()->subDays($period))
                    ->sum('total_amount'),
            ],
            'booking_stats' => [
                'pending' => Booking::where('status', 'pending')->count(),
                'confirmed' => Booking::where('status', 'confirmed')->count(),
                'checked_in' => Booking::where('status', 'checked_in')->count(),
                'completed' => Booking::where('status', 'checked_out')->count(),
                'cancelled' => Booking::where('status', 'cancelled')->count(),
            ],
            'payment_stats' => [
                'paid' => Booking::where('payment_status', 'paid')->count(),
                'pending' => Booking::where('payment_status', 'pending')->count(),
                'failed' => Booking::where('payment_status', 'failed')->count(),
            ],
        ];

        // Chart data for bookings over time
        $bookingChartData = Booking::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereDate('created_at', '>=', now()->subDays($period))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Revenue chart data
        $revenueChartData = Booking::selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue')
            ->where('payment_status', 'paid')
            ->whereDate('created_at', '>=', now()->subDays($period))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return Inertia::render('SuperAdmin/Analytics', [
            'stats' => $stats,
            'bookingChartData' => $bookingChartData,
            'revenueChartData' => $revenueChartData,
            'period' => $period,
        ]);
    }

    /**
     * Hostel Management for Super Admin
     */
    public function hostels(Request $request)
    {
        $query = Hostel::with(['owner', 'roomTypes', 'bookings']);

        if ($request->filled('search')) {
            $query->where('name', 'like', "%{$request->search}%")
                  ->orWhere('city', 'like', "%{$request->search}%");
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($request->filled('verification')) {
            if ($request->verification === 'verified') {
                $query->where('is_verified', true);
            } elseif ($request->verification === 'unverified') {
                $query->where('is_verified', false);
            }
        }

        $hostels = $query->withCount('bookings')->orderBy('created_at', 'desc')->paginate(15);

        $stats = [
            'total_hostels' => Hostel::count(),
            'active_hostels' => Hostel::where('is_active', true)->count(),
            'verified_hostels' => Hostel::where('is_verified', true)->count(),
            'pending_verification' => Hostel::where('is_verified', false)->count(),
        ];

        return Inertia::render('SuperAdmin/Hostels', [
            'hostels' => $hostels,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'verification']),
        ]);
    }

    /**
     * Toggle hostel verification
     */
    public function toggleHostelVerification(Hostel $hostel)
    {
        $hostel->update(['is_verified' => !$hostel->is_verified]);
        
        $status = $hostel->is_verified ? 'verified' : 'unverified';
        return redirect()->back()->with('success', "Hostel {$status} successfully");
    }

    /**
     * Show hostel edit form (Super Admin)
     */
    public function editHostelForm(Hostel $hostel)
    {
        $hostel->load('roomTypes');

        // Get all hostel admins to assign as owners
        $hostelAdmins = User::where('role', 'hostel_admin')->get(['id', 'name', 'email']);
        
        return Inertia::render('SuperAdmin/EditHostel', [
            'hostel' => $hostel,
            'hostel_admins' => $hostelAdmins,
        ]);
    }

    /**
     * Show hostel creation form (Super Admin)
     */
    public function createHostelForm()
    {
        // Get all hostel admins to assign as owners
        $hostelAdmins = User::where('role', 'hostel_admin')->get(['id', 'name', 'email']);
        $universities = \App\Models\University::orderBy('name')->get(['id', 'name']);
        return Inertia::render('SuperAdmin/CreateHostel', [
            'hostel_admins' => $hostelAdmins,
            'universities' => $universities,
        ]);
    }

    /**
     * Create new hostel (Super Admin)
     */
    public function createHostel(Request $request)
    {
        // Log the incoming request for debugging
        \Log::info('Hostel creation request', [
            'has_photos' => $request->hasFile('photos'),
            'photos_count' => $request->hasFile('photos') ? count($request->file('photos')) : 0,
        ]);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'house_rules' => 'nullable|string',
            'cancellation_policy' => 'nullable|string',
            'payment_period' => 'required|in:month,semester,year',
            'hostel_type' => 'required|in:university,homestel,other',
            'university_id' => 'nullable|exists:universities,id',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string|max:100',
            'owner_id' => 'required|exists:users,id',
            'is_active' => 'boolean',
            'is_verified' => 'boolean',
            'allow_deposit' => 'boolean',
            'is_per_semester' => 'boolean',
            'photos' => 'nullable|array|max:10',
            'photos.*' => 'file|image|mimes:jpeg,png,jpg,gif,webp|max:51200',
            'youtube_links' => 'nullable|array',
            'youtube_links.*' => 'nullable|url',
            
            // New pricing fields
            'base_price' => 'required|numeric|min:0',
            'service_fee' => 'nullable|numeric|min:0',
            'deposit_percentage' => 'nullable|numeric|min:0|max:100',
            'cancellation_fee' => 'nullable|numeric|min:0',
            
            'room_types' => 'nullable|array',
            'room_types.*.name' => 'required|string|max:255',
            'room_types.*.description' => 'nullable|string',
            'room_types.*.capacity' => 'required|integer|min:1',
            'room_types.*.beds' => 'required|integer|min:1',
            'room_types.*.available_rooms' => 'required|integer|min:0',
            'room_types.*.semester_type' => 'required|in:both,first_only,second_only',
        ], [
            // Custom validation messages for photos
            'photos.*.file' => 'The photo at position :position must be a valid file.',
            'photos.*.image' => 'The photo at position :position must be an image file.',
            'photos.*.mimes' => 'The photo at position :position must be a file of type: jpeg, png, jpg, gif, webp.',
            'photos.*.max' => 'The photo at position :position may not be greater than 50MB.',
            'photos.max' => 'You may not upload more than 10 photos.',
        ]);

        // Handle photo uploads
        if ($request->hasFile('photos')) {
            $photoUrls = [];
            
            foreach ($request->file('photos') as $index => $photo) {
                try {
                    // Skip empty or null files
                    if (!$photo || !$photo->isValid()) {
                        \Log::warning("Skipping invalid photo at index {$index}");
                        continue;
                    }
                    
                    \Log::info("Processing photo {$index}", [
                        'name' => $photo->getClientOriginalName(),
                        'size' => $photo->getSize(),
                        'valid' => $photo->isValid(),
                        'error' => $photo->getError(),
                    ]);
                    
                    // Additional validation
                    if ($photo->getSize() > 52428800) { // 50MB in bytes
                        throw new \Exception("File size exceeds 50MB limit");
                    }
                    
                    // Validate MIME type safely
                    try {
                        $mimeType = $photo->getMimeType();
                        $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                        if (!in_array($mimeType, $allowedMimes)) {
                            throw new \Exception("File type not allowed. Allowed types: JPEG, PNG, GIF, WebP");
                        }
                    } catch (\Exception $mimeException) {
                        throw new \Exception("Could not determine file type: " . $mimeException->getMessage());
                    }
                    
                    // Store to default disk (R2)
                    $path = $photo->store('hostels/photos');
                    if (!$path) {
                        throw new \Exception("Failed to store file to disk");
                    }
                    
                    $photoUrls[] = $path;
                    \Log::info("Successfully stored photo {$index} at path: {$path}");
                } catch (\Exception $e) {
                    \Log::error("Failed to process photo {$index}: " . $e->getMessage());
                    return back()->withErrors(["photos.{$index}" => "Photo {$index} failed to upload: " . $e->getMessage()]);
                }
            }
            $validated['photos'] = $photoUrls;
        }

        // Set default values for super admin created hostels
        $validated['is_active'] = $validated['is_active'] ?? true;
        $validated['is_verified'] = $validated['is_verified'] ?? true;
        $validated['allow_deposit'] = $validated['allow_deposit'] ?? false;
        $validated['is_per_semester'] = $validated['is_per_semester'] ?? true;
        
        // Set default pricing values if not provided
        $validated['service_fee'] = $validated['service_fee'] ?? 0;
        $validated['deposit_percentage'] = $validated['deposit_percentage'] ?? 0;
        $validated['cancellation_fee'] = $validated['cancellation_fee'] ?? 0;

        $hostel = Hostel::create($validated);

        if (isset($validated['room_types'])) {
            foreach ($validated['room_types'] as $roomTypeData) {
                $roomTypeData['max_occupancy'] = $roomTypeData['capacity']; // Set max_occupancy
                $roomType = $hostel->roomTypes()->create($roomTypeData);

                // Create individual rooms
                $numberOfRooms = $roomTypeData['available_rooms'] ?? 0;
                for ($i = 1; $i <= $numberOfRooms; $i++) {
                    $roomNumber = $i; // Sequential room numbering for new room types
                    $roomType->rooms()->create([
                        'hostel_id' => $hostel->id,
                        'room_number' => (string)$roomNumber,
                        'capacity' => $roomTypeData['capacity'],
                        'status' => 'available',
                    ]);
                }
            }
        }

        return redirect()->route('admin.hostels')
            ->with('success', 'Hostel created successfully!');
    }

    /**
     * Update hostel (Super Admin)
     */
    public function updateHostel(Request $request, Hostel $hostel)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'amenities' => 'nullable|array',
            'owner_id' => 'required|exists:users,id',
            'is_active' => 'boolean',
            'is_verified' => 'boolean',
            'allow_deposit' => 'boolean',
            'is_per_semester' => 'boolean',
            'payment_period' => 'required|in:month,semester,year',
            
            // New pricing fields with proper defaults
            'base_price' => 'required|numeric|min:0',
            'service_fee' => 'nullable|numeric|min:0',
            'deposit_percentage' => 'nullable|numeric|min:0|max:100',
            'cancellation_fee' => 'nullable|numeric|min:0',
            
            'photos' => 'nullable|array',
            'photos.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:51200', // 50MB for R2
            'existing_photos' => 'nullable|array',
            'existing_photos.*' => 'string',
            'room_types' => 'nullable|array',
            'room_types.*.id' => 'nullable|string|exists:room_types,id',
            'room_types.*.name' => 'required|string|max:255',
            'room_types.*.description' => 'nullable|string',
            'room_types.*.capacity' => 'required|integer|min:1',
            'room_types.*.beds' => 'required|integer|min:1',
            'room_types.*.available_rooms' => 'required|integer|min:0',
            'room_types.*.semester_type' => 'required|in:both,first_only,second_only',
        ]);
        
        // Handle Photo Updates
        $currentPhotos = $hostel->photos ?? [];
        $existingPhotos = $validated['existing_photos'] ?? [];
        
        // Photos to delete are those in currentPhotos but not in existingPhotos
        $photosToDelete = array_diff($currentPhotos, $existingPhotos);
        foreach ($photosToDelete as $photo) {
            Storage::delete($photo);
        }

        $newPhotoUrls = [];
        if ($request->hasFile('photos')) {
            foreach ($request->file('photos') as $index => $photo) {
                try {
                    // Skip empty or null files
                    if (!$photo || !$photo->isValid()) {
                        \Log::warning("Skipping invalid photo at index {$index}");
                        continue;
                    }
                    
                    // Additional validation
                    if ($photo->getSize() > 52428800) { // 50MB in bytes
                        throw new \Exception("File size exceeds 50MB limit");
                    }
                    
                    // Store to default disk (R2)
                    $path = $photo->store('hostels/photos');
                    if (!$path) {
                        throw new \Exception("Failed to store file to disk");
                    }
                    
                    $newPhotoUrls[] = $path;
                    \Log::info("Successfully stored photo {$index} at path: {$path}");
                } catch (\Exception $e) {
                    \Log::error("Failed to process photo {$index}: " . $e->getMessage());
                    return back()->withErrors(["photos.{$index}" => "Photo {$index} failed to upload: " . $e->getMessage()]);
                }
            }
        }
        $validated['photos'] = array_merge($existingPhotos, $newPhotoUrls);

        // Handle Room Type Updates
        if (isset($validated['room_types'])) {
            $existingRoomTypeIds = [];
            foreach ($validated['room_types'] as $roomTypeData) {
                $roomTypeData['max_occupancy'] = $roomTypeData['capacity'];
                $roomType = null;
                if (isset($roomTypeData['id'])) {
                    // Update existing room type
                    $roomType = \App\Models\RoomType::find($roomTypeData['id']);
                    if ($roomType && $roomType->hostel_id === $hostel->id) {
                        $roomType->update($roomTypeData);
                        $existingRoomTypeIds[] = $roomType->id;
                    }
                } else {
                    // Create new room type
                    $roomType = $hostel->roomTypes()->create($roomTypeData);
                    $existingRoomTypeIds[] = $roomType->id;
                }

                if ($roomType) {
                    // Sync rooms
                    $numberOfRooms = $roomTypeData['available_rooms'] ?? 0;
                    $existingRoomsCount = $roomType->rooms()->count();

                    if ($numberOfRooms > $existingRoomsCount) {
                        // Get existing room numbers for this room type to avoid duplicates
                        $existingRoomNumbers = $roomType->rooms()->pluck('room_number')->map('intval')->toArray();
                        
                        // Add new rooms
                        $roomsToAdd = $numberOfRooms - $existingRoomsCount;
                        $currentMaxRoom = empty($existingRoomNumbers) ? 0 : max($existingRoomNumbers);
                        
                        for ($i = 1; $i <= $roomsToAdd; $i++) {
                            // Find next available room number
                            $roomNumber = $currentMaxRoom + $i;
                            
                            // Double-check this room number doesn't exist (extra safety)
                            while (in_array($roomNumber, $existingRoomNumbers)) {
                                $roomNumber++;
                            }
                            
                            $roomType->rooms()->create([
                                'hostel_id' => $hostel->id,
                                'room_number' => (string)$roomNumber,
                                'capacity' => $roomTypeData['capacity'],
                                'status' => 'available',
                            ]);
                            
                            // Add to existing numbers to avoid duplicates in the loop
                            $existingRoomNumbers[] = $roomNumber;
                        }
                    } elseif ($numberOfRooms < $existingRoomsCount) {
                        // Delete excess rooms (be careful with this in production)
                        $roomType->rooms()->orderBy('created_at', 'desc')->limit($existingRoomsCount - $numberOfRooms)->delete();
                    }
                }
            }
            // Delete room types that were removed in the form
            $hostel->roomTypes()->whereNotIn('id', $existingRoomTypeIds)->delete();
        } else {
            // If no room types are submitted, delete all existing ones
            $hostel->roomTypes()->delete();
        }

        // Set default values for nullable pricing fields
        $validated['service_fee'] = $validated['service_fee'] ?? 0;
        $validated['deposit_percentage'] = $validated['deposit_percentage'];
        $validated['cancellation_fee'] = $validated['cancellation_fee'] ?? 0;

        $hostel->update($validated);

        return redirect()->route('admin.hostels')
            ->with('success', 'Hostel updated successfully!');
    }

    /**
     * Delete hostel (Super Admin)
     */
    public function deleteHostel(Hostel $hostel)
    {
        // Check if hostel has active bookings
        if ($hostel->bookings()->whereIn('status', ['pending', 'confirmed', 'checked_in'])->exists()) {
            return redirect()->back()
                ->with('error', 'Cannot delete hostel with active bookings.');
        }

        // Delete photos
        if ($hostel->photos) {
            foreach ($hostel->photos as $photo) {
                Storage::delete($photo);
            }
        }

        $hostel->delete();

        return redirect()->route('admin.hostels')
            ->with('success', 'Hostel deleted successfully.');
    }

    /**
     * Payment Management
     */
    public function payments(Request $request)
    {
        $query = Booking::with(['user', 'hostel']);

        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('booking_reference', 'like', "%{$request->search}%")
                  ->orWhereHas('user', function ($userQuery) use ($request) {
                      $userQuery->where('name', 'like', "%{$request->search}%")
                               ->orWhere('email', 'like', "%{$request->search}%");
                  })
                  ->orWhereHas('hostel', function ($hostelQuery) use ($request) {
                      $hostelQuery->where('name', 'like', "%{$request->search}%");
                  });
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20);

        $stats = [
            'total_payments' => Booking::count(),
            'paid_payments' => Booking::where('payment_status', 'paid')->count(),
            'pending_payments' => Booking::where('payment_status', 'pending')->count(),
            'failed_payments' => Booking::where('payment_status', 'failed')->count(),
            'total_revenue' => Booking::where('payment_status', 'paid')->sum('total_amount'),
            'this_month_revenue' => Booking::where('payment_status', 'paid')
                ->whereMonth('created_at', now()->month)
                ->sum('total_amount'),
        ];

        return Inertia::render('SuperAdmin/Payments', [
            'payments' => $payments,
            'stats' => $stats,
            'filters' => $request->only(['status', 'search', 'date_from', 'date_to']),
        ]);
    }

    /**
     * System Settings
     */
    public function settings()
    {
        return Inertia::render('SuperAdmin/Settings', [
            'settings' => [
                'platform_name' => config('app.name'),
                'environment' => config('app.env'),
                'debug_mode' => config('app.debug'),
                'maintenance_mode' => app()->isDownForMaintenance(),
            ]
        ]);
    }
} 