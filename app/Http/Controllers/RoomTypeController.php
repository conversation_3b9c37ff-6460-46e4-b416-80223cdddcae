<?php

namespace App\Http\Controllers;

use App\Models\Hostel;
use App\Models\RoomType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class RoomTypeController extends Controller
{
    /**
     * Display a listing of room types for a hostel.
     */
    public function index(Hostel $hostel)
    {
        $this->authorize('view', $hostel);

        $roomTypes = $hostel->roomTypes()
            ->with(['availableRooms'])
            ->withCount(['bookings', 'rooms'])
            ->get();

        return Inertia::render('room-types/index', [
            'hostel' => $hostel,
            'room_types' => $roomTypes,
            'can_manage' => auth()->user()->can('update', $hostel),
        ]);
    }

    /**
     * Show the form for creating a new room type.
     */
    public function create(Hostel $hostel)
    {
        $this->authorize('update', $hostel);

        return Inertia::render('room-types/create', [
            'hostel' => $hostel,
        ]);
    }

    /**
     * Store a newly created room type.
     */
    public function store(Request $request, Hostel $hostel)
    {
        $this->authorize('update', $hostel);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|in:dorm,private,family',
            'gender_type' => 'required|in:male,female,mixed',
            'semester_type' => 'required|in:both,first_only,second_only',
            'capacity' => 'required|integer|min:1|max:20',
            'max_occupancy' => 'required|integer|min:1|max:20',
            'beds' => 'required|integer|min:1|max:20',
            'available_rooms' => 'required|integer|min:0|max:100',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string|max:100',
            'photos' => 'nullable|array|max:10',
            'photos.*' => 'image|mimes:jpeg,png,jpg,webp|max:51200', // 50MB for R2
        ]);

        // Handle photo uploads
        if ($request->hasFile('photos')) {
            $photoUrls = [];
            foreach ($request->file('photos') as $photo) {
                $path = $photo->store('room-types/photos');
                $photoUrls[] = $path;
            }
            $validated['photos'] = $photoUrls;
        }

        $validated['hostel_id'] = $hostel->id;
        
        $roomType = RoomType::create($validated);

        // Create individual room instances with beds
        $this->createRoomInstances($roomType, $validated['available_rooms'], $validated['beds']);

        return redirect()->route('hostels.room-types.index', $hostel)
            ->with('success', 'Room type created successfully with beds!');
    }

    /**
     * Display the specified room type.
     */
    public function show(Hostel $hostel, RoomType $roomType)
    {
        $this->authorize('view', $hostel);

        $roomType->load(['hostel', 'rooms', 'bookings' => function ($query) {
            $query->active()->latest()->take(10);
        }]);

        return Inertia::render('room-types/show', [
            'hostel' => $hostel,
            'room_type' => $roomType,
            'can_edit' => auth()->user()->can('update', $hostel),
        ]);
    }

    /**
     * Show the form for editing the room type.
     */
    public function edit(Hostel $hostel, RoomType $roomType)
    {
        $this->authorize('update', $hostel);

        return Inertia::render('room-types/edit', [
            'hostel' => $hostel,
            'room_type' => $roomType,
        ]);
    }

    /**
     * Update the specified room type.
     */
    public function update(Request $request, Hostel $hostel, RoomType $roomType)
    {
        $this->authorize('update', $hostel);

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'sometimes|required|in:dorm,private,family',
            'gender_type' => 'sometimes|required|in:male,female,mixed',
            'semester_type' => 'sometimes|required|in:both,first_only,second_only',
            'capacity' => 'sometimes|required|integer|min:1|max:20',
            'max_occupancy' => 'sometimes|required|integer|min:1|max:20',
            'beds' => 'sometimes|required|integer|min:1|max:20',
            'available_rooms' => 'sometimes|required|integer|min:0|max:100',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string|max:100',
            'photos' => 'nullable|array|max:10',
            'photos.*' => 'image|mimes:jpeg,png,jpg,webp|max:51200', // 50MB for R2
            'is_active' => 'sometimes|boolean',
        ]);

        // Handle photo uploads
        if ($request->hasFile('photos')) {
            // Delete old photos
            if ($roomType->photos) {
                foreach ($roomType->photos as $photo) {
                    Storage::delete($photo);
                }
            }
            
            // Upload new photos
            $photoUrls = [];
            foreach ($request->file('photos') as $photo) {
                $path = $photo->store('room-types/photos');
                $photoUrls[] = $path;
            }
            $validated['photos'] = $photoUrls;
        }

        $oldRoomCount = $roomType->available_rooms;
        $oldBedCount = $roomType->beds;
        $roomType->update($validated);

        // Adjust room instances if room count or bed count changed
        if ((isset($validated['available_rooms']) && $validated['available_rooms'] !== $oldRoomCount) ||
            (isset($validated['beds']) && $validated['beds'] !== $oldBedCount)) {
            $this->adjustRoomInstances(
                $roomType, 
                $oldRoomCount, 
                $validated['available_rooms'] ?? $roomType->available_rooms,
                $oldBedCount,
                $validated['beds'] ?? $roomType->beds
            );
        }

        return redirect()->route('hostels.room-types.show', [$hostel, $roomType])
            ->with('success', 'Room type updated successfully!');
    }

    /**
     * Remove the specified room type.
     */
    public function destroy(Hostel $hostel, RoomType $roomType)
    {
        $this->authorize('update', $hostel);

        // Check if room type has active bookings
        if ($roomType->bookings()->active()->exists()) {
            return redirect()->back()
                ->with('error', 'Cannot delete room type with active bookings.');
        }

        // Delete photos
        if ($roomType->photos) {
            foreach ($roomType->photos as $photo) {
                Storage::delete($photo);
            }
        }

        $roomType->delete();

        return redirect()->route('hostels.room-types.index', $hostel)
            ->with('success', 'Room type deleted successfully.');
    }

    /**
     * Create room instances for a room type with beds
     */
    private function createRoomInstances(RoomType $roomType, int $count, int $bedsPerRoom)
    {
        for ($i = 1; $i <= $count; $i++) {
            $room = $roomType->rooms()->create([
                'room_number' => $i, // Sequential room numbering
                'status' => 'available',
            ]);

            // Create beds for the room
            $this->createBedsForRoom($room, $bedsPerRoom);
        }
    }

    /**
     * Create beds for a room
     */
    private function createBedsForRoom($room, int $bedCount)
    {
        for ($j = 1; $j <= $bedCount; $j++) {
            $bedType = 'single';
            $bedNumber = 'B' . $j;
            
            // Use bunk beds for rooms with more than 2 beds
            if ($bedCount > 2) {
                $bedType = $j <= ceil($bedCount / 2) ? 'bunk_lower' : 'bunk_upper';
                $bedNumber = ($j <= ceil($bedCount / 2)) ? 
                    'Lower-' . chr(64 + $j) : 
                    'Upper-' . chr(64 + ($j - ceil($bedCount / 2)));
            }

            $room->beds()->create([
                'bed_number' => $bedNumber,
                'bed_type' => $bedType,
                'status' => 'available',
            ]);
        }
    }

    /**
     * Adjust room instances when room count or bed count changes
     */
    private function adjustRoomInstances(RoomType $roomType, int $oldRoomCount, int $newRoomCount, int $oldBedCount, int $newBedCount)
    {
        if ($newRoomCount > $oldRoomCount) {
            // Add new rooms
            for ($i = $oldRoomCount + 1; $i <= $newRoomCount; $i++) {
                $room = $roomType->rooms()->create([
                    'room_number' => $i, // Sequential room numbering
                    'status' => 'available',
                ]);
                $this->createBedsForRoom($room, $newBedCount);
            }
        } elseif ($newRoomCount < $oldRoomCount) {
            // Remove excess rooms (only if they have no active bookings)
            $roomsToRemove = $roomType->rooms()
                ->latest()
                ->take($oldRoomCount - $newRoomCount)
                ->get();

            foreach ($roomsToRemove as $room) {
                if (!$room->bookings()->active()->exists()) {
                    $room->beds()->delete(); // Delete beds first
                    $room->delete();
                }
            }
        }

        // Adjust beds in existing rooms if bed count changed
        if ($newBedCount !== $oldBedCount) {
            foreach ($roomType->rooms as $room) {
                $currentBedCount = $room->beds()->count();
                
                if ($newBedCount > $currentBedCount) {
                    // Add beds
                    for ($j = $currentBedCount + 1; $j <= $newBedCount; $j++) {
                        $bedType = $newBedCount > 2 ? 
                            ($j <= ceil($newBedCount / 2) ? 'bunk_lower' : 'bunk_upper') : 
                            'single';
                        $bedNumber = $newBedCount > 2 ? 
                            ($j <= ceil($newBedCount / 2) ? 'Lower-' . chr(64 + $j) : 'Upper-' . chr(64 + ($j - ceil($newBedCount / 2)))) :
                            'B' . $j;

                        $room->beds()->create([
                            'bed_number' => $bedNumber,
                            'bed_type' => $bedType,
                            'status' => 'available',
                        ]);
                    }
                } elseif ($newBedCount < $currentBedCount) {
                    // Remove excess beds (only if not occupied)
                    $bedsToRemove = $room->beds()
                        ->where('status', 'available')
                        ->latest()
                        ->take($currentBedCount - $newBedCount)
                        ->get();

                    foreach ($bedsToRemove as $bed) {
                        $bed->delete();
                    }
                }
            }
        }
    }
}
