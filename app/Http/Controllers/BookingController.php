<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreBookingRequest;
use App\Http\Requests\UpdateBookingRequest;
use App\Models\Booking;
use App\Models\Hostel;
use App\Models\RoomType;
use App\Models\Room;
use App\Models\AcademicYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class BookingController extends Controller
{
    use AuthorizesRequests;
    
    /**
     * Display a listing of bookings
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $query = Booking::with(['user', 'hostel', 'roomType', 'room']);

        // Filter based on user role
        if ($user->isStudent()) {
            $query->where('user_id', $user->id);
        } elseif ($user->isHostelAdmin()) {
            // Show bookings for hostels managed by this admin
            $hostelIds = $user->hostels()->pluck('id');
            $query->whereIn('hostel_id', $hostelIds);
        }
        // Super admins see all bookings

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('hostel_id')) {
            $query->where('hostel_id', $request->hostel_id);
        }

        if ($request->filled('date_from')) {
            $query->where('check_in_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('check_out_date', '<=', $request->date_to);
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(15);

        return Inertia::render('bookings/index', [
            'bookings' => $bookings,
            'filters' => [
                'status' => $request->status,
                'hostel_id' => $request->hostel_id,
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
            ],
        ]);
    }

    /**
     * Show the form for creating a new booking
     */
    public function create(Request $request)
    {
        $hostel = null;
        $roomType = null;
        $user = auth()->user();

        if ($request->filled('hostel_id')) {
            $hostel = \App\Models\Hostel::with('activeRoomTypes')->findOrFail($request->hostel_id);
            // Filter room types by user gender
            if ($user && $hostel) {
                $hostel->setRelation('activeRoomTypes', $hostel->activeRoomTypes->filter(function ($rt) use ($user) {
                    return $rt->gender_type === $user->gender || $rt->gender_type === 'mixed';
                })->values());
            }
        }

        if ($request->filled('room_type_id')) {
            $roomType = \App\Models\RoomType::with('hostel.activeRoomTypes')->findOrFail($request->room_type_id);
            $hostel = $roomType->hostel;
            // Filter room types by user gender
            if ($user && $hostel) {
                $hostel->setRelation('activeRoomTypes', $hostel->activeRoomTypes->filter(function ($rt) use ($user) {
                    return $rt->gender_type === $user->gender || $rt->gender_type === 'mixed';
                })->values());
            }
        }

        return Inertia::render('bookings/create', [
            'hostel' => $hostel,
            'room_type' => $roomType,
            'booking_data' => [
                'hostel_id' => $request->hostel_id,
                'room_type_id' => $request->room_type_id,
                'check_in_date' => $request->check_in,
                'check_out_date' => $request->check_out,
                'guests' => $request->guests ?? 1,
            ],
            'academicYears' => \App\Models\AcademicYear::active()->orderBy('start_date', 'desc')->get(),
        ]);
    }

    /**
     * Get available rooms and beds for a given room type.
     */
    public function getAvailableRooms(Request $request, RoomType $roomType)
    {
        $this->authorize('view', $roomType->hostel);
        $user = auth()->user();
        
        // Enforce gender filtering for all users
        if ($roomType->gender_type !== 'mixed' && $user && $roomType->gender_type !== $user->gender) {
            return response()->json([], 403);
        }

        $validated = $request->validate([
            'academic_year' => 'sometimes|string',
            'semester' => 'sometimes|string|in:first,second,full_year',
        ]);

        $academicYear = $validated['academic_year'] ?? '2024/2025';
        $semester = $validated['semester'] ?? 'first';

        $bookedRoomCapacity = Booking::where('room_type_id', $roomType->id)
            ->where('academic_year', $academicYear)
            ->where(function ($query) use ($semester) {
                if ($semester === 'full_year') {
                    $query->whereIn('semester', ['first', 'second', 'full_year']);
                } else {
                    $query->whereIn('semester', [$semester, 'full_year']);
                }
            })
            ->whereIn('status', ['confirmed', 'checked_in', 'pending'])
            ->groupBy('room_id')
            ->select('room_id', DB::raw('SUM(students) as total_students'))
            ->get()
            ->pluck('total_students', 'room_id');

        $availableRooms = Room::where('room_type_id', $roomType->id)
            ->where('status', 'available')
            ->where(function ($query) use ($user) {
                // Filter rooms by gender allocation - users can only book rooms allocated to their gender or mixed rooms
                if ($user && $user->gender) {
                    $query->where('gender_allocated', $user->gender)
                          ->orWhere('gender_allocated', 'mixed');
                } else {
                    // If no user gender, only show mixed rooms
                    $query->where('gender_allocated', 'mixed');
                }
            })
            ->get()
            ->filter(function ($room) use ($bookedRoomCapacity) {
                $bookedCount = $bookedRoomCapacity->get($room->id, 0);
                return $room->capacity > $bookedCount;
            });

        return response()->json($availableRooms->values());
    }

    /**
     * Store a newly created booking
     */
    public function store(StoreBookingRequest $request)
    {
        $user = auth()->user();
        $roomType = \App\Models\RoomType::findOrFail($request->room_type_id);
        // Enforce gender validation for all users
        if ($roomType->gender_type !== 'mixed' && $user && $roomType->gender_type !== $user->gender) {
            return redirect()->back()->withErrors(['room_type_id' => 'You are not eligible to book this room type based on gender.']);
        }
        return DB::transaction(function () use ($request) {
            $data = $request->validated();
            
            $roomType = RoomType::with('hostel')->findOrFail($data['room_type_id']);
            $hostel = $roomType->hostel;
            
            $academicYear = $data['academic_year'];
            $semester = $data['semester'];

            if (!$hostel->base_price) {
                throw new \Exception('Base price not set for this hostel');
            }

            $pricingData = $hostel->calculateBookingPrice($data['students'], $semester);
            
            $basePrice = $pricingData['base_price'];
            $subtotal = $pricingData['subtotal'];
            $serviceFee = $pricingData['service_fee'];
            $charges = $pricingData['charges'];
            $totalAmount = $pricingData['total_amount'];

            // Determine payment amounts based on type
            $amountDue = 0;
            $amountPaid = 0;
            
            if ($data['payment_type'] === 'deposit' && $hostel->allow_deposit) {
                $depositPercentage = $hostel->deposit_percentage;
                $depositAmount = $totalAmount * ($depositPercentage / 100);
                $amountDue = $totalAmount - $depositAmount;
                // Amount paid will be set after payment processing
            } else {
                // Full payment
                $amountDue = 0;
                // Amount paid will be set after payment processing
            }

            // Automatically find and assign an available room based on gender
            $user = auth()->user();
            $availableRoom = $this->findAvailableRoom($roomType, $user, $academicYear, $semester);
            
            if (!$availableRoom) {
                return redirect()->back()->withErrors([
                    'room_type_id' => 'No available rooms for your gender allocation. Please try a different room type.'
                ]);
            }

            // Create the booking
            $booking = Booking::create([
                'user_id' => auth()->id(),
                'hostel_id' => $data['hostel_id'],
                'room_type_id' => $data['room_type_id'],
                'room_id' => $availableRoom->id,
                'semester' => $data['semester'],
                'academic_year' => $data['academic_year'],
                'students' => $data['students'],
                'price_per_semester' => $basePrice,
                'subtotal' => $subtotal,
                'charges' => $charges,
                'service_fee' => $serviceFee,
                'total_amount' => $totalAmount,
                'payment_type' => $data['payment_type'],
                'amount_paid' => $amountPaid,
                'amount_due' => $amountDue,
                'payment_status' => 'pending',
                'status' => 'pending',
                'special_requests' => $data['special_requests'] ?? null,
            ]);

            // Load relationships before redirecting
            $booking->load(['hostel', 'roomType']);

            // Redirect to payment
            return redirect()->route('bookings.payment', $booking)
                ->with('success', 'Booking created successfully! Please complete your payment.');
        });
    }

    /**
     * Display the specified booking
     */
    public function show(Booking $booking)
    {
        $this->authorize('view', $booking);

        $booking->load(['user', 'hostel.owner', 'roomType', 'room']);

        $user = auth()->user();
        if ($user->isHostelAdmin()) {
            $hostelIds = $user->hostels()->pluck('id');
            if (!$hostelIds->contains($booking->hostel_id)) {
                abort(403);
            }
        }

        return Inertia::render('bookings/show', [
            'booking' => $booking,
            'can_edit' => auth()->user()->can('update', $booking) && $booking->status === 'pending',
            'can_cancel' => ($booking->status === 'pending' || $booking->status === 'confirmed') && $booking->payment_status !== 'paid',
        ]);
    }

    /**
     * Show the form for editing the specified booking
     */
    public function edit(Booking $booking)
    {
        $this->authorize('update', $booking);

        // Prevent editing of confirmed bookings
        if ($booking->status !== 'pending') {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'Only pending bookings can be edited. Contact support if you need to make changes.');
        }

        $booking->load(['hostel', 'roomType', 'roomType.rooms']);

        return Inertia::render('bookings/edit', [
            'booking' => $booking,
            'available_rooms' => $booking->roomType->rooms()->where('status', 'available')->get(),
        ]);
    }

    /**
     * Update the specified booking
     */
    public function update(UpdateBookingRequest $request, Booking $booking)
    {
        $this->authorize('update', $booking);

        // Prevent updating of confirmed bookings
        if ($booking->status !== 'pending') {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'Only pending bookings can be updated. Contact support if you need to make changes.');
        }

        return DB::transaction(function () use ($request, $booking) {
            $data = $request->validated();
            $originalStatus = $booking->status;

            $booking->update($data);

            // Handle status changes
            if (isset($data['status']) && $data['status'] !== $originalStatus) {
                $this->handleStatusChange($booking, $originalStatus, $data['status']);
            }

            // Handle room assignment
            if (isset($data['room_id'])) {
                $this->handleRoomAssignment($booking, $data['room_id']);
            }

            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking updated successfully!');
        });
    }

    /**
     * Cancel a booking
     */
    public function cancel(Request $request, Booking $booking)
    {
        $this->authorize('update', $booking);

        $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return redirect()->back()
                ->with('error', 'This booking cannot be cancelled.');
        }

        // Prevent cancellation of paid bookings
        if ($booking->payment_status === 'paid') {
            return redirect()->back()
                ->with('error', 'Paid bookings cannot be cancelled. Please contact support for assistance.');
        }

        $booking->cancel($request->cancellation_reason);

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Booking cancelled successfully.');
    }

    /**
     * Confirm a booking (for hostel owners)
     */
    public function confirm(Booking $booking)
    {
        $this->authorize('update', $booking);

        if ($booking->status !== 'pending') {
            return redirect()->back()
                ->with('error', 'Only pending bookings can be confirmed.');
        }

        if ($booking->payment_status !== 'paid' && $booking->payment_status !== 'partially_paid') {
            return redirect()->back()
                ->with('error', 'Payment must be completed before confirmation.');
        }

        $booking->confirm();

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Booking confirmed successfully!');
    }

    /**
     * Check in a guest
     */
    public function checkIn(Booking $booking)
    {
        $this->authorize('update', $booking);

        if ($booking->status !== 'confirmed') {
            return redirect()->back()
                ->with('error', 'Only confirmed bookings can be checked in.');
        }

        $booking->checkIn();

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Guest checked in successfully!');
    }

    /**
     * Check out a guest
     */
    public function checkOut(Booking $booking)
    {
        $this->authorize('update', $booking);

        if ($booking->status !== 'checked_in') {
            return redirect()->back()
                ->with('error', 'Only checked-in guests can be checked out.');
        }

        $booking->checkOut();

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Guest checked out successfully!');
    }

    /**
     * Show payment page
     */
    public function payment(Booking $booking)
    {
        $this->authorize('view', $booking);

        if ($booking->payment_status === 'paid') {
            return redirect()->route('bookings.show', $booking)
                ->with('info', 'This booking has already been paid.');
        }

        $booking->load(['hostel', 'roomType', 'user', 'room']);

        // Debug logging
        \Log::info('Payment page data:', [
            'booking_id' => $booking->id,
            'booking_reference' => $booking->booking_reference,
            'hostel_loaded' => $booking->hostel ? true : false,
            'hostel_name' => $booking->hostel?->name,
            'roomType_loaded' => $booking->roomType ? true : false,
            'roomType_name' => $booking->roomType?->name,
            'user_loaded' => $booking->user ? true : false,
            'user_email' => $booking->user?->email,
            'semester' => $booking->semester,
            'academic_year' => $booking->academic_year,
            'students' => $booking->students,
            'total_amount' => $booking->total_amount,
            'amount_due' => $booking->amount_due,
            'amount_paid' => $booking->amount_paid,
            'payment_type' => $booking->payment_type,
        ]);

        return Inertia::render('bookings/payment', [
            'booking' => $booking,
            'payment_amount' => $booking->payment_type === 'deposit' 
                ? ($booking->total_amount - $booking->amount_paid)
                : ($booking->amount_due > 0 ? $booking->amount_due : $booking->total_amount),
        ]);
    }

    /**
     * Get booking analytics for dashboard
     */
    public function analytics(Request $request)
    {
        $user = auth()->user();
        
        // Base query for filtering
        $baseQuery = Booking::query();
        
        // Filter based on user role
        if ($user->isHostelAdmin()) {
            $baseQuery->whereHas('hostel', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            });
        } elseif ($user->isStudent()) {
            $baseQuery->where('user_id', $user->id);
        }

        $stats = [
            'total_bookings' => (clone $baseQuery)->count(),
            'confirmed_bookings' => (clone $baseQuery)->where('status', 'confirmed')->count(),
            'pending_bookings' => (clone $baseQuery)->where('status', 'pending')->count(),
            'cancelled_bookings' => (clone $baseQuery)->where('status', 'cancelled')->count(),
            'total_revenue' => (clone $baseQuery)->where('payment_status', 'paid')->sum('total_amount'),
            'this_month_bookings' => (clone $baseQuery)->whereMonth('created_at', now()->month)->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Find an available room for a user based on gender allocation
     */
    private function findAvailableRoom($roomType, $user, $academicYear, $semester)
    {
        // Get booked room capacity
        $bookedRoomCapacity = Booking::where('room_type_id', $roomType->id)
            ->where('academic_year', $academicYear)
            ->where(function ($query) use ($semester) {
                if ($semester === 'full_year') {
                    $query->whereIn('semester', ['first', 'second', 'full_year']);
                } else {
                    $query->whereIn('semester', [$semester, 'full_year']);
                }
            })
            ->whereIn('status', ['confirmed', 'checked_in', 'pending'])
            ->groupBy('room_id')
            ->select('room_id', DB::raw('SUM(students) as total_students'))
            ->get()
            ->pluck('total_students', 'room_id');

        // Find available rooms based on gender allocation
        $availableRooms = Room::where('room_type_id', $roomType->id)
            ->where('status', 'available')
            ->where(function ($query) use ($user) {
                // Filter rooms by gender allocation
                if ($user && $user->gender) {
                    $query->where('gender_allocated', $user->gender)
                          ->orWhere('gender_allocated', 'mixed');
                } else {
                    // If no user gender, only show mixed rooms
                    $query->where('gender_allocated', 'mixed');
                }
            })
            ->get()
            ->filter(function ($room) use ($bookedRoomCapacity) {
                $bookedCount = $bookedRoomCapacity->get($room->id, 0);
                return $room->capacity > $bookedCount;
            });

        // Return a random available room, or null if none available
        return $availableRooms->count() > 0 ? $availableRooms->random() : null;
    }

    /**
     * Handle status changes
     */
    private function handleStatusChange(Booking $booking, string $oldStatus, string $newStatus)
    {
        switch ($newStatus) {
            case 'confirmed':
                $booking->update(['confirmed_at' => now()]);
                break;
            case 'checked_in':
                $booking->update(['checked_in_at' => now()]);
                if ($booking->room) {
                    $booking->room->markAsOccupied();
                }
                break;
            case 'checked_out':
                $booking->update(['checked_out_at' => now()]);
                if ($booking->room) {
                    $booking->room->markAsAvailable();
                }
                break;
            case 'cancelled':
                $booking->update(['cancelled_at' => now()]);
                if ($booking->room) {
                    $booking->room->markAsAvailable();
                }
                break;
        }
    }

    /**
     * Handle room assignment
     */
    private function handleRoomAssignment(Booking $booking, ?string $roomId)
    {
        if ($booking->room_id && $booking->room_id !== $roomId) {
            // Release the old room
            if ($booking->room && $booking->status === 'checked_in') {
                $booking->room->markAsAvailable();
            }
        }

        if ($roomId) {
            $newRoom = Room::find($roomId);
            if ($newRoom && $booking->status === 'checked_in') {
                $newRoom->markAsOccupied();
            }
        }
    }
}
