<?php

namespace App\Http\Controllers;

use App\Helpers\ImageHelper;
use App\Http\Requests\StoreHostelRequest;
use App\Http\Requests\UpdateHostelRequest;
use App\Models\Hostel;
use App\Services\PhotoUploadService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class HostelController extends Controller
{
    use AuthorizesRequests;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        $hostels = Hostel::query()
            ->when($user && $user->isHostelAdmin(), function ($query) use ($user) {
                $query->where('owner_id', $user->id);
            })
            ->when(!$user || $user->isGuest(), function ($query) {
                $query->active()->verified();
            })
            ->with(['owner', 'activeRoomTypes'])
            ->withCount('bookings')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        // Transform photos to full URLs for all hostels
        $hostels->getCollection()->transform(function ($hostel) {
            if ($hostel->photos) {
                $hostel->photos = array_map(function ($photo) {
                    return ImageHelper::getImageUrl($photo);
                }, $hostel->photos);
            }
            return $hostel;
        });

        return Inertia::render('hostels/index', [
            'hostels' => $hostels,
            'can_create' => $user && $user->isHostelAdmin(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Hostel::class);
        
        return Inertia::render('hostels/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreHostelRequest $request, PhotoUploadService $photoService)
    {
        $data = $request->validated();
        
        // Handle photo uploads with R2 and multiple sizes
        if ($request->hasFile('photos')) {
            try {
                $uploadedPhotos = $photoService->uploadPhotos(
                    $request->file('photos'), 
                    'hostels'
                );
                $data['photos'] = $uploadedPhotos;
            } catch (\Exception $e) {
                Log::error('Photo upload failed: ' . $e->getMessage());
                return back()->withErrors(['photos' => 'Failed to upload photos. Please try again.']);
            }
        }

        // Set the owner
        $data['owner_id'] = auth()->id();
        
        $hostel = Hostel::create($data);

        return redirect()->route('hostels.show', $hostel)
            ->with('success', 'Hostel created successfully! You can now add room types.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Hostel $hostel)
    {
        $this->authorize('view', $hostel);

        $hostel->load([
            'owner',
            'activeRoomTypes',
            'bookings' => function ($query) {
                $query->active()->latest()->take(5);
            }
        ]);

        // Transform photos to full URLs for proper display
        $hostelData = $hostel->toArray();
        if ($hostel->photos) {
            $hostelData['photos'] = array_map(function ($photo) {
                return ImageHelper::getImageUrl($photo);
            }, $hostel->photos);
        }

        $canEdit = auth()->user() && auth()->user()->id === $hostel->owner_id;

        return Inertia::render('hostels/show', [
            'hostel' => $hostelData,
            'can_edit' => $canEdit,
            'room_types' => $hostel->activeRoomTypes,
            'recent_bookings' => $hostel->bookings,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Hostel $hostel)
    {
        $this->authorize('update', $hostel);
        $hostel->load('roomTypes');

        // Photos are now handled by the OptimizedImage component
        // No need to transform here as it handles both legacy and R2 formats

        return Inertia::render('hostels/edit', [
            'hostel' => $hostel,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateHostelRequest $request, Hostel $hostel, PhotoUploadService $photoService)
    {
        $data = $request->validated();
        
        // Handle photo uploads with R2 and multiple sizes
        if ($request->hasFile('photos')) {
            try {
                // Delete old photos from R2
                if ($hostel->photos) {
                    $photoService->deletePhotos($hostel->photos);
                }
                
                // Upload new photos to R2
                $uploadedPhotos = $photoService->uploadPhotos(
                    $request->file('photos'), 
                    'hostels'
                );
                $data['photos'] = $uploadedPhotos;
            } catch (\Exception $e) {
                Log::error('Photo upload failed during update: ' . $e->getMessage());
                return back()->withErrors(['photos' => 'Failed to upload photos. Please try again.']);
            }
        }

        $hostel->update($data);

        return redirect()->route('hostels.show', $hostel)
            ->with('success', 'Hostel updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Hostel $hostel, PhotoUploadService $photoService)
    {
        $this->authorize('delete', $hostel);

        // Check if hostel has active bookings
        if ($hostel->bookings()->active()->exists()) {
            return redirect()->back()
                ->with('error', 'Cannot delete hostel with active bookings.');
        }

        // Delete photos from R2
        if ($hostel->photos) {
            try {
                $photoService->deletePhotos($hostel->photos);
            } catch (\Exception $e) {
                Log::warning('Failed to delete photos during hostel deletion: ' . $e->getMessage());
                // Continue with hostel deletion even if photo cleanup fails
            }
        }

        $hostel->delete();

        return redirect()->route('hostels.index')
            ->with('success', 'Hostel deleted successfully.');
    }

    /**
     * Toggle hostel active status
     */
    public function toggleStatus(Hostel $hostel)
    {
        $this->authorize('update', $hostel);

        $hostel->update([
            'is_active' => !$hostel->is_active
        ]);

        $status = $hostel->is_active ? 'activated' : 'deactivated';
        
        return redirect()->back()
            ->with('success', "Hostel {$status} successfully.");
    }

    /**
     * Get hostels for API (public endpoint)
     */
    public function api(Request $request)
    {
        $query = Hostel::active()->verified()
            ->with(['activeRoomTypes']);

        // Search filters
        if ($request->has('city')) {
            $query->inCity($request->city);
        }

        if ($request->has('guests')) {
            $query->whereHas('activeRoomTypes', function ($q) use ($request) {
                $q->minCapacity($request->guests);
            });
        }

        if ($request->has('min_price')) {
            $query->whereHas('activeRoomTypes', function ($q) use ($request) {
                $q->where('price_per_night', '>=', $request->min_price);
            });
        }

        if ($request->has('max_price')) {
            $query->whereHas('activeRoomTypes', function ($q) use ($request) {
                $q->where('price_per_night', '<=', $request->max_price);
            });
        }

        $hostels = $query->paginate(12);

        return response()->json($hostels);
    }


}
