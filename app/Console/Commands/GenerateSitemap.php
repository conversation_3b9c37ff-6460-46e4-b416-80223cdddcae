<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>tie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;
use App\Models\Hostel;
use App\Models\University;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-sitemap';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sitemap = Sitemap::create()
            // Static URLs
            ->add(Url::create('/'))
            ->add(Url::create('/hostels'))
            ->add(Url::create('/bookings'))
            ->add(Url::create('/universities'));

        // Dynamic Hostel URLs
        foreach (Hostel::all() as $hostel) {
            $sitemap->add(Url::create("/hostels/{$hostel->id}"));
        }

        // Dynamic University URLs
        foreach (University::all() as $university) {
            $sitemap->add(Url::create("/universities/{$university->id}"));
        }

        $sitemap->writeToFile(public_path('sitemap.xml'));

        $this->info('Sitemap generated successfully!');
    }
}
