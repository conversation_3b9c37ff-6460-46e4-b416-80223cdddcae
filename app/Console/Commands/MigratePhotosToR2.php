<?php

namespace App\Console\Commands;

use App\Models\Hostel;
use App\Services\PhotoUploadService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Symfony\Component\HttpFoundation\File\File;

class MigratePhotosToR2 extends Command
{
    protected $signature = 'photos:migrate-to-r2 {--dry-run : Show what would be migrated without actually doing it}';
    protected $description = 'Migrate existing hostel photos from local storage to Cloudflare R2';

    public function handle(PhotoUploadService $photoService): int
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No actual changes will be made');
        }

        $this->info('🚀 Starting photo migration to Cloudflare R2...');

        // Get hostels with photos that are still in old format (string arrays)
        $hostels = Hostel::whereNotNull('photos')
            ->where('photos', '!=', '[]')
            ->get()
            ->filter(function ($hostel) {
                // Check if photos are in old format (array of strings)
                if (!$hostel->photos || empty($hostel->photos)) {
                    return false;
                }
                
                $firstPhoto = $hostel->photos[0];
                return is_string($firstPhoto); // Old format
            });

        if ($hostels->isEmpty()) {
            $this->info('✅ No hostels found with old format photos to migrate.');
            return 0;
        }

        $this->info("📊 Found {$hostels->count()} hostels with photos to migrate");

        $bar = $this->output->createProgressBar($hostels->count());
        $bar->start();

        $migrated = 0;
        $failed = 0;

        foreach ($hostels as $hostel) {
            try {
                $this->line("\n🏨 Processing hostel: {$hostel->name}");
                
                $oldPhotos = $hostel->photos;
                $newPhotos = [];
                
                foreach ($oldPhotos as $index => $photoPath) {
                    $this->line("  📸 Processing photo " . ($index + 1) . ": {$photoPath}");
                    
                    // Check if file exists in local storage
                    $fullPath = storage_path("app/public/{$photoPath}");
                    
                    if (!file_exists($fullPath)) {
                        $this->warn("    ⚠️  File not found: {$fullPath}");
                        continue;
                    }

                    if ($isDryRun) {
                        $this->line("    ✓ Would migrate: {$photoPath}");
                        continue;
                    }

                    try {
                        // Create a temporary UploadedFile from the existing file
                        $file = new File($fullPath);
                        $uploadedFile = new UploadedFile(
                            $file->getPathname(),
                            $file->getFilename(),
                            $file->getMimeType(),
                            null,
                            true
                        );

                        // Upload to R2 with multiple sizes
                        $photoData = $photoService->uploadSinglePhoto($uploadedFile, 'hostels');
                        
                        if ($photoData) {
                            $newPhotos[] = $photoData;
                            $this->line("    ✅ Migrated successfully");
                        } else {
                            $this->warn("    ❌ Failed to upload to R2");
                        }

                    } catch (\Exception $e) {
                        $this->error("    ❌ Error processing photo: " . $e->getMessage());
                        Log::error("Photo migration error for {$hostel->name}: " . $e->getMessage());
                    }
                }

                if (!$isDryRun && !empty($newPhotos)) {
                    // Update hostel with new photo data
                    $hostel->update(['photos' => $newPhotos]);
                    
                    // Optionally delete old files (commented out for safety)
                    // foreach ($oldPhotos as $photoPath) {
                    //     Storage::disk('public')->delete($photoPath);
                    // }
                    
                    $migrated++;
                    $this->line("  ✅ Hostel updated with " . count($newPhotos) . " photos");
                } elseif ($isDryRun) {
                    $this->line("  ✓ Would update hostel with new photo format");
                }

            } catch (\Exception $e) {
                $failed++;
                $this->error("❌ Failed to migrate hostel {$hostel->name}: " . $e->getMessage());
                Log::error("Hostel migration error: " . $e->getMessage());
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        if ($isDryRun) {
            $this->info("🔍 DRY RUN COMPLETE");
            $this->info("📊 Would migrate {$hostels->count()} hostels");
        } else {
            $this->info("✅ MIGRATION COMPLETE");
            $this->info("📊 Successfully migrated: {$migrated} hostels");
            
            if ($failed > 0) {
                $this->warn("⚠️  Failed migrations: {$failed} hostels");
            }
        }

        $this->newLine();
        $this->info("💡 Note: Old photo files are preserved for safety. You can manually delete them after verifying the migration.");
        
        return 0;
    }
}