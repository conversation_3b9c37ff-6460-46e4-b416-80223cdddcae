<?php

namespace App\Policies;

use App\Models\Hostel;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class HostelPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // Anyone can view listings
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(?User $user, Hostel $hostel): bool
    {
        // Super admins can view all hostels
        if ($user && $user->isSuperAdmin()) {
            return true;
        }

        // Hostel admins can view their own hostels
        if ($user && $user->isHostelAdmin() && $hostel->owner_id === $user->id) {
            return true;
        }

        // Everyone (including guests) can view active and verified hostels
        return $hostel->is_active && $hostel->is_verified;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isHostelAdmin() || $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Hostel $hostel): bool
    {
        // Super admins can update any hostel
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->isHostelAdmin() && $hostel->owner_id === $user->id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Hostel $hostel): bool
    {
        // Super admins can delete any hostel
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->isHostelAdmin() &&
               $hostel->owner_id === $user->id &&
               !$hostel->bookings()->active()->exists();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Hostel $hostel): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Hostel $hostel): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can verify the hostel.
     */
    public function verify(User $user, Hostel $hostel): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can manage the hostel (view bookings, etc.)
     */
    public function manage(User $user, Hostel $hostel): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->isHostelAdmin() && $hostel->owner_id === $user->id;
    }
}
