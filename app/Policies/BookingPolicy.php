<?php

namespace App\Policies;

use App\Models\Booking;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class BookingPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // Users can view their own bookings, owners can view hostel bookings
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Booking $booking): bool
    {
        // Super admins can view all bookings
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Students can view their own bookings
        if ($user->isStudent() && $booking->user_id === $user->id) {
            return true;
        }

        // Hostel admins can view bookings for their hostels
        if ($user->isHostelAdmin() && $booking->hostel->owner_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isStudent() || $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Booking $booking): bool
    {
        // Super admins can update any booking
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Students can update their own pending bookings
        if ($user->isStudent() && 
            $booking->user_id === $user->id && 
            $booking->status === 'pending') {
            return true;
        }

        // Hostel admins can update bookings for their hostels
        if ($user->isHostelAdmin() && $booking->hostel->owner_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Booking $booking): bool
    {
        // Super admins can delete any booking
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Students can delete their own pending or confirmed bookings
        if ($user->isStudent() && 
            $booking->user_id === $user->id && 
            in_array($booking->status, ['pending', 'confirmed'])) {
            return true;
        }

        // Hostel admins can delete bookings for their hostels (if not paid)
        if ($user->isHostelAdmin() && 
            $booking->hostel->owner_id === $user->id && 
            $booking->payment_status !== 'paid') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Booking $booking): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Booking $booking): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can cancel the booking.
     */
    public function cancel(User $user, Booking $booking): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        if ($user->isStudent() && $booking->user_id === $user->id) {
            return true;
        }

        return $user->isHostelAdmin() && $booking->hostel->owner_id === $user->id;
    }

    /**
     * Determine whether the user can confirm the booking.
     */
    public function confirm(User $user, Booking $booking): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->isHostelAdmin() && $booking->hostel->owner_id === $user->id;
    }

    /**
     * Determine whether the user can check in/out students.
     */
    public function manageCheckInOut(User $user, Booking $booking): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->isHostelAdmin() && $booking->hostel->owner_id === $user->id;
    }

    /**
     * Determine whether the user can assign rooms.
     */
    public function assignRoom(User $user, Booking $booking): bool
    {
        // Only hostel owners and super admins can assign rooms
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->isHostelAdmin() && $booking->hostel->owner_id === $user->id;
    }

    /**
     * Determine whether the user can manage payment status.
     */
    public function managePayment(User $user, Booking $booking): bool
    {
        // Only super admins can directly manage payment status
        // Payment processing should be handled through payment gateway
        return $user->isSuperAdmin();
    }
}
