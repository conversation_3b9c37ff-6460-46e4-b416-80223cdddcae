<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;

class ImageHelper
{
    /**
     * Get the full URL for an image stored in R2
     */
    public static function getImageUrl(?string $path): ?string
    {
        if (!$path) {
            return null;
        }

        // If it's already a full URL, return as is
        if (str_starts_with($path, 'http')) {
            return $path;
        }

        // Generate R2 URL
        return Storage::disk('r2')->url($path);
    }

    /**
     * Get multiple image URLs
     */
    public static function getImageUrls(?array $paths): array
    {
        if (!$paths) {
            return [];
        }

        return array_map(fn($path) => self::getImageUrl($path), $paths);
    }
}