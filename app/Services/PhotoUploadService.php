<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class PhotoUploadService
{
    private int $maxFileSize = 50 * 1024 * 1024;
    private array $allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    private array $imageSizes = [
        'original' => null,
        'large' => ['width' => 1200, 'height' => 800],
        'medium' => ['width' => 600, 'height' => 400],
        'thumbnail' => ['width' => 300, 'height' => 200],
    ];

    private ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Upload multiple photos with different sizes
     */
    public function uploadPhotos(array $photos, string $folder = 'photos'): array
    {
        $uploadedPhotos = [];

        foreach ($photos as $photo) {
            if ($photo instanceof UploadedFile) {
                try {
                    $photoData = $this->uploadSinglePhoto($photo, $folder);
                    if ($photoData) {
                        $uploadedPhotos[] = $photoData;
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to upload photo: ' . $e->getMessage());
                    // Continue with other photos instead of failing completely
                }
            }
        }

        return $uploadedPhotos;
    }

    /**
     * Upload a single photo with multiple sizes
     */
    public function uploadSinglePhoto(UploadedFile $photo, string $folder = 'photos'): ?array
    {
        // Validate file
        if (!$this->validatePhoto($photo)) {
            return null;
        }

        $photoId = Str::uuid()->toString();
        $extension = $photo->getClientOriginalExtension() ?: 'jpg';
        $originalName = pathinfo($photo->getClientOriginalName(), PATHINFO_FILENAME);
        
        $photoData = [
            'id' => $photoId,
            'original_name' => $originalName,
            'sizes' => []
        ];

        try {
            // Create image instance
            $image = $this->imageManager->read($photo->getPathname());
            
            foreach ($this->imageSizes as $sizeName => $dimensions) {
                $filename = "{$photoId}_{$sizeName}.{$extension}";
                $path = "{$folder}/{$filename}";

                if ($sizeName === 'original') {
                    // Upload original file directly
                    $uploaded = Storage::disk('r2')->put($path, file_get_contents($photo->getPathname()));
                } else {
                    // Resize and upload using Intervention Image v3 API
                    $resizedImage = clone $image;
                    $resizedImage->scale($dimensions['width'], $dimensions['height']);

                    // Convert to string using the new Intervention Image v3 API
                    if ($extension === 'png') {
                        $imageData = $resizedImage->toPng()->toString();
                    } else {
                        $imageData = $resizedImage->toJpeg(85)->toString();
                    }
                    $uploaded = Storage::disk('r2')->put($path, $imageData);
                }

                if ($uploaded) {
                    $photoData['sizes'][$sizeName] = [
                        'path' => $path,
                        'url' => Storage::disk('r2')->url($path),
                        'size' => $dimensions
                    ];
                } else {
                    Log::warning("Failed to upload {$sizeName} size for photo {$photoId}");
                }
            }

            return $photoData;

        } catch (\Exception $e) {
            Log::error('Photo processing failed: ' . $e->getMessage());
            // Clean up any uploaded files
            $this->cleanupFailedUpload($photoData, $folder);
            return null;
        }
    }

    /**
     * Validate uploaded photo
     */
    private function validatePhoto(UploadedFile $photo): bool
    {
        // Check file size
        if ($photo->getSize() > $this->maxFileSize) {
            Log::warning('Photo too large: ' . $photo->getSize() . ' bytes');
            return false;
        }

        // Check mime type
        if (!in_array($photo->getMimeType(), $this->allowedMimeTypes)) {
            Log::warning('Invalid photo type: ' . $photo->getMimeType());
            return false;
        }

        return true;
    }

    /**
     * Clean up files from failed upload
     */
    private function cleanupFailedUpload(array $photoData, string $folder): void
    {
        if (isset($photoData['sizes'])) {
            foreach ($photoData['sizes'] as $sizeData) {
                if (isset($sizeData['path'])) {
                    try {
                        Storage::disk('r2')->delete($sizeData['path']);
                    } catch (\Exception $e) {
                        Log::warning('Failed to cleanup file: ' . $sizeData['path']);
                    }
                }
            }
        }
    }

    /**
     * Delete photos from storage
     */
    public function deletePhotos(array $photos): void
    {
        foreach ($photos as $photo) {
            if (isset($photo['sizes'])) {
                foreach ($photo['sizes'] as $sizeData) {
                    if (isset($sizeData['path'])) {
                        try {
                            Storage::disk('r2')->delete($sizeData['path']);
                        } catch (\Exception $e) {
                            Log::warning('Failed to delete file: ' . $sizeData['path']);
                        }
                    }
                }
            }
        }
    }

    /**
     * Get photo URL for specific size
     */
    public function getPhotoUrl(array $photo, string $size = 'medium'): ?string
    {
        if (!isset($photo['sizes'][$size])) {
            // Fallback to original if requested size doesn't exist
            $size = 'original';
        }

        return $photo['sizes'][$size]['url'] ?? null;
    }

    /**
     * Check if photo has specific size
     */
    public function hasSize(array $photo, string $size): bool
    {
        return isset($photo['sizes'][$size]);
    }
}