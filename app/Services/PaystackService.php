<?php

namespace App\Services;

use App\Models\Booking;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaystackService
{
    private string $secretKey;
    private string $publicKey;
    private string $baseUrl;

    public function __construct()
    {
        $this->secretKey = config('services.paystack.secret_key', env('PAYSTACK_SECRET_KEY'));
        $this->publicKey = config('services.paystack.public_key', env('PAYSTACK_PUBLIC_KEY'));
        $this->baseUrl = 'https://api.paystack.co';
    }

    /**
     * Initialize a payment transaction
     */
    public function initializePayment(Booking $booking): array
    {
        try {
            $amount = $booking->payment_type === 'deposit' 
                ? $booking->total_amount * 0.3 
                : $booking->total_amount;

            // Convert to kobo (Paystack uses kobo for NGN)
            $amountInKobo = (int) ($amount * 100);

            $payload = [
                'email' => $booking->user->email,
                'amount' => $amountInKobo,
                'reference' => $this->generateReference($booking),
                'callback_url' => route('payments.callback'),
                'metadata' => [
                    'booking_id' => $booking->id,
                    'booking_reference' => $booking->booking_reference,
                    'user_id' => $booking->user_id,
                    'hostel_name' => $booking->hostel->name,
                    'payment_type' => $booking->payment_type,
                    'custom_fields' => [
                        [
                            'display_name' => 'Booking Reference',
                            'variable_name' => 'booking_reference',
                            'value' => $booking->booking_reference,
                        ],
                        [
                            'display_name' => 'Academic Year',
                            'variable_name' => 'academic_year',
                            'value' => $booking->academic_year,
                        ],
                        [
                            'display_name' => 'Semester',
                            'variable_name' => 'semester',
                            'value' => ucfirst(str_replace('_', ' ', $booking->semester)),
                        ],
                    ],
                ],
                'channels' => ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer'],
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/transaction/initialize', $payload);

            if (!$response->successful()) {
                Log::error('Paystack payment initialization failed', [
                    'booking_id' => $booking->id,
                    'response' => $response->json(),
                    'status' => $response->status(),
                ]);
                
                throw new \Exception('Payment initialization failed. Please try again.');
            }

            $data = $response->json();

            if (!$data['status']) {
                throw new \Exception($data['message'] ?? 'Payment initialization failed');
            }

            return [
                'status' => true,
                'authorization_url' => $data['data']['authorization_url'],
                'access_code' => $data['data']['access_code'],
                'reference' => $data['data']['reference'],
            ];

        } catch (\Exception $e) {
            Log::error('Paystack payment initialization error', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a payment transaction
     */
    public function verifyPayment(string $reference): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
            ])->get($this->baseUrl . "/transaction/verify/{$reference}");

            if (!$response->successful()) {
                Log::error('Paystack payment verification failed', [
                    'reference' => $reference,
                    'response' => $response->json(),
                    'status' => $response->status(),
                ]);
                
                throw new \Exception('Payment verification failed');
            }

            $data = $response->json();

            if (!$data['status']) {
                throw new \Exception($data['message'] ?? 'Payment verification failed');
            }

            return [
                'status' => true,
                'data' => $data['data'],
            ];

        } catch (\Exception $e) {
            Log::error('Paystack payment verification error', [
                'reference' => $reference,
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process webhook event
     */
    public function processWebhook(array $payload): bool
    {
        try {
            $event = $payload['event'];
            $data = $payload['data'];

            switch ($event) {
                case 'charge.success':
                    return $this->handleSuccessfulPayment($data);
                    
                case 'charge.failed':
                    return $this->handleFailedPayment($data);
                    
                case 'charge.dispute':
                    return $this->handlePaymentDispute($data);
                    
                default:
                    Log::info('Unhandled Paystack webhook event', [
                        'event' => $event,
                        'data' => $data,
                    ]);
                    return true;
            }

        } catch (\Exception $e) {
            Log::error('Paystack webhook processing error', [
                'payload' => $payload,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle successful payment
     */
    private function handleSuccessfulPayment(array $data): bool
    {
        try {
            $bookingId = $data['metadata']['booking_id'] ?? null;
            
            if (!$bookingId) {
                Log::warning('Booking ID not found in payment metadata', $data);
                return false;
            }

            $booking = Booking::find($bookingId);
            
            if (!$booking) {
                Log::warning('Booking not found for successful payment', [
                    'booking_id' => $bookingId,
                    'reference' => $data['reference'],
                ]);
                return false;
            }

            $amountPaid = $data['amount'] / 100; // Convert from kobo to naira
            
            $paymentStatus = $booking->payment_type === 'deposit' && $amountPaid < $booking->total_amount 
                ? 'partially_paid' 
                : 'paid';
            
            $bookingStatus = $paymentStatus === 'paid' ? 'confirmed' : 'pending';
            
            // Update booking payment and status
            $booking->update([
                'payment_reference' => $data['reference'],
                'payment_data' => $data,
                'amount_paid' => $amountPaid,
                'payment_status' => $paymentStatus,
                'status' => $bookingStatus,
                'amount_due' => max(0, $booking->total_amount - $amountPaid),
                'confirmed_at' => $paymentStatus === 'paid' ? now() : null,
            ]);

            Log::info('Payment processed successfully', [
                'booking_id' => $booking->id,
                'booking_reference' => $booking->booking_reference,
                'amount_paid' => $amountPaid,
                'payment_status' => $booking->payment_status,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Error handling successful payment', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle failed payment
     */
    private function handleFailedPayment(array $data): bool
    {
        try {
            $bookingId = $data['metadata']['booking_id'] ?? null;
            
            if (!$bookingId) {
                return false;
            }

            $booking = Booking::find($bookingId);
            
            if (!$booking) {
                return false;
            }

            $booking->update([
                'payment_reference' => $data['reference'],
                'payment_data' => $data,
                'payment_status' => 'failed',
            ]);

            Log::info('Payment failed', [
                'booking_id' => $booking->id,
                'booking_reference' => $booking->booking_reference,
                'reason' => $data['gateway_response'] ?? 'Unknown',
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Error handling failed payment', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle payment dispute
     */
    private function handlePaymentDispute(array $data): bool
    {
        // Log dispute for manual review
        Log::warning('Payment dispute received', [
            'dispute_data' => $data,
        ]);

        // Additional dispute handling logic can be added here
        return true;
    }

    /**
     * Generate unique payment reference
     */
    private function generateReference(Booking $booking): string
    {
        return 'HST_' . $booking->booking_reference . '_' . time();
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        $computedSignature = hash_hmac('sha512', $payload, $this->secretKey);
        return hash_equals($signature, $computedSignature);
    }

    /**
     * Get public key for frontend
     */
    public function getPublicKey(): string
    {
        return $this->publicKey;
    }

    /**
     * Refund a payment
     */
    public function refundPayment(string $reference, ?float $amount = null): array
    {
        try {
            $payload = ['transaction' => $reference];
            
            if ($amount) {
                $payload['amount'] = (int) ($amount * 100); // Convert to kobo
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/refund', $payload);

            if (!$response->successful()) {
                throw new \Exception('Refund request failed');
            }

            $data = $response->json();

            if (!$data['status']) {
                throw new \Exception($data['message'] ?? 'Refund failed');
            }

            return [
                'status' => true,
                'data' => $data['data'],
            ];

        } catch (\Exception $e) {
            Log::error('Paystack refund error', [
                'reference' => $reference,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => false,
                'message' => $e->getMessage(),
            ];
        }
    }
} 