<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RoomType extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'hostel_id',
        'name',
        'description',
        'category',
        'gender_type',
        'semester_type',
        'capacity',
        'max_occupancy',
        'beds',
        'available_rooms',
        'amenities',
        'photos',
        'is_active',
        'male_rooms',
        'female_rooms',
        'mixed_rooms',
    ];

    protected $appends = [
        'available_beds',
        'total_beds',
    ];

    protected function casts(): array
    {
        return [
            'amenities' => 'array',
            'photos' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the hostel this room type belongs to
     */
    public function hostel()
    {
        return $this->belongsTo(Hostel::class);
    }

    /**
     * Get individual rooms of this type
     */
    public function rooms()
    {
        return $this->hasMany(Room::class);
    }

    /**
     * Get available rooms of this type
     */
    public function availableRooms()
    {
        return $this->hasMany(Room::class)->where('status', 'available');
    }

    /**
     * Get bookings for this room type
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Check availability for given semester and academic year
     */
    public function checkAvailability($academicYear, $semester, $students = 1)
    {
        $overlappingBookings = $this->bookings()
            ->where('academic_year', $academicYear)
            ->where(function ($query) use ($semester) {
                if ($semester === 'full_year') {
                    // Full year conflicts with any semester in same academic year
                    $query->whereIn('semester', ['first', 'second', 'full_year']);
                } else {
                    // Specific semester conflicts with same semester or full year
                    $query->whereIn('semester', [$semester, 'full_year']);
                }
            })
            ->whereIn('status', ['confirmed', 'moved_in', 'active'])
            ->count();

        return $this->available_rooms - $overlappingBookings;
    }

    /**
     * Check semester availability - alias for checkAvailability method
     */
    public function checkSemesterAvailability($academicYear, $semester, $students = 1)
    {
        return $this->checkAvailability($academicYear, $semester, $students);
    }

    /**
     * Scope to filter by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to filter active room types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by capacity
     */
    public function scopeMinCapacity($query, $students)
    {
        return $query->where('capacity', '>=', $students);
    }

    /**
     * Scope to filter by gender type
     */
    public function scopeByGender($query, $gender)
    {
        return $query->where('gender_type', $gender)->orWhere('gender_type', 'mixed');
    }

    /**
     * Scope to filter by semester type
     */
    public function scopeBySemester($query, $semester)
    {
        return $query->where('semester_type', $semester)->orWhere('semester_type', 'both');
    }

    /**
     * Get available beds for this room type
     */
    public function getAvailableBedsCount()
    {
        return $this->rooms()->with('beds')->get()
            ->flatMap->beds
            ->where('status', 'available')
            ->count();
    }

    /**
     * Get available beds attribute
     */
    public function getAvailableBedsAttribute()
    {
        // For simplicity, calculate based on room capacity and available rooms
        // In a real system, this would check actual bed availability
        return $this->available_rooms * $this->capacity;
    }

    /**
     * Get total beds attribute
     */
    public function getTotalBedsAttribute()
    {
        return $this->available_rooms * $this->capacity;
    }
}
