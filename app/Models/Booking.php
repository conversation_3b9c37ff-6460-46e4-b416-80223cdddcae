<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Booking extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'booking_reference',
        'user_id',
        'hostel_id',
        'room_type_id',
        'room_id',
        'bed_id',
        'semester',
        'academic_year',
        'students',
        'price_per_semester',
        'subtotal',
        'charges',
        'service_fee',
        'total_amount',
        'payment_type',
        'amount_paid',
        'amount_due',
        'payment_status',
        'payment_reference',
        'payment_data',
        'status',
        'special_requests',
        'cancellation_reason',
        'cancelled_at',
        'confirmed_at',
        'checked_in_at',
        'checked_out_at',
    ];

    protected function casts(): array
    {
        return [
            'price_per_semester' => 'decimal:2',
            'subtotal' => 'decimal:2',
            'charges' => 'decimal:2',
            'service_fee' => 'decimal:2',
            'total_amount' => 'decimal:2',
            'amount_paid' => 'decimal:2',
            'amount_due' => 'decimal:2',
            'payment_data' => 'array',
            'cancelled_at' => 'datetime',
            'confirmed_at' => 'datetime',
            'checked_in_at' => 'datetime',
            'checked_out_at' => 'datetime',
        ];
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (empty($booking->booking_reference)) {
                $booking->booking_reference = $booking->generateBookingReference();
            }
        });
    }

    /**
     * Generate unique booking reference
     */
    public function generateBookingReference()
    {
        do {
            $reference = 'HST' . strtoupper(Str::random(6));
        } while (static::where('booking_reference', $reference)->exists());

        return $reference;
    }

    /**
     * Get the student who made this booking
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the hostel for this booking
     */
    public function hostel()
    {
        return $this->belongsTo(Hostel::class);
    }

    /**
     * Get the room type for this booking
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class);
    }

    /**
     * Get the assigned room for this booking
     */
    public function room()
    {
        return $this->belongsTo(Room::class);
    }

    /**
     * Get the assigned bed for this booking
     */
    public function bed()
    {
        return $this->belongsTo(Bed::class);
    }

    /**
     * Check if booking is confirmed
     */
    public function isConfirmed()
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if student is moved in
     */
    public function isMovedIn()
    {
        return $this->status === 'moved_in';
    }

    /**
     * Check if booking is active (student currently living in)
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if booking is cancelled
     */
    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if payment is complete
     */
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Confirm the booking
     */
    public function confirm()
    {
        $this->update([
            'status' => 'confirmed',
            'confirmed_at' => now(),
        ]);
    }

    /**
     * Move in the student
     */
    public function moveIn()
    {
        $this->update([
            'status' => 'moved_in',
            'checked_in_at' => now(),
        ]);

        // Mark bed as occupied if assigned
        if ($this->bed) {
            $this->bed->assignStudent($this->user);
        }

        // Mark room as occupied if assigned
        if ($this->room) {
            $this->room->markAsOccupied();
        }
    }

    /**
     * Mark as active (living in)
     */
    public function markActive()
    {
        $this->update([
            'status' => 'active',
        ]);
    }

    /**
     * Move out the student
     */
    public function moveOut()
    {
        $this->update([
            'status' => 'moved_out',
            'checked_out_at' => now(),
        ]);

        // Release bed
        if ($this->bed) {
            $this->bed->releaseStudent();
        }

        // Mark room as available if no other students
        if ($this->room && !$this->room->beds()->occupied()->exists()) {
            $this->room->markAsAvailable();
        }
    }

    /**
     * Cancel the booking
     */
    public function cancel($reason = null)
    {
        $this->update([
            'status' => 'cancelled',
            'cancellation_reason' => $reason,
            'cancelled_at' => now(),
        ]);

        // Release bed if assigned
        if ($this->bed) {
            $this->bed->releaseStudent();
        }

        // Mark room as available if no other students
        if ($this->room && !$this->room->beds()->occupied()->exists()) {
            $this->room->markAsAvailable();
        }
    }

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter confirmed bookings
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope to filter active bookings (confirmed + moved_in + active)
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['confirmed', 'moved_in', 'active']);
    }

    /**
     * Scope to filter by semester and academic year
     */
    public function scopeForSemester($query, $semester, $academicYear)
    {
        return $query->where('semester', $semester)
                    ->where('academic_year', $academicYear);
    }

    /**
     * Scope to filter by academic year
     */
    public function scopeForAcademicYear($query, $academicYear)
    {
        return $query->where('academic_year', $academicYear);
    }
}
