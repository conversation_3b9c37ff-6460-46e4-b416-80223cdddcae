<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Hostel extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'owner_id',
        'name',
        'description',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'latitude',
        'longitude',
        'phone',
        'email',
        'website',
        'amenities',
        'photos',
        'house_rules',
        'cancellation_policy',
        'is_active',
        'is_verified',
        'allow_deposit',
        'is_per_semester',
        'average_rating',
        'total_reviews',
        'payment_period',
        'hostel_type',
        'youtube_links',
        'base_price',
        'service_fee',
        'deposit_percentage',
        'cancellation_fee',
    ];

    protected function casts(): array
    {
        return [
            'amenities' => 'array',
            'photos' => 'array',
            'youtube_links' => 'array',
            'is_active' => 'boolean',
            'is_verified' => 'boolean',
            'allow_deposit' => 'boolean',
            'is_per_semester' => 'boolean',
            'latitude' => 'decimal:6',
            'longitude' => 'decimal:6',
            'average_rating' => 'decimal:2',
            'base_price' => 'decimal:2',
            'service_fee' => 'decimal:2',
            'deposit_percentage' => 'decimal:2',
            'cancellation_fee' => 'decimal:2',
        ];
    }

    /**
     * Get the photos attribute with proper URLs
     */
    protected function getPhotosAttribute($value): array
    {
        $photos = json_decode($value, true) ?? [];
        
        // Convert legacy string paths to full URLs
        return array_map(function ($photo) {
            if (is_string($photo)) {
                return \Storage::url($photo);
            }
            return $photo; // Already in new format with URLs
        }, $photos);
    }

    /**
     * Get the owner of this hostel
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get room types for this hostel
     */
    public function roomTypes()
    {
        return $this->hasMany(RoomType::class);
    }

    /**
     * Get active room types for this hostel
     */
    public function activeRoomTypes()
    {
        return $this->hasMany(RoomType::class)->where('is_active', true);
    }

    /**
     * Get bookings for this hostel
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the minimum price per semester for this hostel (deprecated - use base_price instead)
     */
    public function getMinPriceAttribute()
    {
        // Return hostel base price instead of room type minimum
        return $this->base_price ?? 0;
    }

    /**
     * Get the payment period display text
     */
    public function getPaymentPeriodTextAttribute()
    {
        return match($this->payment_period) {
            'month' => 'Month',
            'semester' => 'Semester', 
            'year' => 'Academic Year',
            default => 'Period'
        };
    }

    /**
     * Get the base price with proper period formatting
     */
    public function getFormattedBasePriceAttribute()
    {
        if (!$this->base_price) return 'Price not set';
        
        return 'GH¢ ' . number_format($this->base_price, 2) . ' per ' . strtolower($this->payment_period_text);
    }

    /**
     * Calculate total price for a booking
     */
    public function calculateBookingPrice($students = 1, $semester = 'first')
    {
        if (!$this->base_price) {
            throw new \Exception('Base price not set for this hostel');
        }

        $basePrice = $this->base_price;
        
        // Apply multipliers based on payment period and semester selection
        if ($this->payment_period === 'month') {
            // For monthly hostels, calculate based on semester length (4-5 months)
            $monthsInSemester = $semester === 'full_year' ? 9 : 4.5;
            $subtotal = $basePrice * $monthsInSemester * $students;
        } elseif ($this->payment_period === 'semester') {
            // Semester-based pricing
            $semesterMultiplier = $semester === 'full_year' ? 1.8 : 1; // Slight discount for full year
            $subtotal = $basePrice * $semesterMultiplier * $students;
        } else { // year
            // Academic year pricing - no additional multiplier needed
            $subtotal = $basePrice * $students;
        }

        $serviceFee = $this->service_fee ?? 0;
        $charges = $subtotal * 0.03; // 3% charges
        $totalAmount = $subtotal + $serviceFee + $charges;

        return [
            'base_price' => $basePrice,
            'subtotal' => $subtotal,
            'service_fee' => $serviceFee,
            'charges' => $charges,
            'total_amount' => $totalAmount,
            'students' => $students,
        ];
    }

    /**
     * Scope to filter by city
     */
    public function scopeInCity($query, $city)
    {
        return $query->where('city', 'LIKE', "%{$city}%");
    }

    /**
     * Scope to filter active hostels
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter verified hostels
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    public function university()
    {
        return $this->belongsTo(University::class);
    }

    /**
     * Get optimized photos for a specific size
     */
    public function getOptimizedPhotos(string $size = 'medium'): array
    {
        if (!$this->photos) {
            return [];
        }

        $optimizedPhotos = [];
        
        foreach ($this->photos as $photo) {
            if (is_string($photo)) {
                // Legacy format - convert storage path to URL
                $optimizedPhotos[] = \Storage::url($photo);
            } elseif (is_array($photo) && isset($photo['sizes'])) {
                // New R2 format
                if (isset($photo['sizes'][$size])) {
                    $optimizedPhotos[] = $photo['sizes'][$size]['url'];
                } elseif (isset($photo['sizes']['original'])) {
                    $optimizedPhotos[] = $photo['sizes']['original']['url'];
                }
            }
        }

        return $optimizedPhotos;
    }

    /**
     * Get thumbnail photo URL
     */
    public function getThumbnailAttribute(): ?string
    {
        if (!$this->photos || empty($this->photos)) {
            return null;
        }

        $firstPhoto = $this->photos[0];
        
        if (is_string($firstPhoto)) {
            // Legacy format - convert storage path to URL
            return \Storage::url($firstPhoto);
        } elseif (is_array($firstPhoto) && isset($firstPhoto['sizes'])) {
            // New R2 format
            return $firstPhoto['sizes']['thumbnail']['url'] ?? 
                   $firstPhoto['sizes']['medium']['url'] ?? 
                   $firstPhoto['sizes']['original']['url'] ?? null;
        }

        return null;
    }

    /**
     * Get featured image URL (medium size)
     */
    public function getFeaturedImageAttribute(): ?string
    {
        if (!$this->photos || empty($this->photos)) {
            return null;
        }

        $firstPhoto = $this->photos[0];
        
        if (is_string($firstPhoto)) {
            // Legacy format
            return $firstPhoto;
        } elseif (is_array($firstPhoto) && isset($firstPhoto['sizes'])) {
            // New R2 format
            return $firstPhoto['sizes']['medium']['url'] ?? 
                   $firstPhoto['sizes']['large']['url'] ?? 
                   $firstPhoto['sizes']['original']['url'] ?? null;
        }

        return null;
    }

    /**
     * Get photo URL for specific size
     */
    public function getPhotoUrl(int $index = 0, string $size = 'medium'): ?string
    {
        if (!$this->photos || !isset($this->photos[$index])) {
            return null;
        }

        $photo = $this->photos[$index];
        
        if (is_string($photo)) {
            // Legacy format
            return $photo;
        } elseif (is_array($photo) && isset($photo['sizes'])) {
            // New R2 format
            return $photo['sizes'][$size]['url'] ?? 
                   $photo['sizes']['original']['url'] ?? null;
        }

        return null;
    }

    /**
     * Check if hostel has photos
     */
    public function hasPhotos(): bool
    {
        return !empty($this->photos);
    }

    /**
     * Get total number of photos
     */
    public function getPhotoCountAttribute(): int
    {
        return $this->photos ? count($this->photos) : 0;
    }
}
