<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AcademicYear extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'start_date',
        'end_date',
        'is_active',
        'is_current',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
        'is_current' => 'boolean',
    ];

    /**
     * Get bookings for this academic year
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class, 'academic_year', 'name');
    }

    /**
     * Scope to filter active academic years
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter current academic year
     */
    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    /**
     * Get the current academic year
     */
    public static function getCurrent()
    {
        return static::current()->active()->first();
    }

    /**
     * Get active academic years ordered by start date
     */
    public static function getActive()
    {
        return static::active()->orderBy('start_date', 'desc')->get();
    }

    /**
     * Check if this academic year is current
     */
    public function isCurrent(): bool
    {
        return $this->is_current;
    }

    /**
     * Check if this academic year is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }
} 