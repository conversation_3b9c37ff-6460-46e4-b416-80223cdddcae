<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bed extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'room_id',
        'bed_number',
        'bed_type',
        'status',
        'current_student_id',
        'notes',
    ];

    /**
     * Get the room this bed belongs to
     */
    public function room()
    {
        return $this->belongsTo(Room::class);
    }

    /**
     * Get the current student assigned to this bed
     */
    public function currentStudent()
    {
        return $this->belongsTo(User::class, 'current_student_id');
    }

    /**
     * Get bookings for this bed
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the hostel through room
     */
    public function hostel()
    {
        return $this->room->roomType->hostel();
    }

    /**
     * Check if bed is available
     */
    public function isAvailable()
    {
        return $this->status === 'available';
    }

    /**
     * Check if bed is occupied
     */
    public function isOccupied()
    {
        return $this->status === 'occupied';
    }

    /**
     * Check if bed is reserved
     */
    public function isReserved()
    {
        return $this->status === 'reserved';
    }

    /**
     * Assign student to this bed
     */
    public function assignStudent(User $student)
    {
        $this->update([
            'current_student_id' => $student->id,
            'status' => 'occupied'
        ]);
    }

    /**
     * Release student from this bed
     */
    public function releaseStudent()
    {
        $this->update([
            'current_student_id' => null,
            'status' => 'available'
        ]);
    }

    /**
     * Scope to filter available beds
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    /**
     * Scope to filter occupied beds
     */
    public function scopeOccupied($query)
    {
        return $query->where('status', 'occupied');
    }

    /**
     * Scope to filter by bed type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('bed_type', $type);
    }
} 