<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Room extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'room_type_id',
        'room_number',
        'floor',
        'status',
        'capacity',
        'notes',
        'gender_allocated',
    ];

    /**
     * Get the room type this room belongs to
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class);
    }

    /**
     * Get the hostel through room type
     */
    public function hostel()
    {
        return $this->roomType->hostel();
    }

    /**
     * Get beds in this room
     */
    public function beds()
    {
        return $this->hasMany(Bed::class);
    }

    /**
     * Get available beds in this room
     */
    public function availableBeds()
    {
        return $this->beds()->where('status', 'available');
    }

    /**
     * Get bookings for this specific room
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get current booking if room is occupied
     */
    public function currentBooking()
    {
        return $this->bookings()
            ->where('status', 'checked_in')
            ->where('check_in_date', '<=', now())
            ->where('check_out_date', '>', now())
            ->first();
    }

    /**
     * Check if room is available
     */
    public function isAvailable()
    {
        return $this->status === 'available';
    }

    /**
     * Check if room is occupied
     */
    public function isOccupied()
    {
        return $this->status === 'occupied';
    }

    /**
     * Check if room is under maintenance
     */
    public function isMaintenance()
    {
        return $this->status === 'maintenance';
    }

    /**
     * Mark room as occupied
     */
    public function markAsOccupied()
    {
        $this->update(['status' => 'occupied']);
    }

    /**
     * Mark room as available
     */
    public function markAsAvailable()
    {
        $this->update(['status' => 'available']);
    }

    /**
     * Scope to filter available rooms
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    /**
     * Scope to filter occupied rooms
     */
    public function scopeOccupied($query)
    {
        return $query->where('status', 'occupied');
    }

    /**
     * Scope to filter rooms under maintenance
     */
    public function scopeMaintenance($query)
    {
        return $query->where('status', 'maintenance');
    }
}
