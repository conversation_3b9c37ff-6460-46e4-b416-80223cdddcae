# AVIF Image Optimization Design

## Overview

This design extends the existing Cloudflare R2 photo storage system to include AVIF image conversion, providing significant storage savings (50-80% reduction) while maintaining backward compatibility. The system will store both AVIF and fallback formats, automatically serving the optimal format based on browser support.

## Architecture

### High-Level Flow
```
Upload → Image Processing → AVIF Conversion → Dual Storage → Smart Serving
```

### Component Interaction
```mermaid
graph TD
    A[Photo Upload] --> B[PhotoUploadService]
    B --> C[Image Processing]
    C --> D[AVIF Converter]
    C --> E[Original Format Processor]
    D --> F[R2 Storage - AVIF]
    E --> G[R2 Storage - Fallback]
    H[Browser Request] --> I[OptimizedImage Component]
    I --> J[Browser Support Detection]
    J --> K{AVIF Supported?}
    K -->|Yes| F
    K -->|No| G
```

## Components and Interfaces

### 1. Enhanced PhotoUploadService

**Purpose**: Extend existing service to handle AVIF conversion alongside current functionality.

**Key Methods**:
```php
class PhotoUploadService
{
    // Enhanced to support AVIF conversion
    public function uploadSinglePhoto(UploadedFile $photo, string $folder): ?array
    
    // New method for AVIF conversion
    private function convertToAvif(Image $image, string $quality = '80'): ?string
    
    // Enhanced storage structure
    private function storeImageVariants(Image $image, array $photoData, string $path): array
    
    // New method for format optimization
    private function shouldConvertToAvif(Image $image): bool
}
```

**Enhanced Photo Data Structure**:
```php
[
    'id' => 'uuid',
    'original_name' => 'filename',
    'sizes' => [
        'thumbnail' => [
            'avif' => ['path' => '...', 'url' => '...', 'size' => 15KB],
            'fallback' => ['path' => '...', 'url' => '...', 'size' => 45KB, 'format' => 'jpeg'],
            'dimensions' => ['width' => 300, 'height' => 200]
        ],
        'medium' => [...],
        'large' => [...],
        'original' => [...]
    ],
    'stats' => [
        'total_avif_size' => 120KB,
        'total_fallback_size' => 380KB,
        'savings_percentage' => 68.4
    ]
]
```

### 2. AVIF Conversion Service

**Purpose**: Handle AVIF-specific conversion logic and quality optimization.

```php
class AvifConversionService
{
    private ImageManager $imageManager;
    private int $defaultQuality = 80;
    private array $qualitySettings = [
        'thumbnail' => 75,
        'medium' => 80,
        'large' => 85,
        'original' => 90
    ];
    
    public function convertToAvif(Image $image, string $size = 'medium'): ?string
    public function isAvifSupported(): bool
    public function getOptimalQuality(string $size): int
    public function validateAvifOutput(string $avifData, string $originalData): bool
}
```

### 3. Enhanced OptimizedImage Component

**Purpose**: Automatically detect browser support and serve appropriate format.

```tsx
interface OptimizedImageProps {
    photo: PhotoData | string;
    alt: string;
    size?: 'thumbnail' | 'medium' | 'large' | 'original';
    className?: string;
    loading?: 'lazy' | 'eager';
    preferAvif?: boolean; // New prop for manual control
    onLoad?: () => void;
    onError?: () => void;
}

// Enhanced component with AVIF support detection
const OptimizedImage: React.FC<OptimizedImageProps> = ({...}) => {
    const [supportsAvif, setSupportsAvif] = useState<boolean | null>(null);
    
    // Browser AVIF support detection
    useEffect(() => {
        detectAvifSupport().then(setSupportsAvif);
    }, []);
    
    // Smart URL selection based on support
    const getOptimalImageUrl = () => {
        // Logic to choose AVIF or fallback
    };
}
```

### 4. Browser Support Detection Utility

**Purpose**: Detect AVIF support in browsers efficiently.

```tsx
// utils/imageSupport.ts
export const detectAvifSupport = (): Promise<boolean> => {
    return new Promise((resolve) => {
        const avif = new Image();
        avif.onload = () => resolve(true);
        avif.onerror = () => resolve(false);
        avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
    });
};

// Cache the result to avoid repeated checks
let avifSupportCache: boolean | null = null;

export const getAvifSupport = async (): Promise<boolean> => {
    if (avifSupportCache !== null) return avifSupportCache;
    avifSupportCache = await detectAvifSupport();
    return avifSupportCache;
};
```

## Data Models

### Enhanced Photo Storage Structure

The existing photo data structure will be enhanced to support dual formats:

```php
// Before (current structure)
'sizes' => [
    'thumbnail' => [
        'path' => 'hostels/uuid_thumbnail.jpg',
        'url' => 'https://cdn.example.com/hostels/uuid_thumbnail.jpg',
        'size' => ['width' => 300, 'height' => 200]
    ]
]

// After (AVIF-enhanced structure)
'sizes' => [
    'thumbnail' => [
        'avif' => [
            'path' => 'hostels/uuid_thumbnail.avif',
            'url' => 'https://cdn.example.com/hostels/uuid_thumbnail.avif',
            'file_size' => 15360 // bytes
        ],
        'fallback' => [
            'path' => 'hostels/uuid_thumbnail.jpg',
            'url' => 'https://cdn.example.com/hostels/uuid_thumbnail.jpg',
            'file_size' => 45120, // bytes
            'format' => 'jpeg'
        ],
        'dimensions' => ['width' => 300, 'height' => 200],
        'savings' => 66.2 // percentage saved with AVIF
    ]
]
```

### Database Schema Considerations

No database schema changes required - the enhanced photo data structure fits within the existing JSON column structure.

## Error Handling

### AVIF Conversion Failures
1. **Graceful Degradation**: If AVIF conversion fails, continue with original format
2. **Logging**: Log conversion failures with detailed error information
3. **Retry Logic**: Implement retry mechanism for transient failures
4. **Fallback Strategy**: Always ensure fallback format is available

### Browser Compatibility Issues
1. **Detection Failures**: Default to fallback format if detection fails
2. **Loading Errors**: Implement automatic fallback to original format
3. **Performance Monitoring**: Track format serving success rates

### Storage Issues
1. **Partial Uploads**: Clean up incomplete uploads (both formats)
2. **Storage Limits**: Monitor R2 storage usage and implement alerts
3. **Consistency Checks**: Verify both formats are stored successfully

## Testing Strategy

### Unit Tests
```php
// PhotoUploadServiceTest enhancements
public function test_avif_conversion_success()
public function test_avif_conversion_fallback()
public function test_dual_format_storage()
public function test_storage_cleanup_both_formats()
```

### Integration Tests
```php
// Full upload-to-serve workflow
public function test_complete_avif_workflow()
public function test_browser_compatibility_serving()
public function test_existing_image_compatibility()
```

### Frontend Tests
```tsx
// OptimizedImage component tests
describe('AVIF Support Detection', () => {
  test('detects AVIF support correctly')
  test('falls back to original format when AVIF unsupported')
  test('serves AVIF when supported')
})
```

### Performance Tests
- Conversion time benchmarks
- File size reduction measurements
- Loading performance comparisons
- Storage usage tracking

## Performance Considerations

### Conversion Performance
- **Parallel Processing**: Convert multiple sizes concurrently
- **Quality Optimization**: Use size-specific quality settings
- **Memory Management**: Process images efficiently to avoid memory issues
- **Timeout Handling**: Set reasonable timeouts for conversion operations

### Serving Performance
- **CDN Optimization**: Leverage Cloudflare's edge caching for both formats
- **Header Optimization**: Set appropriate cache headers for both formats
- **Lazy Loading**: Maintain lazy loading capabilities with format detection

### Storage Efficiency
- **Compression Monitoring**: Track actual savings achieved
- **Cleanup Automation**: Automated cleanup of orphaned files
- **Usage Analytics**: Monitor format usage patterns

## Security Considerations

### Input Validation
- Validate image formats before AVIF conversion
- Sanitize file names and paths for both formats
- Implement file size limits for both AVIF and fallback formats

### Access Control
- Maintain existing access control for both image formats
- Ensure consistent permissions across format variants
- Implement secure URL generation for both formats

### Data Integrity
- Verify successful conversion before storing
- Implement checksums for both formats
- Monitor for corruption during conversion process

## Migration Strategy

### Existing Images
1. **Backward Compatibility**: Existing images continue to work unchanged
2. **Gradual Migration**: Provide command to convert existing images to AVIF
3. **Mixed Format Support**: Handle both old and new format structures seamlessly

### Rollout Plan
1. **Phase 1**: Deploy AVIF conversion for new uploads only
2. **Phase 2**: Update frontend components to support format detection
3. **Phase 3**: Migrate existing images (optional, based on storage savings)
4. **Phase 4**: Monitor and optimize based on usage patterns

## Monitoring and Analytics

### Key Metrics
- AVIF conversion success rate
- Average file size reduction percentage
- Browser support adoption rates
- Storage cost savings
- Image loading performance improvements

### Logging Strategy
```php
// Conversion metrics
Log::info('AVIF conversion completed', [
    'original_size' => $originalSize,
    'avif_size' => $avifSize,
    'savings_percentage' => $savings,
    'conversion_time' => $conversionTime
]);

// Serving metrics
Log::info('Image served', [
    'format' => 'avif|fallback',
    'size' => 'thumbnail|medium|large',
    'browser_support' => true|false
]);
```

This design provides a comprehensive approach to AVIF optimization while maintaining the reliability and performance of the existing system.