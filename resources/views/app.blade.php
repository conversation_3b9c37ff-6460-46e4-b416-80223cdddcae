<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        {{-- Inline style to set the HTML background color for light mode --}}
        <style>
            html {
                background-color: oklch(1 0 0);
            }
        </style>

        <title inertia>{{ config('app.name', 'Laravel') }}</title>

        <link rel="icon" href="/favicon.svg" type="image/svg+xml">
        <link rel="apple-touch-icon" href="/apple-touch-icon.png">

        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
        @routes
        @viteReactRefresh
        @vite(['resources/js/app.tsx', "resources/js/pages/{$page['component']}.tsx"])
        <meta name="description" content="Find and book the best student hostels in Ghana. Secure, affordable, and comfortable accommodation for students." />
        <!-- Open Graph -->
        <meta property="og:title" content="Osdan - Student Hostel Booking in Ghana" />
        <meta property="og:description" content="Find and book the best student hostels in Ghana. Secure, affordable, and comfortable accommodation for students." />
        <meta property="og:type" content="website" />
        <meta property="og:image" content="/images/mandy-bourke-JWvTUzh-RHk-unsplash.jpg" />
        <meta property="og:url" content="{{ url()->current() }}" />
        <!-- Twitter Card -->
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Osdan - Student Hostel Booking in Ghana" />
        <meta name="twitter:description" content="Find and book the best student hostels in Ghana. Secure, affordable, and comfortable accommodation for students." />
        <meta name="twitter:image" content="/images/mandy-bourke-JWvTUzh-RHk-unsplash.jpg" />
        <!-- Canonical -->
        <link rel="canonical" href="{{ url()->current() }}" />
        <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "OSDAN Real Estate",
          "url": "https://osdanrealestate.org",
          "logo": "https://osdanrealestate.org/logo_2.svg"
        }
        </script>
        <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "OSDAN Real Estate",
          "url": "https://osdanrealestate.org",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://osdanrealestate.org/search?query={search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
        </script>
        @inertiaHead
    </head>
    <body class="font-sans antialiased">
        @inertia
    </body>
</html>
