import React, { useState, useEffect } from 'react';
import { ImageIcon, AlertCircle } from 'lucide-react';

interface PhotoData {
    id: string;
    original_name: string;
    sizes: {
        [key: string]: {
            path: string;
            url: string;
            size?: {
                width: number;
                height: number;
            };
        };
    };
}

interface OptimizedImageProps {
    photo: PhotoData | string; // Support both new format and legacy string paths
    alt: string;
    size?: 'thumbnail' | 'medium' | 'large' | 'original';
    className?: string;
    loading?: 'lazy' | 'eager';
    onLoad?: () => void;
    onError?: () => void;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
    photo,
    alt,
    size = 'medium',
    className = '',
    loading = 'lazy',
    onLoad,
    onError,
}) => {
    const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
    const [imageSrc, setImageSrc] = useState<string>('');

    useEffect(() => {
        let src = '';

        if (typeof photo === 'string') {
            src = photo;
        } else if (photo && typeof photo === 'object' && photo.sizes) {
            if (photo.sizes[size]) {
                src = photo.sizes[size].url;
            } else if (photo.sizes.original) {
                src = photo.sizes.original.url;
            } else {
                const availableSizes = Object.keys(photo.sizes);
                if (availableSizes.length > 0) {
                    src = photo.sizes[availableSizes[0]].url;
                }
            }
        }

        setImageSrc(src);
        setImageState('loading');
    }, [photo, size]);

    const handleLoad = () => {
        setImageState('loaded');
        onLoad?.();
    };

    const handleError = () => {
        setImageState('error');
        onError?.();
    };

    if (!imageSrc) {
        return (
            <div className={`flex items-center justify-center bg-gray-100 ${className}`}>
                <ImageIcon className="h-8 w-8 text-gray-400" />
            </div>
        );
    }

    return (
        <div className={`relative overflow-hidden ${className}`}>
            {/* Loading skeleton */}
            {imageState === 'loading' && (
                <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                    <ImageIcon className="h-8 w-8 text-gray-400" />
                </div>
            )}

            {/* Error state */}
            {imageState === 'error' && (
                <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                    <AlertCircle className="h-8 w-8 text-gray-400" />
                </div>
            )}

            {/* Actual image */}
            <img
                src={imageSrc}
                alt={alt}
                loading={loading}
                onLoad={handleLoad}
                onError={handleError}
                className={`w-full h-full object-cover transition-opacity duration-200 ${
                    imageState === 'loaded' ? 'opacity-100' : 'opacity-0'
                }`}
            />
        </div>
    );
};

export default OptimizedImage;