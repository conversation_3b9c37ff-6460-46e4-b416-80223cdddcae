import { Search, Calendar, CreditCard, Shield, Users, MapPin } from 'lucide-react';

export default function FeaturesSection() {
    const features = [
        {
            icon: Search,
            title: "Hostel Discovery",
            description: "Comprehensive platform to search and discover verified student hostels across Ghana's top universities and cities.",
            gradient: "from-blue-500 to-cyan-500",
        },
        {
            icon: Calendar,
            title: "Booking Management", 
            description: "Streamlined booking solutions tailored to your academic semester schedule and accommodation needs.",
            gradient: "from-purple-500 to-pink-500",
        },
        {
            icon: Shield,
            title: "Verified Safety",
            description: "Find secure and verified accommodations that meet our strict safety and quality standards.",
            gradient: "from-green-500 to-emerald-500",
        },
        {
            icon: CreditCard,
            title: "Payment Processing",
            description: "Secure payment management, semester fees handling, and seamless refund processing when needed.",
            gradient: "from-orange-500 to-red-500",
        },
        {
            icon: Users,
            title: "Student Community",
            description: "Connect with verified hostel operators and build trusted relationships within our student community.",
            gradient: "from-indigo-500 to-purple-500",
        },
        {
            icon: MapPin,
            title: "Location Services",
            description: "Find accommodations near your university with real-time availability and precise location tracking.",
            gradient: "from-teal-500 to-blue-500",
        }
    ];

    return (
        <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50/30">
            <div className="container mx-auto px-4">
                {/* Header Section */}
                <div className="text-center mb-16">
                    <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        Our Services
                    </div>
                    <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                        World-Class Student
                        <br />
                        <span className="bg-blue-500 bg-clip-text text-transparent">
                            Accommodation Solutions
                        </span>
                    </h2>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        OSDAN delivers premium accommodation solutions to students and stakeholders 
                        across Ghana's leading university campuses.
                    </p>
                </div>
                
                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                    {features.map((feature, index) => {
                        const IconComponent = feature.icon;
                        return (
                            <div 
                                key={index} 
                                className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100/50"
                            >
                                {/* Gradient background on hover */}
                                <div className="absolute inset-0 bg-blue-500 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                
                                <div className="relative">
                                    {/* Icon with gradient background */}
                                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                                        <IconComponent className="w-8 h-8 text-white" />
                                    </div>
                                    
                                    {/* Content */}
                                    <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors duration-300">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                                        {feature.description}
                                    </p>
                                    
                                    {/* Decorative element */}
                                    <div className="absolute top-6 right-6 w-20 h-20 bg-gradient-to-br from-blue-100/20 to-purple-100/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                </div>
                            </div>
                        );
                    })}
                </div>
                
                {/* Bottom CTA */}
                <div className="text-center mt-16">
                    <div className="inline-flex items-center gap-2 text-blue-600 font-medium">
                        <span>Trusted by thousands of students</span>
                        <div className="flex -space-x-2">
                            {[1, 2, 3, 4].map((i) => (
                                <div key={i} className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full border-2 border-white"></div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
} 