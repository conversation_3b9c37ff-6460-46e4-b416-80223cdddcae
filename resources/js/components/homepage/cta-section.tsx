import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { Search, GraduationCap, ArrowRight, Users, Shield, Star } from 'lucide-react';

interface CtaSectionProps {
    isAuthenticated: boolean;
}

export default function CtaSection({ isAuthenticated }: CtaSectionProps) {
    const stats = [
        { icon: Users, value: "10,000+", label: "Happy Students" },
        { icon: Shield, value: "100%", label: "Verified Hostels" },
        { icon: Star, value: "4.9/5", label: "Average Rating" },
    ];

    return (
        <section className="py-20 bg-blue-600 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                    backgroundSize: '60px 60px'
                }}></div>
            </div>

            <div className="container mx-auto px-4 relative">
                {/* Main CTA Content */}
                <div className="text-center mb-16">
                    <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        Ready to Get Started?
                    </div>
                    
                    <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                        Find Your Home Away
                        <br />
                        <span className="text-blue-100">From Home</span>
                    </h2>
                    
                    <p className="text-xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
                        Join thousands of students who have found their perfect accommodation through OSDAN. 
                        Start your journey to comfortable, verified, and affordable student housing today.
                    </p>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                        <Link href="/search">
                            <Button 
                                size="lg" 
                                className="bg-white text-blue-600 hover:bg-blue-50 rounded-full px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                            >
                                <Search className="mr-3 size-5" />
                                Browse All Hostels
                                <ArrowRight className="ml-3 size-5" />
                            </Button>
                        </Link>
                        {!isAuthenticated && (
                            <Link href="/register">
                                <Button 
                                    size="lg" 
                                    variant="outline" 
                                    className="border-2 border-white text-white hover:bg-white hover:text-blue-600 rounded-full px-8 py-4 text-lg font-semibold transition-all duration-300 hover:scale-105"
                                >
                                    <GraduationCap className="mr-3 size-5" />
                                    Sign Up as Student
                                </Button>
                            </Link>
                        )}
                    </div>
                </div>

                {/* Stats Section */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                    {stats.map((stat, index) => {
                        const IconComponent = stat.icon;
                        return (
                            <div 
                                key={index}
                                className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
                            >
                                <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-2xl mb-4">
                                    <IconComponent className="w-8 h-8 text-white" />
                                </div>
                                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                                <div className="text-blue-100 font-medium">{stat.label}</div>
                            </div>
                        );
                    })}
                </div>

                {/* Bottom Trust Indicators */}
                <div className="text-center mt-16">
                    <p className="text-blue-100 mb-6">Trusted by students from top universities</p>
                    <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
                        {['UG', 'KNUST', 'UCC', 'UDS', 'UEW'].map((uni, index) => (
                            <div key={index} className="text-white font-bold text-lg px-4 py-2 bg-white/10 rounded-lg">
                                {uni}
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
} 