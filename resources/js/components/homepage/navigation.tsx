import { Button } from '@/components/ui/button';
import { type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { Building2 } from 'lucide-react';
import AppLogoIcon from '@/components/app-logo-icon';

export default function Navigation() {
    const { auth } = usePage<SharedData>().props;

    return (
        <header className="sticky top-0 z-50 bg-card/95 backdrop-blur-sm border-b border-border h-20">
            <div className="container mx-auto px-4 h-full flex items-center">
                <nav className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                        <AppLogoIcon className="size-8" />
                        <div className="flex flex-col">
                            <span className="text-xl font-bold text-foreground">OSDAN</span>
                        </div>
                    </div>
                    
                    <div className="hidden md:flex items-center gap-8">
                        <Link href="https://chat.whatsapp.com/K74fNK3Zc2a2tgFg0dPH0b?mode=ac_t" className="text-foreground hover:text-primary font-medium transition-colors">
                            Contact
                        </Link>
                    </div>
                    
                    <div className="flex items-center gap-3">
                        {auth.user ? (
                            <div className="flex items-center gap-3">
                                <Link
                                    href={route('dashboard')}
                                    className="inline-flex items-center rounded-full bg-primary px-5 py-2.5 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors"
                                >
                                    Dashboard
                                </Link>
                            </div>
                        ) : (
                            <div className="flex items-center gap-3">
                                <Link
                                    href={route('login')}
                                    className="text-foreground hover:text-primary font-medium transition-colors"
                                >
                                    Log in
                                </Link>
                                <Link
                                    href={route('register')}
                                    className="inline-flex items-center rounded-full bg-primary px-5 py-2.5 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors"
                                >
                                    Sign up
                                </Link>
                            </div>
                        )}
                    </div>
                </nav>
            </div>
        </header>
    );
} 