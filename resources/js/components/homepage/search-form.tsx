import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search } from 'lucide-react';

interface SearchFormProps {
    data: {
        location: string;
        semester: string;
        academic_year: string;
        students: number;
    };
    setData: (key: string, value: string) => void;
    onSubmit: (e: React.FormEvent) => void;
    processing: boolean;
    variant?: 'desktop' | 'mobile';
}

export default function SearchForm({ data, setData, onSubmit, processing, variant = 'desktop' }: SearchFormProps) {
    if (variant === 'desktop') {
        return (
            <div className="hidden md:block bg-white rounded-full p-3 shadow-2xl max-w-2xl">
                <form onSubmit={onSubmit} className="flex items-center gap-2">
                    <div className="flex-1 flex items-center gap-2">
                        <div className="flex-1 px-3 py-2">
                            <Input
                                type="text"
                                placeholder="Enter your university or city"
                                value={data.location}
                                onChange={(e) => setData('location', e.target.value)}
                                className="border-0 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent text-foreground placeholder:text-muted-foreground text-base font-medium p-0 h-auto shadow-none"
                            />
                        </div>
                        <div className="h-8 w-px bg-border" />
                        <div className="min-w-[200px] px-3 py-2">
                            <Select value={data.semester} onValueChange={(value) => setData('semester', value)}>
                                <SelectTrigger className="border-0 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent text-foreground text-base font-medium p-0 h-auto shadow-none">
                                    <SelectValue placeholder="Which semester?" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="first">First Semester</SelectItem>
                                    <SelectItem value="second">Second Semester</SelectItem>
                                    <SelectItem value="both">Full Academic Year</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <Button 
                        type="submit" 
                        className="rounded-full p-5 h-12 bg-primary hover:bg-primary/90 text-white font-medium"
                        disabled={processing}
                    >
                        Find hostels
                    </Button>
                </form>
            </div>
        );
    }

    return (
        <section className="md:hidden py-8 bg-background">
            <div className="container mx-auto px-4">
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                    <h2 className="text-lg font-semibold text-foreground mb-6">Find your perfect student accommodation</h2>
                    <form onSubmit={onSubmit} className="space-y-4">
                        <div>
                            <Input
                                type="text"
                                placeholder="Enter your university or city"
                                value={data.location}
                                onChange={(e) => setData('location', e.target.value)}
                                className="w-full p-4 text-base border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                            />
                        </div>
                        <div>
                            <Select value={data.semester} onValueChange={(value) => setData('semester', value)}>
                                <SelectTrigger className="w-full p-4 text-base border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary">
                                    <SelectValue placeholder="Which semester?" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="first">First Semester</SelectItem>
                                    <SelectItem value="second">Second Semester</SelectItem>
                                    <SelectItem value="both">Full Academic Year</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <Button 
                            type="submit" 
                            className="w-full p-4 rounded-full bg-primary hover:bg-primary/90 text-white font-medium text-base"
                            disabled={processing}
                        >
                            <Search className="mr-2 size-5" />
                            Find hostels
                        </Button>
                    </form>
                </div>
            </div>
        </section>
    );
} 