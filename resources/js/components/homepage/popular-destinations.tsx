import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

interface Hostel {
    id: number;
    name: string;
    city: string;
    state: string;
    price_per_semester: number;
    image_url?: string;
    rating?: number;
    amenities?: string[];
    hostel_type?: string;
}

interface FeaturedHostelsProps {
    hostels: Hostel[];
    onHostelClick: (hostelId: number) => void;
}

export default function FeaturedHostels({ hostels, onHostelClick }: FeaturedHostelsProps) {
    // Use hostels directly without duplication since backend now provides up to 9
    const displayHostels = hostels;

    return (
        <section className="py-16 bg-white">
            <div className="container mx-auto px-4">
                <div className="text-center mb-12">
                    <h2 className="text-3xl font-bold text-foreground mb-4">
                        Featured Student Hostels
                    </h2>
                    <p className="text-muted-foreground">
                        Discover premium student accommodation across Ghana
                    </p>
                </div>
                
                <Tabs defaultValue="hostels" className="w-full max-w-6xl mx-auto">
                    <TabsList className="grid w-full grid-cols-2 h-10 md:w-[200px] mx-auto mb-8 rounded-full">
                        <TabsTrigger value="hostels" className="text-sm px-4 py-2 rounded-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                            Hostels
                        </TabsTrigger>
                        <TabsTrigger value="homestels" className="text-sm px-4 py-2 rounded-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                            Homestels
                        </TabsTrigger>
                    </TabsList>
                    <TabsContent value="hostels">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                            {displayHostels.filter(h => h.hostel_type !== 'homestel').slice(0, 9).map((hostel, index) => (
                                <div 
                                    key={`${hostel.id}-${index}`} 
                                    className="group cursor-pointer transition-all hover:shadow-xl hover:-translate-y-2 rounded-xl overflow-hidden bg-white"
                                    onClick={() => onHostelClick(hostel.id)}
                                >
                                    {/* Image */}
                                    <div className="h-75 bg-gradient-to-br from-primary/20 to-primary/40 relative overflow-hidden">
                                        {hostel.image_url ? (
                                            <img 
                                                src={hostel.image_url} 
                                                alt={hostel.name}
                                                className="w-full h-full object-cover"
                                            />
                                        ) : (
                                            <div className="w-full h-full bg-gradient-to-br from-primary/30 to-primary/50" />
                                        )}
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                                    </div>
                                    
                                    {/* Content */}
                                    <div className="p-6">
                                        <div className="space-y-3">
                                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                <span className="text-primary">○</span>
                                                <span>{hostel.city}, {hostel.state}</span>
                                            </div>
                                            <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
                                                {hostel.name}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        
                        {/* View All Hostels Button */}
                        <div className="text-center mt-12">
                            <a 
                                href="/search"
                                className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-8 py-3 rounded-full font-medium hover:bg-primary/90 transition-colors shadow-lg hover:shadow-xl"
                            >
                                View All Hostels
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                </svg>
                            </a>
                        </div>
                    </TabsContent>
                    <TabsContent value="homestels">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                            {hostels.filter(h => h.hostel_type === 'homestel').slice(0, 9).map((hostel, index) => (
                                <div 
                                    key={`homestel-${hostel.id}-${index}`} 
                                    className="group cursor-pointer transition-all hover:shadow-xl hover:-translate-y-2 rounded-xl overflow-hidden bg-white"
                                    onClick={() => onHostelClick(hostel.id)}
                                >
                                    {/* Image */}
                                    <div className="h-75 bg-gradient-to-br from-orange-200 to-orange-400 relative overflow-hidden">
                                        {hostel.image_url ? (
                                            <img 
                                                src={hostel.image_url} 
                                                alt={hostel.name}
                                                className="w-full h-full object-cover"
                                            />
                                        ) : (
                                            <div className="w-full h-full bg-gradient-to-br from-orange-300 to-orange-500" />
                                        )}
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                                    </div>
                                    
                                    {/* Content */}
                                    <div className="p-6">
                                        <div className="space-y-3">
                                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                <span className="text-orange-500">🏠</span>
                                                <span>{hostel.city}, {hostel.state}</span>
                                            </div>
                                            <h3 className="text-lg font-semibold text-foreground group-hover:text-orange-600 transition-colors line-clamp-2">
                                                {hostel.name}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        
                        {/* View All Homestels Button */}
                        <div className="text-center mt-12">
                            <a 
                                href="/search?type=homestel"
                                className="inline-flex items-center gap-2 bg-orange-600 text-white px-8 py-3 rounded-full font-medium hover:bg-orange-700 transition-colors shadow-lg hover:shadow-xl"
                            >
                                View All Homestels
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                </svg>
                            </a>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </section>
    );
}