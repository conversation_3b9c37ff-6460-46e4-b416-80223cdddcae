import SearchForm from './search-form';

interface HeroSectionProps {
    data: {
        location: string;
        semester: string;
        academic_year: string;
        students: number;
    };
    setData: (key: string, value: string) => void;
    onSubmit: (e: React.FormEvent) => void;
    processing: boolean;
}

export default function HeroSection({ data, setData, onSubmit, processing }: HeroSectionProps) {
    return (
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
            {/* Background Image with Overlay */}
            <div className="absolute inset-0 z-0">
                <div 
                    className="w-full h-full bg-cover bg-center bg-no-repeat"
                    style={{
                        backgroundImage: `linear-gradient(135deg, rgba(52, 73, 85, 0.8), rgba(52, 73, 85, 0.6)), url('/images/mandy-bourke-JWvTUzh-RHk-unsplash.jpg')`
                    }}
                />
            </div>
            
            <div className="relative z-20 container mx-auto px-4 py-20">
                <div className="grid lg:grid-cols-2 gap-16 items-center w-full">
                    {/* Left Content */}
                    <div className="text-white space-y-8 max-w-2xl">
                        <div className="space-y-6">
                            <h1 className="text-5xl lg:text-7xl font-bold leading-[0.9] tracking-tight">
                                Student Rooms
                                <br />
                                <span className="block text-4xl lg:text-6xl font-light">
                                    Real Comfort
                                </span>
                                <span className="block text-4xl lg:text-6xl font-light text-gray-300">
                                    Zero Hassle.
                                </span>
                            </h1>
                            <p className="text-xl lg:text-lg text-gray-200 font-light leading-relaxed max-w-xl">
                                Discover verified student hostels across Ghana's top universities. 
                                Secure your perfect accommodation with trusted hosts and seamless booking.
                            </p>
                        </div>
                        
                        {/* Hero Search Component - Desktop */}
                        <SearchForm 
                            data={data}
                            setData={setData}
                            onSubmit={onSubmit}
                            processing={processing}
                            variant="desktop"
                        />
                    </div>
                </div>
            </div>
        </section>
    );
} 