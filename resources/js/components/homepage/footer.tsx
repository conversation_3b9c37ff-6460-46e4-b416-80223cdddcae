import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Link } from '@inertiajs/react';
import { Building2, Facebook, Twitter, Instagram, Linkedin, Youtube } from 'lucide-react';
import AppLogoIcon from '@/components/app-logo-icon';

export default function Footer() {
    return (
        <footer className="bg-gradient-to-br from-slate-800 to-slate-900 text-white py-24">
            <div className="container mx-auto px-4">
                {/* Main Footer Content */}
                <div className="grid gap-12 lg:grid-cols-2 items-start mb-16">
                    {/* Left Side - Brand & Message */}
                    <div className="space-y-8">
                        <div className="flex items-center gap-3 mb-6">
                            <AppLogoIcon className="size-12 text-white" />
                            <div>
                                <span className="text-3xl font-bold text-white">OSDAN</span>
                                <p className="text-sm text-white/80 font-medium uppercase tracking-wider">Student Housing</p>
                            </div>
                        </div>
                        
                        <div className="space-y-6">
                            <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
                                It's good to be
                                <br />
                                <span className="italic font-normal" style={{ fontFamily: 'Instrument Serif, serif' }}>home</span>
                            </h2>
                            <p className="text-xl text-white/90 leading-relaxed max-w-lg">
                                Ghana's premier student accommodation platform. 
                                Find verified hostels near your university and make it feel like home.
                            </p>
                        </div>

                        {/* Newsletter Signup */}
                        <div className="space-y-4">
                            <h3 className="text-xl font-semibold text-white">Sign-up to our newsletter</h3>
                            <div className="flex gap-3 max-w-md">
                                <Input
                                    type="email"
                                    placeholder="Enter your email..."
                                    className="bg-white/10 border-white/20 text-white placeholder:text-white/60 focus:border-white focus:ring-white"
                                />
                                <Button className="bg-white text-slate-800 hover:bg-white/90 px-8 rounded-full">
                                    Subscribe
                                </Button>
                            </div>
                            <p className="text-xs text-white/60">
                                Find out <Link href="#" className="underline hover:text-white">how we use your personal data</Link>
                            </p>
                        </div>

                        {/* Social Media */}
                        <div className="flex gap-4 pt-4">
                            <Link href="#" className="p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                                <Twitter className="size-5 text-white" />
                            </Link>
                            <Link href="#" className="p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                                <Facebook className="size-5 text-white" />
                            </Link>
                            <Link href="#" className="p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                                <Linkedin className="size-5 text-white" />
                            </Link>
                            <Link href="#" className="p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                                <Instagram className="size-5 text-white" />
                            </Link>
                            <Link href="#" className="p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                                <Youtube className="size-5 text-white" />
                            </Link>
                        </div>
                    </div>
                    
                    {/* Right Side - Navigation Links */}
                    <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-2">
                        <div>
                            <h3 className="text-xl font-semibold text-white mb-6">For Students</h3>
                            <ul className="space-y-3 text-white/80">
                                <li><Link href="/search" className="hover:text-white transition-colors text-lg">Search Hostels</Link></li>
                                <li><Link href="/register" className="hover:text-white transition-colors text-lg">Create Account</Link></li>
                                <li><Link href="/login" className="hover:text-white transition-colors text-lg">Student Login</Link></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                {/* Bottom Bar */}
                <div className="border-t border-white/20 pt-8">
                    <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                        <p className="text-white/70">
                            &copy; {new Date().getFullYear()} OSDAN. All rights reserved.
                        </p>
                        <div className="flex flex-wrap gap-6 text-sm text-white/70">
                            <Link href="#" className="hover:text-white transition-colors">Privacy Policy</Link>
                            <Link href="#" className="hover:text-white transition-colors">Terms & Conditions</Link>
                            <Link href="#" className="hover:text-white transition-colors">Cookie Policy</Link>
                            <Link href="#" className="hover:text-white transition-colors">Accessibility</Link>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
} 