
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, <PERSON>barContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { Calendar, Users, BarChart3, Building2, CreditCard, Settings, LayoutGrid } from 'lucide-react';
import AppLogo from './app-logo';

export function AppSidebar() {
    const { auth } = usePage().props as any;
    const user = auth?.user;
    
    // Dynamic navigation based on user role
    const mainNavItems: NavItem[] = [];

    if (user?.role === 'student') {
        mainNavItems.push({
            title: 'Bookings',
            href: '/bookings',
            icon: Calendar,
        });
    } else if (user?.role === 'hostel_admin') {
        mainNavItems.push(
            {
                title: 'Dashboard',
                href: '/admin/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Bookings',
                href: '/bookings',
                icon: Calendar,
            },
            {
                title: 'Hostels',
                href: '/hostels',
                icon: Building2,
            }
        );
    } else if (user?.role === 'super_admin') {
        mainNavItems.push(
            {
                title: 'Dashboard',
                href: '/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Users',
                href: '/admin/users',
                icon: Users,
            },
            {
                title: 'Analytics',
                href: '/admin/analytics',
                icon: BarChart3,
            },
            {
                title: 'Hostels',
                href: '/admin/hostels',
                icon: Building2,
            },
            {
                title: 'Universities',
                href: '/admin/universities',
                icon: Building2,
            },
            {
                title: 'Academic Years',
                href: '/admin/academic-years',
                icon: Calendar,
            },
            {
                title: 'Payments',
                href: '/admin/payments',
                icon: CreditCard,
            },
            {
                title: 'Settings',
                href: '/admin/settings',
                icon: Settings,
            }
        );
    }
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/bookings" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
