import { type SharedData } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import { useState, useEffect } from 'react';

// Homepage Components
import Navigation from '@/components/homepage/navigation';
import HeroSection from '@/components/homepage/hero-section';
import SearchForm from '@/components/homepage/search-form';
import FeaturedHostels from '@/components/homepage/popular-destinations';
import FeaturesSection from '@/components/homepage/features-section';
import CtaSection from '@/components/homepage/cta-section';
import Footer from '@/components/homepage/footer';

interface Hostel {
    id: number;
    name: string;
    city: string;
    state: string;
    price_per_semester: number;
    image_url?: string;
    rating?: number;
    amenities?: string[];
}

interface WelcomeProps extends SharedData {
    featured_hostels: Hostel[];
}

export default function Welcome() {
    const { auth, featured_hostels } = usePage<WelcomeProps>().props;
    const [featuredHostels, setFeaturedHostels] = useState<Hostel[]>(featured_hostels || []);

    const { data, setData, get, processing } = useForm({
        location: '',
        semester: 'first',
        academic_year: '2024/2025',
        students: 1,
    });

    useEffect(() => {
        // If no hostels from props, load static or fallback data
        if (featured_hostels && featured_hostels.length > 0) {
            setFeaturedHostels(featured_hostels);
        } else {
            loadStaticHostels();
        }
    }, [featured_hostels]);



    const loadStaticHostels = () => {
        // Use static hostel data as fallback
        setFeaturedHostels([
            { 
                id: 1, 
                name: 'University of Ghana - Legon Hall', 
                city: 'Accra', 
                state: 'Greater Accra', 
                price_per_semester: 2500, 
                rating: 4.8,
                amenities: ['wifi', 'laundry', 'dining_hall', 'study_rooms']
            },
            { 
                id: 2, 
                name: 'KNUST - Unity Hall', 
                city: 'Kumasi', 
                state: 'Ashanti', 
                price_per_semester: 2200, 
                rating: 4.6,
                amenities: ['wifi', 'gym', 'laundry', 'common_areas']
            },
            { 
                id: 3, 
                name: 'UCC - Atlantic Hall', 
                city: 'Cape Coast', 
                state: 'Central', 
                price_per_semester: 1800, 
                rating: 4.7,
                amenities: ['wifi', 'study_rooms', 'dining_hall', 'security']
            },
            { 
                id: 4, 
                name: 'GIMPA - Executive Hall', 
                city: 'Accra', 
                state: 'Greater Accra', 
                price_per_semester: 3000, 
                rating: 4.9,
                amenities: ['wifi', 'air_conditioning', 'gym', 'parking']
            },
            { 
                id: 5, 
                name: 'UEW - Jophus Anamuah-Mensah Hall', 
                city: 'Winneba', 
                state: 'Central', 
                price_per_semester: 1900, 
                rating: 4.5,
                amenities: ['wifi', 'laundry', 'study_rooms', 'common_room']
            },
            { 
                id: 6, 
                name: 'Ashesi University - Premier Residence', 
                city: 'Berekuso', 
                state: 'Eastern', 
                price_per_semester: 4500, 
                rating: 4.9,
                amenities: ['wifi', 'air_conditioning', 'gym', 'dining_hall', 'innovation_lab']
            }
        ]);
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (data.location.trim()) {
            get('/search', {
                preserveScroll: true,
            });
        }
    };

    const handleHostelClick = (hostelId: number) => {
        // Navigate to hostel details page
        window.location.href = `/hostels/${hostelId}`;
    };

    return (
        <>
            <Head title="Find Your Perfect Student Hostel in Ghana">
                <meta name="description" content="Discover and book verified student hostels across Ghana's top universities. Secure, comfortable, and affordable accommodation for students." />
            </Head>
            
            <div className="min-h-screen bg-background">
                {/* Navigation */}
                <Navigation />

                {/* Hero Section */}
                <HeroSection 
                    data={data}
                    setData={setData}
                    onSubmit={handleSearch}
                    processing={processing}
                />

                {/* Mobile Search Section */}
                <div className="md:hidden shadow-lg">
                    <SearchForm 
                        data={data}
                        setData={setData}
                        onSubmit={handleSearch}
                        processing={processing}
                        variant="mobile"
                    />
                </div>

                {/* Featured Hostels */}
                <div className="bg-background">
                    <FeaturedHostels
                        hostels={featuredHostels}
                        onHostelClick={handleHostelClick}
                    />
                </div>

                {/* Features Section */}
                {/* <div className="bg-background">
                    <FeaturesSection />
                </div> */}

                {/* Call to Action
                <div className="bg-background">
                    <CtaSection isAuthenticated={!!auth.user} />
                </div> */}

                {/* Footer */}
                <Footer />
            </div>
        </>
    );
}
