import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { MapPin, Star, Users, Plus, Search, Filter, Building2 } from 'lucide-react';
import { useState } from 'react';

interface Hostel {
    id: string;
    name: string;
    description: string;
    city: string;
    state: string;
    country: string;
    photos: string[];
    average_rating: number;
    total_reviews: number;
    is_active: boolean;
    is_verified: boolean;
    owner: {
        name: string;
    };
    active_room_types: Array<{
        id: string;
        name: string;
        category: string;
        price_per_night: number;
        capacity: number;
    }>;
    bookings_count: number;
    hostel_type: 'university' | 'homestel';
}

interface Props {
    hostels?: {
        data: Hostel[];
        links: any;
        meta: any;
    };
    can_create: boolean;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Hostels',
        href: '/hostels',
    },
];

export default function HostelsIndex({ hostels, can_create }: Props) {
    const { auth } = usePage<SharedData>().props;
    const [searchQuery, setSearchQuery] = useState('');
    const [cityFilter, setCityFilter] = useState('all');
    const [sortBy, setSortBy] = useState('newest');
    const [selectedTab, setSelectedTab] = useState<'hostel' | 'homestel'>('hostel');

    // Safety check for hostels prop
    if (!hostels || !hostels.data) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Hostels" />
                <div className="flex h-full flex-1 flex-col gap-6 p-4">
                    <div className="flex h-64 flex-col items-center justify-center rounded-lg border border-dashed">
                        <Building2 className="h-12 w-12 text-gray-400" />
                        <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
                            Loading hostels...
                        </h3>
                    </div>
                </div>
            </AppLayout>
        );
    }

    const handleSearch = () => {
        router.get('/hostels', {
            search: searchQuery,
            city: cityFilter === 'all' ? '' : cityFilter,
            sort: sortBy,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getMinPrice = (roomTypes: Hostel['active_room_types']) => {
        if (roomTypes.length === 0) return null;
        return Math.min(...roomTypes.map(rt => rt.price_per_night));
    };

    const filteredHostels = hostels?.data?.filter(h => selectedTab === 'hostel' ? h.hostel_type === 'university' : h.hostel_type === 'homestel') || [];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Hostels" />
            
            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                            {auth.user?.role === 'hostel_admin' ? 'My Hostels' : 'Hostels'}
                        </h1>
                        <p className="mt-2 text-gray-600 dark:text-gray-400">
                            {auth.user?.role === 'hostel_admin'
                                ? 'Manage your university hostels and room assignments'
                                : 'Find the perfect student accommodation for your university experience'
                            }
                        </p>
                    </div>
                    
                    {can_create && (
                        <Link href="/hostels/create">
                            <Button className="gap-2">
                                <Plus className="h-4 w-4" />
                                Add Hostel
                            </Button>
                        </Link>
                    )}
                </div>

                {/* Search and Filters */}
                <div className="flex flex-col gap-4 rounded-lg border bg-card p-4 sm:flex-row">
                    <div className="flex-1">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                            <Input
                                placeholder="Search hostels by name or location..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-10"
                                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                            />
                        </div>
                    </div>
                    
                    <div className="flex gap-2">
                        <Select value={cityFilter} onValueChange={setCityFilter}>
                            <SelectTrigger className="w-[140px]">
                                <SelectValue placeholder="All Cities" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Cities</SelectItem>
                                <SelectItem value="lagos">Lagos</SelectItem>
                                <SelectItem value="abuja">Abuja</SelectItem>
                                <SelectItem value="kano">Kano</SelectItem>
                                <SelectItem value="ibadan">Ibadan</SelectItem>
                            </SelectContent>
                        </Select>

                        <Select value={sortBy} onValueChange={setSortBy}>
                            <SelectTrigger className="w-[140px]">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="newest">Newest</SelectItem>
                                <SelectItem value="oldest">Oldest</SelectItem>
                                <SelectItem value="rating">Rating</SelectItem>
                                <SelectItem value="price_low">Price: Low</SelectItem>
                                <SelectItem value="price_high">Price: High</SelectItem>
                            </SelectContent>
                        </Select>

                        <Button onClick={handleSearch} variant="outline" className="gap-2">
                            <Filter className="h-4 w-4" />
                            Filter
                        </Button>
                    </div>
                </div>

                {/* Tab Navigation */}
                <div className="mb-4 flex gap-2">
                    <button className={selectedTab === 'hostel' ? 'font-bold' : ''} onClick={() => setSelectedTab('hostel')}>Hostels</button>
                    <button className={selectedTab === 'homestel' ? 'font-bold' : ''} onClick={() => setSelectedTab('homestel')}>Homestels</button>
                </div>

                {/* Hostels Grid */}
                <div className="flex-1">
                    {filteredHostels.length > 0 ? (
                        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                            {filteredHostels.map((hostel) => {
                                const minPrice = getMinPrice(hostel.active_room_types);
                                
                                return (
                                    <Card key={hostel.id} className="group cursor-pointer transition-all hover:shadow-lg">
                                        <Link href={`/hostels/${hostel.id}`}>
                                            <div className="relative aspect-[4/3] overflow-hidden rounded-t-lg">
                                                {hostel.photos && hostel.photos.length > 0 ? (
                                                    <img
                                                        src={hostel.photos[0]}
                                                        alt={hostel.name}
                                                        className="h-full w-full object-cover transition-transform group-hover:scale-105"
                                                    />
                                                ) : (
                                                    <div className="flex h-full items-center justify-center bg-gray-100 dark:bg-gray-800">
                                                        <Building2 className="h-12 w-12 text-gray-400" />
                                                    </div>
                                                )}
                                                
                                                {/* Status badges */}
                                                <div className="absolute left-2 top-2 flex gap-2">
                                                    {hostel.is_verified && (
                                                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                                                            Verified
                                                        </Badge>
                                                    )}
                                                    {!hostel.is_active && (
                                                        <Badge variant="destructive">
                                                            Inactive
                                                        </Badge>
                                                    )}
                                                </div>

                                                {/* Price badge */}
                                                {minPrice && (
                                                    <div className="absolute bottom-2 right-2">
                                                        <Badge className="bg-black/70 text-white">
                                                            ₦{minPrice}/night
                                                        </Badge>
                                                    </div>
                                                )}
                                            </div>
                                            
                                            <CardHeader className="pb-2">
                                                <div className="flex items-start justify-between">
                                                    <CardTitle className="line-clamp-1 text-lg">
                                                        {hostel.name}
                                                    </CardTitle>
                                                    {hostel.average_rating > 0 && (
                                                        <div className="flex items-center gap-1">
                                                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                                            <span className="text-sm font-medium">
                                                                {hostel.average_rating.toFixed(1)}
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                                                    <MapPin className="h-4 w-4" />
                                                    <span>{hostel.city}, {hostel.state}</span>
                                                </div>
                                            </CardHeader>
                                            
                                            <CardContent className="pt-0">
                                                <CardDescription className="line-clamp-2 text-sm">
                                                    {hostel.description}
                                                </CardDescription>
                                                
                                                <div className="mt-3 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                                                    <div className="flex items-center gap-1">
                                                        <Users className="h-4 w-4" />
                                                        <span>{hostel.active_room_types.length} room types</span>
                                                    </div>
                                                    
                                                    {auth.user?.role === 'hostel_admin' && (
                                                        <span>{hostel.bookings_count} bookings</span>
                                                    )}
                                                </div>
                                            </CardContent>
                                        </Link>
                                    </Card>
                                );
                            })}
                        </div>
                    ) : (
                        <div className="flex h-64 flex-col items-center justify-center rounded-lg border border-dashed">
                            <Building2 className="h-12 w-12 text-gray-400" />
                            <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
                                No hostels found
                            </h3>
                            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                {can_create 
                                    ? "Get started by creating your first hostel." 
                                    : "Try adjusting your search filters."}
                            </p>
                            {can_create && (
                                <Link href="/hostels/create" className="mt-4">
                                    <Button>Create Hostel</Button>
                                </Link>
                            )}
                        </div>
                    )}
                </div>

                {/* Pagination */}
                {hostels.meta && hostels.meta.total > hostels.meta.per_page && hostels.links && (
                    <div className="flex items-center justify-center gap-2">
                        {hostels.links.map((link: any, index: number) => (
                            <Button
                                key={index}
                                variant={link.active ? "default" : "outline"}
                                size="sm"
                                disabled={!link.url}
                                onClick={() => link.url && router.visit(link.url)}
                                dangerouslySetInnerHTML={{ __html: link.label }}
                            />
                        ))}
                    </div>
                )}
            </div>
        </AppLayout>
    );
} 