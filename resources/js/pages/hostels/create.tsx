import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm, router } from '@inertiajs/react';
import { Building2, MapPin, Phone, Mail, Globe, Clock, FileText, Upload, X, ImageIcon, AlertCircle } from 'lucide-react';
import { useState, useRef, useCallback } from 'react';

interface FormData {
    name: string;
    description: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude: string;
    longitude: string;
    phone: string;
    email: string;
    website: string;

    house_rules: string;
    cancellation_policy: string;
    amenities: string[];
    photos: File[];
    payment_period: 'month' | 'semester' | 'year'; // Added for Feature 1
    hostel_type: 'university' | 'homestel' | 'other'; // Added for Feature 3
    youtube_links: string[]; // Added for Feature 6
    [key: string]: string | number | string[] | File[] | File | 'month' | 'semester' | 'year' | 'university' | 'homestel' | 'other';
}

interface PhotoPreview {
    file: File;
    url: string;
    id: string;
}



const ghanaianRegions = [
    'Greater Accra', 'Ashanti', 'Central', 'Eastern', 'Northern', 'Upper East',
    'Upper West', 'Volta', 'Western', 'Western North', 'Brong Ahafo',
    'Ahafo', 'Bono East', 'Oti', 'North East', 'Savannah'
];

const amenitiesList = [
    'wifi', 'parking', 'laundry', 'air_conditioning', 'kitchen', 'dining_hall',
    'study_rooms', 'common_room', 'library', 'sports_facilities', 'security',
    'generator', 'water_supply', 'cleaning_service'
];

export default function CreateHostel() {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        name: '',
        description: '',
        address: '',
        city: '',
        state: '',
        country: 'Ghana',
        postal_code: '',
        latitude: '',
        longitude: '',
        phone: '',
        email: '',
        website: '',
        house_rules: '',
        cancellation_policy: '',
        amenities: [],
        photos: [],
        payment_period: 'month', // Initialize for Feature 1
        hostel_type: 'university', // Initialize for Feature 3
        youtube_links: [''], // Initialize for Feature 6
    });

    const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
    const [photoPreviews, setPhotoPreviews] = useState<PhotoPreview[]>([]);
    const [isDragOver, setIsDragOver] = useState(false);
    const [uploadErrors, setUploadErrors] = useState<string[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);



    // New state for YouTube Links (Feature 6)
    const [youtubeLinks, setYoutubeLinks] = useState<string[]>(['']);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Hostels', href: '/hostels' },
        { title: 'Create New Hostel', href: '' },
    ];

    const validateFile = (file: File): string | null => {
        // Check file type
        if (!file.type.startsWith('image/')) {
            return `${file.name} is not an image file`;
        }

        // Check file size (50MB max)
        if (file.size > 50 * 1024 * 1024) {
            return `${file.name} is too large (max 50MB)`;
        }

        // Check file format
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            return `${file.name} format not supported (use JPEG, PNG, or WebP)`;
        }

        return null;
    };

    const processFiles = useCallback((files: FileList | File[]) => {
        const fileArray = Array.from(files);
        const errors: string[] = [];
        const validFiles: File[] = [];
        const newPreviews: PhotoPreview[] = [];

        // Check total files limit
        if (photoPreviews.length + fileArray.length > 10) {
            errors.push('Maximum 10 photos allowed');
            return;
        }

        fileArray.forEach((file) => {
            const error = validateFile(file);
            if (error) {
                errors.push(error);
            } else {
                validFiles.push(file);
                newPreviews.push({
                    file,
                    url: URL.createObjectURL(file),
                    id: Math.random().toString(36).substring(2),
                });
            }
        });

        setUploadErrors(errors);
        
        if (validFiles.length > 0) {
            const updatedPhotos = [...data.photos, ...validFiles];
            const updatedPreviews = [...photoPreviews, ...newPreviews];
            
            setData('photos', updatedPhotos);
            setPhotoPreviews(updatedPreviews);
        }
    }, [data.photos, photoPreviews]);

    const handlePhotoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files) {
            processFiles(files);
        }
        // Reset input value so same file can be selected again
        e.target.value = '';
    };

    const handleRemovePhoto = (photoId: string) => {
        const photoIndex = photoPreviews.findIndex(p => p.id === photoId);
        if (photoIndex !== -1) {
            // Revoke the object URL to free memory
            URL.revokeObjectURL(photoPreviews[photoIndex].url);
            
            // Remove from previews and data
            const updatedPreviews = photoPreviews.filter(p => p.id !== photoId);
            const updatedPhotos = data.photos.filter((_, index) => index !== photoIndex);
            
            setPhotoPreviews(updatedPreviews);
            setData('photos', updatedPhotos);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
        
        const files = e.dataTransfer.files;
        if (files) {
            processFiles(files);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = new FormData();
        
        // Append text fields
        Object.entries(data).forEach(([key, value]) => {
            if (key !== 'photos' && key !== 'amenities' && key !== 'room_types' && key !== 'youtube_links') {
                formData.append(key, value as string);
            }
        });
        
        // Append amenities
        selectedAmenities.forEach((amenity, index) => {
            formData.append(`amenities[${index}]`, amenity);
        });
        
        // Append photos
        data.photos.forEach((photo, index) => {
            formData.append(`photos[${index}]`, photo);
        });

        // Note: Room types are added separately after hostel creation
        // They are not included in the initial hostel creation form

        // Append YouTube links
        data.youtube_links.forEach((link, index) => {
            formData.append(`youtube_links[${index}]`, link);
        });

        router.post('/hostels', formData, {
            forceFormData: true,
        });
    };

    const handleAmenityToggle = (amenity: string) => {
        setSelectedAmenities(prev => 
            prev.includes(amenity) 
                ? prev.filter(a => a !== amenity)
                : [...prev, amenity]
        );
    };



   
    const handleYoutubeLinkChange = (index: number, value: string) => {
        const newLinks = [...youtubeLinks];
        newLinks[index] = value;
        setYoutubeLinks(newLinks);
    };

    const addYoutubeLink = () => {
        setYoutubeLinks([...youtubeLinks, '']);
    };

    const removeYoutubeLink = (index: number) => {
        const newLinks = [...youtubeLinks];
        newLinks.splice(index, 1);
        setYoutubeLinks(newLinks);
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create University Hostel" />

            <div className="container mx-auto py-8">
                <div className="max-w-4xl mx-auto">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Create University Hostel</h1>
                        <p className="mt-2 text-gray-600 dark:text-gray-400">
                            Add a new university hostel to the platform for student accommodation management.
                        </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Building2 className="h-5 w-5" />
                                    Basic Information
                                </CardTitle>
                                <CardDescription>
                                    Provide basic details about your university hostel
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="name">Hostel Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="e.g., University of Ghana - Legon Hall"
                                            required
                                        />
                                        {errors.name && (
                                            <p className="text-sm text-red-600 mt-1">{errors.name}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="phone">Phone Number *</Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="+233 XX XXX XXXX"
                                            required
                                        />
                                        {errors.phone && (
                                            <p className="text-sm text-red-600 mt-1">{errors.phone}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="email">Email Address *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                            required
                                        />
                                        {errors.email && (
                                            <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="website">Website</Label>
                                        <Input
                                            id="website"
                                            value={data.website}
                                            onChange={(e) => setData('website', e.target.value)}
                                            placeholder="https://university.edu.gh/housing"
                                        />
                                        {errors.website && (
                                            <p className="text-sm text-red-600 mt-1">{errors.website}</p>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="description">Description *</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Describe the hostel, its facilities, and what makes it special for students..."
                                        rows={4}
                                        required
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-red-600 mt-1">{errors.description}</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Location Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MapPin className="h-5 w-5" />
                                    Location Details
                                </CardTitle>
                                <CardDescription>
                                    Specify the location of your university hostel
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="address">Full Address *</Label>
                                    <Input
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        placeholder="University Campus, Building/Hall Name"
                                        required
                                    />
                                    {errors.address && (
                                        <p className="text-sm text-red-600 mt-1">{errors.address}</p>
                                    )}
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="city">City *</Label>
                                        <Input
                                            id="city"
                                            value={data.city}
                                            onChange={(e) => setData('city', e.target.value)}
                                            placeholder="Accra, Kumasi, Cape Coast..."
                                            required
                                        />
                                        {errors.city && (
                                            <p className="text-sm text-red-600 mt-1">{errors.city}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="state">Region *</Label>
                                        <Select onValueChange={(value) => setData('state', value)} required>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select Region" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {ghanaianRegions.map((region) => (
                                                    <SelectItem key={region} value={region}>
                                                        {region}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.state && (
                                            <p className="text-sm text-red-600 mt-1">{errors.state}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="postal_code">Postal Code</Label>
                                        <Input
                                            id="postal_code"
                                            value={data.postal_code}
                                            onChange={(e) => setData('postal_code', e.target.value)}
                                            placeholder="00233"
                                        />
                                        {errors.postal_code && (
                                            <p className="text-sm text-red-600 mt-1">{errors.postal_code}</p>
                                        )}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="latitude">Latitude</Label>
                                        <Input
                                            id="latitude"
                                            value={data.latitude}
                                            onChange={(e) => setData('latitude', e.target.value)}
                                            placeholder="5.6037"
                                        />
                                        {errors.latitude && (
                                            <p className="text-sm text-red-600 mt-1">{errors.latitude}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="longitude">Longitude</Label>
                                        <Input
                                            id="longitude"
                                            value={data.longitude}
                                            onChange={(e) => setData('longitude', e.target.value)}
                                            placeholder="-0.1870"
                                        />
                                        {errors.longitude && (
                                            <p className="text-sm text-red-600 mt-1">{errors.longitude}</p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Operational Details */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="h-5 w-5" />
                                    Operational Details
                                </CardTitle>
                                <CardDescription>
                                    Configure office hours and administrative details
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="house_rules">Hostel Rules</Label>
                                    <Textarea
                                        id="house_rules"
                                        value={data.house_rules}
                                        onChange={(e) => setData('house_rules', e.target.value)}
                                        placeholder="e.g., Quiet hours from 10 PM to 6 AM. No opposite gender visitors after 9 PM..."
                                        rows={3}
                                    />
                                    {errors.house_rules && (
                                        <p className="text-sm text-red-600 mt-1">{errors.house_rules}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="cancellation_policy">Cancellation Policy</Label>
                                    <Textarea
                                        id="cancellation_policy"
                                        value={data.cancellation_policy}
                                        onChange={(e) => setData('cancellation_policy', e.target.value)}
                                        placeholder="e.g., Cancellation allowed up to 2 weeks before semester start with full refund..."
                                        rows={3}
                                    />
                                    {errors.cancellation_policy && (
                                        <p className="text-sm text-red-600 mt-1">{errors.cancellation_policy}</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Booking & Payment Settings (Feature 1) */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    Booking & Payment Settings
                                </CardTitle>
                                <CardDescription>
                                    Define how bookings and payments are handled for this hostel.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="payment_period">Payment Period *</Label>
                                    <Select
                                        value={data.payment_period}
                                        onValueChange={(value: 'month' | 'semester' | 'year') => setData('payment_period', value)}
                                        required
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Payment Period" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="month">Per Month</SelectItem>
                                            <SelectItem value="semester">Per Semester</SelectItem>
                                            <SelectItem value="year">Per Academic Year</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.payment_period && (
                                        <p className="text-sm text-red-600 mt-1">{errors.payment_period}</p>
                                    )}
                                </div>
                                {/* Hostel Type (Feature 3) */}
                                <div>
                                    <Label htmlFor="hostel_type">Hostel Type *</Label>
                                    <Select
                                        value={data.hostel_type}
                                        onValueChange={(value: 'university' | 'homestel' | 'other') => setData('hostel_type', value)}
                                        required
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Hostel Type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="university">Hostel</SelectItem>
                                            <SelectItem value="homestel">Homestel</SelectItem>
                                            <SelectItem value="other">Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.hostel_type && (
                                        <p className="text-sm text-red-600 mt-1">{errors.hostel_type}</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Hostel Amenities (Existing) */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Hostel Amenities</CardTitle>
                                <CardDescription>
                                    Select the facilities available at your hostel
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                    {amenitiesList.map((amenity) => (
                                        <label
                                            key={amenity}
                                            className="flex items-center space-x-2 cursor-pointer"
                                        >
                                            <input
                                                type="checkbox"
                                                checked={selectedAmenities.includes(amenity)}
                                                onChange={() => handleAmenityToggle(amenity)}
                                                className="rounded border-gray-300"
                                            />
                                            <span className="text-sm capitalize">
                                                {amenity.replace('_', ' ')}
                                            </span>
                                        </label>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* YouTube Video Links (Feature 6) */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <i className="fas fa-play-circle h-5 w-5"></i> {/* Placeholder icon for YouTube */}
                                    YouTube Video Links
                                </CardTitle>
                                <CardDescription>
                                    Add links to YouTube videos showcasing your hostel.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {youtubeLinks.map((link, index) => (
                                    <div key={index} className="flex items-center gap-2">
                                        <Input
                                            type="url"
                                            value={link}
                                            onChange={(e) => handleYoutubeLinkChange(index, e.target.value)}
                                            placeholder="https://www.youtube.com/watch?v=yourvideo"
                                        />
                                        {youtubeLinks.length > 1 && (
                                            <Button type="button" variant="destructive" onClick={() => removeYoutubeLink(index)}>
                                                <X className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button type="button" variant="outline" onClick={addYoutubeLink}>
                                    Add Another Video Link
                                </Button>
                                {errors.youtube_links && (
                                    <p className="text-sm text-red-600 mt-1">{errors.youtube_links}</p>
                                )}
                            </CardContent>
                        </Card>



                        {/* Enhanced Photos Section */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <ImageIcon className="h-5 w-5" />
                                    Hostel Photos
                                </CardTitle>
                                <CardDescription>
                                    Upload high-quality photos to showcase your hostel. Maximum 10 photos, 50MB each.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* Upload Area */}
                                <div
                                    className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                                        isDragOver
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                                            : 'border-gray-300 hover:border-gray-400 dark:border-gray-600'
                                    }`}
                                    onDragOver={handleDragOver}
                                    onDragLeave={handleDragLeave}
                                    onDrop={handleDrop}
                                >
                                    <input
                                        ref={fileInputRef}
                                        type="file"
                                        multiple
                                        accept="image/*"
                                        onChange={handlePhotoSelect}
                                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                    />
                                    <div className="space-y-2">
                                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                Drop photos here or click to browse
                                            </p>
                                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                                PNG, JPG, WebP up to 50MB each ({photoPreviews.length}/10 photos)
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Upload Errors */}
                                {uploadErrors.length > 0 && (
                                    <Alert variant="destructive">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription>
                                            <ul className="list-disc pl-4 space-y-1">
                                                {uploadErrors.map((error, index) => (
                                                    <li key={index} className="text-sm">{error}</li>
                                                ))}
                                            </ul>
                                        </AlertDescription>
                                    </Alert>
                                )}

                                {/* Photo Previews */}
                                {photoPreviews.length > 0 && (
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <h4 className="text-sm font-medium">Photo Previews</h4>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => {
                                                    photoPreviews.forEach(preview => URL.revokeObjectURL(preview.url));
                                                    setPhotoPreviews([]);
                                                    setData('photos', []);
                                                }}
                                            >
                                                Clear All
                                            </Button>
                                        </div>
                                        
                                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                            {photoPreviews.map((preview, index) => (
                                                <div key={preview.id} className="relative group">
                                                    <div className="aspect-square overflow-hidden rounded-lg border">
                                                        <img
                                                            src={preview.url}
                                                            alt={`Preview ${index + 1}`}
                                                            className="w-full h-full object-cover"
                                                        />
                                                    </div>
                                                    
                                                    {/* Remove button */}
                                                    <button
                                                        type="button"
                                                        onClick={() => handleRemovePhoto(preview.id)}
                                                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </button>
                                                    
                                                    {/* File info */}
                                                    <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 truncate">
                                                        <div>{preview.file.name}</div>
                                                        <div>{formatFileSize(preview.file.size)}</div>
                                                    </div>

                                                    {/* First photo indicator */}
                                                    {index === 0 && (
                                                        <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                                            Cover Photo
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                        
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            The first photo will be used as the cover image. Drag and drop to reorder.
                                        </p>
                                    </div>
                                )}

                                {/* Additional Upload Button */}
                                {photoPreviews.length > 0 && photoPreviews.length < 10 && (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => fileInputRef.current?.click()}
                                        className="w-full"
                                    >
                                        <Upload className="mr-2 h-4 w-4" />
                                        Add More Photos ({photoPreviews.length}/10)
                                    </Button>
                                )}

                                {/* General photo upload errors */}
                                {errors.photos && (
                                    <p className="text-sm text-red-600">{errors.photos}</p>
                                )}
                            </CardContent>
                        </Card>

                        {/* Submit */}
                        <div className="flex justify-end space-x-4">
                            <Button 
                                type="button" 
                                variant="outline"
                                onClick={() => router.visit('/hostels')}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Creating...' : 'Create Hostel'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
} 