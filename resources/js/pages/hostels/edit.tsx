import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm, router } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Building2, MapPin, Phone, Mail, Globe, Clock, FileText, Upload, X, ImageIcon, AlertCircle } from 'lucide-react';
import { useState, useRef, useCallback } from 'react';

interface Hostel {
    id: string;
    name: string;
    description: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string | null;
    latitude: string | null;
    longitude: string | null;
    phone: string;
    email: string;
    website: string | null;

    house_rules: string | null;
    cancellation_policy: string | null;
    amenities: string[];
    photos: string[];
    payment_period: string;
    hostel_type: string;
    youtube_links: string[];
    room_types: RoomType[];
}

interface RoomType {
    id: number;
    name: string;
    capacity: number;
    number_of_beds: number;
    price_per_semester: number;
    male_rooms: number;
    female_rooms: number;
    mixed_rooms: number;
}

interface EditHostelProps {
    hostel: Hostel;
}

interface PhotoPreview {
    id: string;
    url: string;
    isNew: boolean;
    file?: File; // Only present for new files
}

interface RoomTypeForm {
    id: string; // Used for unique key and removal, and for existing room types
    name: string;
    capacity: number; // Capacity (persons)
    number_of_beds: number;
    price_per_semester: number;
    male_rooms: number;
    female_rooms: number;
    mixed_rooms: number;
    _method?: 'put'; // For existing room types in backend
}

const ghanaianRegions = [
    'Greater Accra', 'Ashanti', 'Central', 'Eastern', 'Northern', 'Upper East',
    'Upper West', 'Volta', 'Western', 'Western North', 'Brong Ahafo',
    'Ahafo', 'Bono East', 'Oti', 'North East', 'Savannah'
];

const amenitiesList = [
    'wifi', 'parking', 'laundry', 'air_conditioning', 'kitchen', 'dining_hall',
    'study_rooms', 'common_room', 'library', 'sports_facilities', 'security',
    'generator', 'water_supply', 'cleaning_service'
];

export default function EditHostel({ hostel }: EditHostelProps) {
    const { data, setData, post, processing, errors } = useForm({
        _method: 'put',
        name: hostel.name,
        description: hostel.description,
        address: hostel.address,
        city: hostel.city,
        state: hostel.state,
        country: hostel.country,
        postal_code: hostel.postal_code || '',
        latitude: hostel.latitude || '',
        longitude: hostel.longitude || '',
        phone: hostel.phone,
        email: hostel.email,
        website: hostel.website || '',
        house_rules: hostel.house_rules || '',
        cancellation_policy: hostel.cancellation_policy || '',
        amenities: hostel.amenities || [],
        photos: [] as (File | string)[],
        payment_period: hostel.payment_period as 'month' | 'semester' | 'year',
        hostel_type: hostel.hostel_type as 'university' | 'homestel' | 'other',
        youtube_links: hostel.youtube_links || [''],
    });

    const [selectedAmenities, setSelectedAmenities] = useState<string[]>(hostel.amenities || []);
    const [photoPreviews, setPhotoPreviews] = useState<PhotoPreview[]>(
        (hostel.photos || []).map((url: string) => ({ id: Math.random().toString(36).substring(2), url, isNew: false }))
    );
    const [isDragOver, setIsDragOver] = useState(false);
    const [uploadErrors, setUploadErrors] = useState<string[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [roomTypes, setRoomTypes] = useState<RoomTypeForm[]>(
        hostel.room_types && hostel.room_types.length > 0
            ? hostel.room_types.map((rt: RoomType) => ({
                id: rt.id.toString(), // Ensure ID is string for consistency
                name: rt.name,
                capacity: rt.capacity,
                number_of_beds: rt.number_of_beds,
                price_per_semester: rt.price_per_semester,
                male_rooms: rt.male_rooms || 0,
                female_rooms: rt.female_rooms || 0,
                mixed_rooms: rt.mixed_rooms || 0,
            }))
            : [{ id: Math.random().toString(36).substring(2), name: '', capacity: 0, number_of_beds: 0, price_per_semester: 0, male_rooms: 0, female_rooms: 0, mixed_rooms: 0 }]
    );

    const [youtubeLinks, setYoutubeLinks] = useState<string[]>(hostel.youtube_links && hostel.youtube_links.length > 0 ? hostel.youtube_links : ['']);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Hostels', href: '/hostels' },
        { title: `Edit ${hostel.name}`, href: '' },
    ];

    const validateFile = (file: File): string | null => {
        if (!file.type.startsWith('image/')) {
            return `${file.name} is not an image file`;
        }
        if (file.size > 50 * 1024 * 1024) {
            return `${file.name} is too large (max 50MB)`;
        }
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            return `${file.name} format not supported (use JPEG, PNG, or WebP)`;
        }
        return null;
    };

    const processFiles = useCallback((files: FileList | File[]) => {
        const fileArray = Array.from(files);
        const errors: string[] = [];
        const validFiles: File[] = [];
        const newPreviews: PhotoPreview[] = [];

        if (photoPreviews.length + fileArray.length > 10) {
            errors.push('Maximum 10 photos allowed');
            return;
        }

        fileArray.forEach((file) => {
            const error = validateFile(file);
            if (error) {
                errors.push(error);
            } else {
                validFiles.push(file);
                newPreviews.push({
                    file,
                    url: URL.createObjectURL(file),
                    id: Math.random().toString(36).substring(2),
                    isNew: true,
                });
            }
        });

        setUploadErrors(errors);
        
        if (validFiles.length > 0) {
            const updatedPhotos = [...data.photos, ...validFiles];
            const updatedPreviews = [...photoPreviews, ...newPreviews];
            
            setData('photos', updatedPhotos);
            setPhotoPreviews(updatedPreviews);
        }
    }, [data.photos, photoPreviews]);

    const handlePhotoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files) {
            processFiles(files);
        }
        e.target.value = '';
    };

    const handleRemovePhoto = (photoId: string, isNew: boolean) => {
        setPhotoPreviews(prev => {
            const photoToRemove = prev.find(p => p.id === photoId);
            if (photoToRemove && photoToRemove.isNew && photoToRemove.file) {
                URL.revokeObjectURL(photoToRemove.url);
            }
            return prev.filter(p => p.id !== photoId);
        });

        if (isNew) {
            const updatedPhotos = (data.photos as (File | string)[]).filter((photo) => {
                if (photo instanceof File && photo.name === photoPreviews.find(p => p.id === photoId)?.file?.name) {
                    return false;
                }
                return true;
            });
            setData('photos', updatedPhotos);
        } else {
            const updatedPhotos = (data.photos as (File | string)[]).filter((photo) => {
                if (typeof photo === 'string' && photo === photoPreviews.find(p => p.id === photoId)?.url) {
                    return false;
                }
                return true;
            });
            setData('photos', updatedPhotos);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
        
        const files = e.dataTransfer.files;
        if (files) {
            processFiles(files);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = new FormData();
        
        Object.entries(data).forEach(([key, value]) => {
            if (key === 'photos') {
                (value as (File | string)[]).forEach((photo, index) => {
                    if (photo instanceof File) {
                        formData.append(`photos[${index}]`, photo);
                    } else {
                        // Keep existing photo URLs if not new
                        formData.append(`existing_photos[${index}]`, photo);
                    }
                });
            } else if (key === 'amenities') {
                (value as string[]).forEach((amenity, index) => {
                    formData.append(`amenities[${index}]`, amenity);
                });
            } else if (key === 'youtube_links') {
                (value as string[]).forEach((link, index) => {
                    formData.append(`youtube_links[${index}]`, link);
                });
            } else if (key === '_method') {
                formData.append('_method', 'put'); // Must be 'put' for Inertia PUT requests
            } else {
                formData.append(key, value as string);
            }
        });

        // Note: Room types are managed separately via the room-types routes
        // They are not included in the hostel update form

        router.post(route('hostels.update', hostel.id), formData, {
            forceFormData: true,
        });
    };

    const handleAmenityToggle = (amenity: string) => {
        setSelectedAmenities(prev => 
            prev.includes(amenity) 
                ? prev.filter(a => a !== amenity)
                : [...prev, amenity]
        );
    };

    const handleAddRoomType = () => {
        setRoomTypes([...roomTypes, { id: Math.random().toString(36).substring(2), name: '', capacity: 0, number_of_beds: 0, price_per_semester: 0, male_rooms: 0, female_rooms: 0, mixed_rooms: 0 }]);
    };

    const handleRemoveRoomType = (id: string) => {
        setRoomTypes(roomTypes.filter(rt => rt.id !== id));
    };

    const handleRoomTypeChange = (id: string, field: keyof RoomTypeForm, value: string | number) => {
        setRoomTypes(roomTypes.map(rt => {
            if (rt.id === id) {
                if (['capacity', 'number_of_beds', 'price_per_semester', 'male_rooms', 'female_rooms', 'mixed_rooms'].includes(field as string)) {
                    // Convert to number, defaulting to 0 if NaN
                    return { ...rt, [field]: parseFloat(value as string) || 0 };
                }
                return { ...rt, [field]: value };
            }
            return rt;
        }));
    };

    const handleYoutubeLinkChange = (index: number, value: string) => {
        const newLinks = [...youtubeLinks];
        newLinks[index] = value;
        setYoutubeLinks(newLinks);
        setData('youtube_links', newLinks); // Update form data
    };

    const addYoutubeLink = () => {
        setYoutubeLinks([...youtubeLinks, '']);
        setData('youtube_links', [...youtubeLinks, '']); // Update form data
    };

    const removeYoutubeLink = (index: number) => {
        const newLinks = [...youtubeLinks];
        newLinks.splice(index, 1);
        setYoutubeLinks(newLinks);
        setData('youtube_links', newLinks); // Update form data
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit ${hostel.name}`} />

            <div className="container mx-auto py-8">
                <div className="max-w-4xl mx-auto">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Edit Hostel</h1>
                        <p className="mt-2 text-gray-600 dark:text-gray-400">
                            Update the details for {hostel.name}.
                        </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Building2 className="h-5 w-5" />
                                    Basic Information
                                </CardTitle>
                                <CardDescription>
                                    Provide basic details about your hostel
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="name">Hostel Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="e.g., University of Ghana - Legon Hall"
                                            required
                                        />
                                        {errors.name && (
                                            <p className="text-sm text-red-600 mt-1">{errors.name}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="phone">Phone Number *</Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="+233 XX XXX XXXX"
                                            required
                                        />
                                        {errors.phone && (
                                            <p className="text-sm text-red-600 mt-1">{errors.phone}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="email">Email Address *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                            required
                                        />
                                        {errors.email && (
                                            <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="website">Website</Label>
                                        <Input
                                            id="website"
                                            value={data.website}
                                            onChange={(e) => setData('website', e.target.value)}
                                            placeholder="https://university.edu.gh/housing"
                                        />
                                        {errors.website && (
                                            <p className="text-sm text-red-600 mt-1">{errors.website}</p>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="description">Description *</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Describe the hostel, its facilities, and what makes it special for students..."
                                        rows={4}
                                        required
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-red-600 mt-1">{errors.description}</p>
                                    )}
                                </div>

                                {/* Hostel Type (Feature 3) */}
                                <div>
                                    <Label htmlFor="hostel_type">Hostel Type *</Label>
                                    <Select
                                        value={data.hostel_type}
                                        onValueChange={(value: 'university' | 'homestel' | 'other') => setData('hostel_type', value)}
                                        required
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Hostel Type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="university">Hostel</SelectItem>
                                            <SelectItem value="homestel">Homestel</SelectItem>
                                            <SelectItem value="other">Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.hostel_type && (
                                        <p className="text-sm text-red-600 mt-1">{errors.hostel_type}</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Booking & Payment Settings (Feature 1) */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    Booking & Payment Settings
                                </CardTitle>
                                <CardDescription>
                                    Define how bookings and payments are handled for this hostel.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="payment_period">Payment Period *</Label>
                                    <Select
                                        value={data.payment_period}
                                        onValueChange={(value: 'month' | 'semester' | 'year') => setData('payment_period', value)}
                                        required
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Payment Period" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="month">Per Month</SelectItem>
                                            <SelectItem value="semester">Per Semester</SelectItem>
                                            <SelectItem value="year">Per Academic Year</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.payment_period && (
                                        <p className="text-sm text-red-600 mt-1">{errors.payment_period}</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Location Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MapPin className="h-5 w-5" />
                                    Location Details
                                </CardTitle>
                                <CardDescription>
                                    Specify the location of your hostel
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="address">Full Address *</Label>
                                    <Input
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        placeholder="University Campus, Building/Hall Name"
                                        required
                                    />
                                    {errors.address && (
                                        <p className="text-sm text-red-600 mt-1">{errors.address}</p>
                                    )}
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="city">City *</Label>
                                        <Input
                                            id="city"
                                            value={data.city}
                                            onChange={(e) => setData('city', e.target.value)}
                                            placeholder="Accra, Kumasi, Cape Coast..."
                                            required
                                        />
                                        {errors.city && (
                                            <p className="text-sm text-red-600 mt-1">{errors.city}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="state">Region *</Label>
                                        <Select value={data.state} onValueChange={(value) => setData('state', value)} required>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select Region" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {ghanaianRegions.map((region) => (
                                                    <SelectItem key={region} value={region}>
                                                        {region}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.state && (
                                            <p className="text-sm text-red-600 mt-1">{errors.state}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="postal_code">Postal Code</Label>
                                        <Input
                                            id="postal_code"
                                            value={data.postal_code}
                                            onChange={(e) => setData('postal_code', e.target.value)}
                                            placeholder="00233"
                                        />
                                        {errors.postal_code && (
                                            <p className="text-sm text-red-600 mt-1">{errors.postal_code}</p>
                                        )}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="latitude">Latitude</Label>
                                        <Input
                                            id="latitude"
                                            value={data.latitude}
                                            onChange={(e) => setData('latitude', e.target.value)}
                                            placeholder="5.6037"
                                        />
                                        {errors.latitude && (
                                            <p className="text-sm text-red-600 mt-1">{errors.latitude}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="longitude">Longitude</Label>
                                        <Input
                                            id="longitude"
                                            value={data.longitude}
                                            onChange={(e) => setData('longitude', e.target.value)}
                                            placeholder="-0.1870"
                                        />
                                        {errors.longitude && (
                                            <p className="text-sm text-red-600 mt-1">{errors.longitude}</p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Operational Details */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="h-5 w-5" />
                                    Operational Details
                                </CardTitle>
                                <CardDescription>
                                    Configure office hours and administrative details
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="house_rules">Hostel Rules</Label>
                                    <Textarea
                                        id="house_rules"
                                        value={data.house_rules}
                                        onChange={(e) => setData('house_rules', e.target.value)}
                                        placeholder="e.g., Quiet hours from 10 PM to 6 AM. No opposite gender visitors after 9 PM..."
                                        rows={3}
                                    />
                                    {errors.house_rules && (
                                        <p className="text-sm text-red-600 mt-1">{errors.house_rules}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="cancellation_policy">Cancellation Policy</Label>
                                    <Textarea
                                        id="cancellation_policy"
                                        value={data.cancellation_policy}
                                        onChange={(e) => setData('cancellation_policy', e.target.value)}
                                        placeholder="e.g., Cancellation allowed up to 2 weeks before semester start with full refund..."
                                        rows={3}
                                    />
                                    {errors.cancellation_policy && (
                                        <p className="text-sm text-red-600 mt-1">{errors.cancellation_policy}</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Hostel Amenities */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Hostel Amenities</CardTitle>
                                <CardDescription>
                                    Select the facilities available at your hostel
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                    {amenitiesList.map((amenity) => (
                                        <label
                                            key={amenity}
                                            className="flex items-center space-x-2 cursor-pointer"
                                        >
                                            <input
                                                type="checkbox"
                                                checked={selectedAmenities.includes(amenity)}
                                                onChange={() => handleAmenityToggle(amenity)}
                                                className="rounded border-gray-300"
                                            />
                                            <span className="text-sm capitalize">
                                                {amenity.replace('_', ' ')}
                                            </span>
                                        </label>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Room Types (Feature 2) */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <i className="fas fa-bed h-5 w-5"></i> {/* Placeholder icon for Room Types */}
                                    Room Types
                                </CardTitle>
                                <CardDescription>
                                    Define the different room types available in your hostel.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {roomTypes.map((roomType, index) => (
                                    <div key={roomType.id} className="border p-4 rounded-lg relative">
                                        {roomTypes.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="icon"
                                                className="absolute top-2 right-2 rounded-full"
                                                onClick={() => handleRemoveRoomType(roomType.id)}
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        )}
                                        <h4 className="font-semibold mb-4">Room Type {index + 1}</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <div>
                                                <Label htmlFor={`room_type_${index}_name`}>Room Type Name *</Label>
                                                <Input
                                                    id={`room_type_${index}_name`}
                                                    value={roomType.name}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'name', e.target.value)}
                                                    placeholder="e.g., 2 in 1"
                                                    required
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_capacity`}>Capacity (persons) *</Label>
                                                <Input
                                                    id={`room_type_${index}_capacity`}
                                                    type="number"
                                                    value={roomType.capacity || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'capacity', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    required
                                                    min={1}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_beds`}>Number of Beds *</Label>
                                                <Input
                                                    id={`room_type_${index}_beds`}
                                                    type="number"
                                                    value={roomType.number_of_beds || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'number_of_beds', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    required
                                                    min={1}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_price`}>Price per Semester (GHS) *</Label>
                                                <Input
                                                    id={`room_type_${index}_price`}
                                                    type="number"
                                                    value={roomType.price_per_semester || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'price_per_semester', e.target.value)}
                                                    placeholder="e.g., 2500"
                                                    required
                                                    min={0}
                                                />
                                            </div>
                                        </div>

                                        <h5 className="font-semibold mb-2">Room Allocation by Gender</h5>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <Label htmlFor={`room_type_${index}_male`}>Male Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_male`}
                                                    type="number"
                                                    value={roomType.male_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'male_rooms', e.target.value)}
                                                    placeholder="e.g., 5"
                                                    min={0}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_female`}>Female Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_female`}
                                                    type="number"
                                                    value={roomType.female_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'female_rooms', e.target.value)}
                                                    placeholder="e.g., 5"
                                                    min={0}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_mixed`}>Mixed Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_mixed`}
                                                    type="number"
                                                    value={roomType.mixed_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'mixed_rooms', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    min={0}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                <Button type="button" variant="outline" onClick={handleAddRoomType}>
                                    Add Room Type
                                </Button>
                            </CardContent>
                        </Card>

                        {/* Photo Management (Existing, Enhanced) */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <ImageIcon className="h-5 w-5" />
                                    Hostel Photos
                                </CardTitle>
                                <CardDescription>
                                    Upload high-quality photos to showcase your hostel. Maximum 10 photos, 50MB each.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* Upload Area */}
                                <div
                                    className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                                        isDragOver
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                                            : 'border-gray-300 hover:border-gray-400 dark:border-gray-600'
                                    }`}
                                    onDragOver={handleDragOver}
                                    onDragLeave={handleDragLeave}
                                    onDrop={handleDrop}
                                >
                                    <input
                                        ref={fileInputRef}
                                        type="file"
                                        multiple
                                        accept="image/*"
                                        onChange={handlePhotoSelect}
                                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                    />
                                    <div className="space-y-2">
                                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                Drop photos here or click to browse
                                            </p>
                                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                                PNG, JPG, WebP up to 50MB each ({photoPreviews.length}/10 photos)
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Upload Errors */}
                                {uploadErrors.length > 0 && (
                                    <Alert variant="destructive">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription>
                                            <ul className="list-disc pl-4 space-y-1">
                                                {uploadErrors.map((error, index) => (
                                                    <li key={index} className="text-sm">{error}</li>
                                                ))}
                                            </ul>
                                        </AlertDescription>
                                    </Alert>
                                )}

                                {/* Photo Previews */}
                                {photoPreviews.length > 0 && (
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <h4 className="text-sm font-medium">Photo Previews</h4>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => {
                                                    photoPreviews.forEach(preview => {
                                                        if (preview.isNew && preview.file) URL.revokeObjectURL(preview.url);
                                                    });
                                                    setPhotoPreviews([]);
                                                    setData('photos', []);
                                                }}
                                            >
                                                Clear All
                                            </Button>
                                        </div>
                                        
                                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                            {photoPreviews.map((preview, index) => (
                                                <div key={preview.id} className="relative group">
                                                    <div className="aspect-square overflow-hidden rounded-lg border">
                                                        <img
                                                            src={preview.url}
                                                            alt={`Preview ${index + 1}`}
                                                            className="w-full h-full object-cover"
                                                        />
                                                    </div>
                                                    
                                                    {/* Remove button */}
                                                    <button
                                                        type="button"
                                                        onClick={() => handleRemovePhoto(preview.id, preview.isNew)}
                                                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </button>
                                                    
                                                    {/* File info */}
                                                    <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 truncate">
                                                        <div>{preview.isNew ? preview.file?.name : 'Existing Photo'}</div>
                                                        {preview.isNew && preview.file && <div>{formatFileSize(preview.file.size)}</div>}
                                                    </div>

                                                    {/* First photo indicator */}
                                                    {index === 0 && (
                                                        <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                                            Cover Photo
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                        
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            The first photo will be used as the cover image. Drag and drop to reorder.
                                        </p>
                                    </div>
                                )}

                                {/* Additional Upload Button */}
                                {photoPreviews.length > 0 && photoPreviews.length < 10 && (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => fileInputRef.current?.click()}
                                        className="w-full"
                                    >
                                        <Upload className="mr-2 h-4 w-4" />
                                        Add More Photos ({photoPreviews.length}/10)
                                    </Button>
                                )}

                                {/* General photo upload errors */}
                                {errors.photos && (
                                    <p className="text-sm text-red-600">{errors.photos}</p>
                                )}
                            </CardContent>
                        </Card>

                        {/* YouTube Video Links (Feature 6) */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <i className="fas fa-play-circle h-5 w-5"></i> {/* Placeholder icon for YouTube */}
                                    YouTube Video Links
                                </CardTitle>
                                <CardDescription>
                                    Add links to YouTube videos showcasing your hostel.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {youtubeLinks.map((link, index) => (
                                    <div key={index} className="flex items-center gap-2">
                                        <Input
                                            type="url"
                                            value={link}
                                            onChange={(e) => handleYoutubeLinkChange(index, e.target.value)}
                                            placeholder="https://www.youtube.com/watch?v=yourvideo"
                                        />
                                        {youtubeLinks.length > 1 && (
                                            <Button type="button" variant="destructive" onClick={() => removeYoutubeLink(index)}>
                                                <X className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button type="button" variant="outline" onClick={addYoutubeLink}>
                                    Add Another Video Link
                                </Button>
                                {errors.youtube_links && (
                                    <p className="text-sm text-red-600 mt-1">{errors.youtube_links}</p>
                                )}
                            </CardContent>
                        </Card>

                        {/* Submit */}
                        <div className="flex justify-end space-x-4">
                            <Button 
                                type="button" 
                                variant="outline"
                                onClick={() => router.visit('/hostels')}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Updating...' : 'Update Hostel'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
} 