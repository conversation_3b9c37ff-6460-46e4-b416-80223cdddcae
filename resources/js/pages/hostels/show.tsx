import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Head, Link } from '@inertiajs/react';
import { 
    MapPin, Star, Users, Bed, Clock, Wifi, Car, Coffee, 
    Edit, Plus, Calendar, CheckCircle, AlertCircle,
    Phone, Mail, Globe, Building2, ArrowLeft, GraduationCap, ImageIcon,
    ChevronLeft, ChevronRight, CreditCard
} from 'lucide-react';
import { useState, useEffect } from 'react';

interface RoomType {
    id: string;
    name: string;
    description: string;
    category: string;
    gender_type: string;
    semester_type: string;
    capacity: number;
    beds: number;
    available_rooms: number;
    amenities: string[];
    photos: string[];
    is_active: boolean;
}

interface Booking {
    id: string;
    booking_reference: string;
    semester: string;
    academic_year: string;
    students: number;
    status: string;
    total_amount: number;
    user: {
        name: string;
    };
}

interface Hostel {
    id: string;
    name: string;
    description: string;
    address: string;
    city: string;
    state: string;
    country: string;
    phone: string;
    email: string;
    website: string;
    photos: string[];
    amenities: string[];

    house_rules: string;
    cancellation_policy: string;
    average_rating: number;
    total_reviews: number;
    is_active: boolean;
    is_verified: boolean;
    owner: {
        name: string;
        email: string;
    };
    payment_period: string;
    hostel_type: string;
    youtube_links: string[];
    base_price: number;
    service_fee: number;
    deposit_percentage: number;
    cancellation_fee: number;
}

interface Props {
    hostel: Hostel;
    room_types: RoomType[];
    recent_bookings: Booking[];
    can_edit: boolean;
}

const amenityIcons: Record<string, any> = {
    'wifi': Wifi,
    'parking': Car,
    'dining_hall': Coffee,
    'air_conditioning': Coffee,
    'laundry': Building2,
    'gym': Users,
    'library': Building2,
    'study_rooms': Building2,
    'common_room': Users,
    'security': CheckCircle,
    'sports_facilities': Users,
};

export default function HostelShow({ hostel, room_types, recent_bookings, can_edit }: Props) {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [isAutoSliding, setIsAutoSliding] = useState(true);
    
    const images = hostel.photos || [];

    // Helper functions for pricing
    const getPaymentPeriodText = () => {
        switch(hostel.payment_period) {
            case 'month': return 'Month';
            case 'semester': return 'Semester';
            case 'year': return 'Academic Year';
            default: return 'Period';
        }
    };

    const getFormattedBasePrice = () => {
        if (!hostel.base_price) return 'Price not set';
        return `GH₵${hostel.base_price.toLocaleString()} per ${getPaymentPeriodText().toLowerCase()}`;
    };

    const calculateDisplayPrice = () => {
        if (!hostel.base_price) return 0;
        
        // For display purposes, show semester equivalent
        if (hostel.payment_period === 'month') {
            return hostel.base_price * 4.5; // Average semester length
        } else if (hostel.payment_period === 'year') {
            return hostel.base_price / 2; // Half of academic year
        }
        return hostel.base_price; // Already semester-based
    };

    // Auto-slide functionality
    useEffect(() => {
        if (isAutoSliding && images.length > 1) {
            const interval = setInterval(() => {
                setCurrentImageIndex((prev) => (prev + 1) % images.length);
            }, 4000); // Change image every 4 seconds
            
            return () => clearInterval(interval);
        }
    }, [isAutoSliding, images.length]);

    const nextImage = () => {
        const newIndex = (currentImageIndex + 1) % images.length;

        setCurrentImageIndex(newIndex);
        setIsAutoSliding(false); // Pause auto-slide when user interacts
        setTimeout(() => setIsAutoSliding(true), 8000); // Resume after 8 seconds
    };

    const prevImage = () => {
        const newIndex = (currentImageIndex - 1 + images.length) % images.length;

        setCurrentImageIndex(newIndex);
        setIsAutoSliding(false); // Pause auto-slide when user interacts
        setTimeout(() => setIsAutoSliding(true), 8000); // Resume after 8 seconds
    };

    const formatGenderType = (gender: string) => {
        const types = {
            'male': 'Male Only',
            'female': 'Female Only',
            'mixed': 'Co-ed'
        };
        return types[gender as keyof typeof types] || gender;
    };

    const formatSemesterType = (semester: string) => {
        const types = {
            'both': 'Full Academic Year',
            'first_only': 'First Semester Only',
            'second_only': 'Second Semester Only'
        };
        return types[semester as keyof typeof types] || semester;
    };

    return (
        <div className="min-h-screen bg-white">
            <Head title={hostel.name} />
            
            {/* Navigation */}
            <nav className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100">
                <div className="px-6 py-4">
                    <button 
                        onClick={() => window.history.back()}
                        className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        <span className="text-sm font-medium">Back to search</span>
                    </button>
                </div>
            </nav>

            <div className="pt-16 grid lg:grid-cols-3 min-h-screen">
                {/* Main Image Section with Slider */}
                <div className="lg:col-span-2 relative bg-gray-50">
                    {hostel.photos && hostel.photos.length > 0 ? (
                        <div className="relative h-full min-h-[60vh] lg:min-h-screen overflow-hidden">
                            {/* Image Slider Container */}
                            <div 
                                className="flex transition-transform duration-700 ease-in-out h-full"
                                style={{ 
                                    transform: `translateX(-${currentImageIndex * (100 / images.length)}%)`,
                                    width: `${images.length * 100}%`
                                }}
                            >
                                {images.map((image, index) => (
                                    <div key={index} className="w-full h-full flex-shrink-0" style={{ width: `${100 / images.length}%` }}>
                                        <img
                                            src={image}
                                            alt={`${hostel.name} - Image ${index + 1}`}
                                            className="w-full h-full object-cover"
                                            onLoad={() => {}}
                                            onError={() => {}}
                                        />
                                    </div>
                                ))}
                            </div>
                            
                            {/* Image Navigation */}
                            {images.length > 1 && (
                                <>
                                    <button
                                        onClick={prevImage}
                                        className="absolute left-6 top-1/2 -translate-y-1/2 w-14 h-14 bg-white/95 hover:bg-white rounded-full flex items-center justify-center shadow-xl transition-all hover:scale-110 z-10"
                                    >
                                        <ChevronLeft className="h-7 w-7 text-gray-800" />
                                    </button>
                                    <button
                                        onClick={nextImage}
                                        className="absolute right-6 top-1/2 -translate-y-1/2 w-14 h-14 bg-white/95 hover:bg-white rounded-full flex items-center justify-center shadow-xl transition-all hover:scale-110 z-10"
                                    >
                                        <ChevronRight className="h-7 w-7 text-gray-800" />
                                    </button>
                                </>
                            )}

                            {/* Image Counter */}
                            {images.length > 1 && (
                                <div className="absolute top-6 right-6 bg-black/50 text-white px-4 py-2 rounded-full text-sm font-medium">
                                    {currentImageIndex + 1} / {images.length}
                                </div>
                            )}

                            {/* Image Indicators */}
                            {images.length > 1 && (
                                <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-2">
                                    {images.map((_, index) => (
                                        <button
                                            key={index}
                                            onClick={() => setCurrentImageIndex(index)}
                                            className={`w-3 h-3 rounded-full transition-all hover:scale-110 ${
                                                index === currentImageIndex 
                                                    ? 'bg-white shadow-lg' 
                                                    : 'bg-white/60 hover:bg-white/80'
                                            }`}
                                        />
                                    ))}
                                </div>
                            )}

                            {/* Thumbnail Navigation */}
                            {images.length > 1 && (
                                <div className="absolute bottom-6 left-6 flex gap-2 max-w-xs overflow-x-auto">
                                    {images.slice(0, 5).map((image, index) => (
                                        <button
                                            key={index}
                                            onClick={() => setCurrentImageIndex(index)}
                                            className={`w-16 h-12 rounded-lg overflow-hidden flex-shrink-0 transition-all hover:scale-105 ${
                                                index === currentImageIndex 
                                                    ? 'ring-2 ring-white shadow-lg' 
                                                    : 'opacity-70 hover:opacity-100'
                                            }`}
                                        >
                                            <img
                                                src={image}
                                                alt={`Thumbnail ${index + 1}`}
                                                className="w-full h-full object-cover"
                                            />
                                        </button>
                                    ))}
                                    {images.length > 5 && (
                                        <div className="w-16 h-12 bg-black/50 rounded-lg flex items-center justify-center text-white text-xs font-medium">
                                            +{images.length - 5}
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Status Badge */}
                            <div className="absolute top-6 left-6">
                                {hostel.is_verified && (
                                    <Badge className="bg-green-600 text-white px-4 py-2 rounded-full shadow-lg">
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Verified
                                    </Badge>
                                )}
                            </div>
                        </div>
                    ) : (
                        // Placeholder when no photos
                        <div className="h-full min-h-[60vh] lg:min-h-screen bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                            <div className="text-center text-blue-600">
                                <ImageIcon className="h-16 w-16 mx-auto mb-4" />
                                <p className="text-lg font-medium">Photos coming soon</p>
                            </div>
                        </div>
                    )}
                </div>

                {/* Sidebar */}
                <div className="bg-white border-l border-gray-100 overflow-y-auto">
                    <div className="p-8 space-y-8">
                        {/* Header */}
                        <div className="space-y-4">
                            <div className="flex items-start gap-3">
                                <h1 className="text-2xl font-bold text-gray-900 leading-tight">
                                    {hostel.name}
                                </h1>
                            </div>
                            
                            <div className="flex items-center gap-2 text-gray-600">
                                <MapPin className="h-4 w-4" />
                                <span className="text-sm">{hostel.city}, {hostel.state}</span>
                            </div>

                            {hostel.average_rating > 0 && (
                                <div className="flex items-center gap-2">
                                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                    <span className="text-sm font-medium">{hostel.average_rating.toFixed(1)}</span>
                                    <span className="text-sm text-gray-500">({hostel.total_reviews} reviews)</span>
                                </div>
                            )}
                        </div>

                        {/* Hostel Details */}
                        <div className="space-y-6">
                            <div>
                                <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">Hostel Details</h3>
                                <div className="space-y-3 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Category</span>
                                        <span className="text-gray-900 font-medium">
                                            {hostel.hostel_type === 'university' ? 'Hostel' : hostel.hostel_type === 'homestel' ? 'Homestel' : 'Other'}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Year</span>
                                        <span className="text-gray-900 font-medium">2024/2025</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-500">Status</span>
                                        <Badge className={`px-4 py-2 rounded-full text-xs font-medium ${
                                            hostel.is_active 
                                                ? 'bg-green-100 text-green-800 border-green-200' 
                                                : 'bg-red-100 text-red-800 border-red-200'
                                        }`}>
                                            {hostel.is_active ? 'Open for Registration' : 'Closed'}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            {/* Room Types Summary */}
                            <div>
                                <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">Available Rooms</h3>
                                <div className="space-y-2">
                                    {room_types.slice(0, 3).map((roomType) => (
                                        <div key={roomType.id} className="flex justify-between items-center text-sm">
                                            <span className="text-gray-600">{roomType.name}</span>
                                            <span className="font-medium text-blue-600">{getFormattedBasePrice()}</span>
                                        </div>
                                    ))}
                                    {room_types.length > 3 && (
                                        <div className="text-xs text-gray-500 pt-1">
                                            +{room_types.length - 3} more room types
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Contact Information */}
                            <div>
                                <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">Contact</h3>
                                <div className="space-y-3 text-sm">
                                    {hostel.phone && (
                                        <div className="flex items-center gap-3">
                                            <Phone className="h-4 w-4 text-gray-400" />
                                            <span className="text-gray-700">{hostel.phone}</span>
                                        </div>
                                    )}
                                    
                                    {hostel.email && (
                                        <div className="flex items-center gap-3">
                                            <Mail className="h-4 w-4 text-gray-400" />
                                            <span className="text-gray-700">{hostel.email}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="space-y-3 pt-4">
                                {can_edit && (
                                    <Link href={`/hostels/${hostel.id}/edit`}>
                                        <Button variant="outline" className="w-full gap-2 px-8 py-4 text-base font-medium rounded-full">
                                            <Edit className="h-4 w-4" />
                                            Edit Hostel
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Bottom Section - Scrollable Content */}
                    <div className="border-t border-gray-100 p-8 space-y-8">
                        {/* Description */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">About this hostel</h3>
                            <p className="text-gray-600 leading-relaxed text-sm">
                                {hostel.description}
                            </p>
                        </div>

                        {/* Amenities */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Facilities & Amenities</h3>
                            {hostel.amenities && hostel.amenities.length > 0 ? (
                                <div className="grid grid-cols-2 gap-3">
                                    {hostel.amenities.map((amenity, index) => {
                                        const IconComponent = amenityIcons[amenity.toLowerCase()] || CheckCircle;
                                        return (
                                            <div key={index} className="flex items-center gap-2 text-sm">
                                                <IconComponent className="h-4 w-4 text-blue-600" />
                                                <span className="text-gray-700 capitalize">{amenity.replace('_', ' ')}</span>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <p className="text-gray-500 text-sm">Facility information will be updated soon</p>
                            )}
                        </div>

                        {/* Room Types */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Room Types</h3>
                            <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                <div className="flex items-start gap-3">
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <CreditCard className="h-5 w-5 text-blue-600" />
                                    </div>
                                    <div>
                                        <h4 className="font-medium text-blue-900 mb-1">Pricing Information</h4>
                                        <p className="text-blue-700 text-sm mb-2">
                                            All rooms at this hostel are priced at <strong>{getFormattedBasePrice()}</strong>
                                        </p>
                                        <p className="text-blue-600 text-xs">
                                            • Service fee: GH₵{(hostel.service_fee || 0).toLocaleString()} per booking
                                            {/* */}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="space-y-4">
                                {room_types.map((roomType) => (
                                    <div key={roomType.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                                        <div className="flex justify-between items-start mb-2">
                                            <h4 className="font-medium text-gray-900">{roomType.name}</h4>
                                            <span className="text-lg font-bold text-blue-600">
                                                {getFormattedBasePrice()}
                                            </span>
                                        </div>
                                        
                                        <div className="flex gap-2 mb-2 flex-wrap">
                                            <Badge variant="outline" className="text-xs px-2 py-1 rounded-full bg-blue-50 text-blue-700 border-blue-200">
                                                {formatSemesterType(roomType.semester_type)}
                                            </Badge>
                                        </div>
                                        
                                        <div className="grid grid-cols-3 gap-2 text-xs text-gray-500 mb-3">
                                            <div className="flex items-center gap-1">
                                                <Users className="h-3 w-3" />
                                                <span>{roomType.capacity} students</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Bed className="h-3 w-3" />
                                                <span>{roomType.beds} beds</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Building2 className="h-3 w-3" />
                                                <span>{roomType.available_rooms} available</span>
                                            </div>
                                        </div>
                                        
                                        {roomType.is_active ? (
                                            <a 
                                                href={`/bookings/create?hostel_id=${hostel.id}&room_type_id=${roomType.id}`}
                                                className="block w-full"

                                            >
                                                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 text-sm font-medium rounded-full">
                                                    Apply for this room
                                                </Button>
                                            </a>
                                        ) : (
                                            <Button 
                                                className="w-full bg-gray-400 text-white px-6 py-3 text-sm font-medium rounded-full"
                                                disabled
                                            >
                                                Registration Closed
                                            </Button>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* House Rules */}
                        {hostel.house_rules && (
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Hall Rules & Regulations</h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                    {hostel.house_rules}
                                </p>
                            </div>
                        )}

                        {/* Cancellation Policy */}
                        {hostel.cancellation_policy && (
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Registration Policy</h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                    {hostel.cancellation_policy}
                                </p>
                            </div>
                        )}

                        {/* Recent Applications (Admin Only) */}
                        {can_edit && recent_bookings.length > 0 && (
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Applications</h3>
                                <div className="space-y-3">
                                    {recent_bookings.slice(0, 3).map((booking) => (
                                        <div key={booking.id} className="flex justify-between items-center text-sm p-3 bg-gray-50 rounded-lg">
                                            <div>
                                                <div className="font-medium text-gray-900">{booking.user.name}</div>
                                                <div className="text-gray-500">{booking.booking_reference}</div>
                                            </div>
                                            <div className="text-right">
                                                <div className="font-medium text-gray-900">
                                                    GH₵{booking.total_amount.toLocaleString()}
                                                </div>
                                                <Badge className="text-xs px-4 py-2 rounded-full" variant="outline">
                                                    {booking.status}
                                                </Badge>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Display YouTube video links: */}
                        {hostel.youtube_links && hostel.youtube_links.length > 0 && (
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">YouTube Videos</h3>
                                <div className="space-y-3">
                                    {hostel.youtube_links.map((link, idx) => (
                                        <div key={idx}>
                                            <a href={link} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">YouTube Video {idx + 1}</a>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
} 