import { Head, useForm, usePage } from '@inertiajs/react';
import { LoaderCircle, User, Building2, Check } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AuthLayout from '@/layouts/auth-layout';

type RegisterForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    role: string;
    phone: string;
    gender: string;
    university: string;
    student_id: string;
    year_of_study: string;
    emergency_contact_name: string;
    emergency_contact_phone: string;
};

export default function Register() {
    const { genders = [], years_of_study = [], universities = [] } = usePage().props as any;
    const [isStudent, setIsStudent] = useState(true);
    
    const { data, setData, post, processing, errors, reset } = useForm<Required<RegisterForm>>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'student',
        phone: '',
        gender: '',
        university: '',
        student_id: '',
        year_of_study: '',
        emergency_contact_name: '',
        emergency_contact_phone: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    const handleRoleChange = (role: string) => {
        setData('role', role);
        setIsStudent(role === 'student');
        
        // Clear student-specific fields if not a student
        if (role !== 'student') {
            setData({
                ...data,
                role,
                university: '',
                student_id: '',
                year_of_study: '',
            });
        }
    };

    return (
        <AuthLayout title="" description="">
            <Head title="Register" />
            
            <div className="bg-white">
                <form className="space-y-12" onSubmit={submit}>
                {/* Account Type Selection */}
                    <div className="space-y-6">
                        <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                    <span className="text-primary font-semibold text-sm">1</span>
                                </div>
                                <h2 className="text-xl font-semibold">Account Type</h2>
                            </div>
                            <p className="text-muted-foreground">Choose the type of account you want to create</p>
                        </div>
                        
                        <div>
                            <button
                                type="button"
                                onClick={() => handleRoleChange('student')}
                                className={`w-full group relative rounded-lg border-2 p-6 text-left transition-all duration-200 hover:scale-[1.02] hover:shadow-lg ${
                                    data.role === 'student'
                                        ? 'border-primary bg-primary/5 shadow-md ring-2 ring-primary/20'
                                        : 'border-gray-200 hover:border-primary/50 dark:border-gray-700'
                                }`}
                            >
                                <div className="flex items-start gap-4">
                                    <div className={`rounded-full p-3 transition-colors ${
                                        data.role === 'student'
                                            ? 'bg-primary text-primary-foreground' 
                                            : 'bg-gray-100 text-gray-600 group-hover:bg-primary/10 group-hover:text-primary dark:bg-gray-800 dark:text-gray-400'
                                    }`}>
                                        <User className="h-5 w-5" />
                                    </div>
                                    <div className="flex-1">
                                        <div className="font-semibold text-lg mb-1">Student</div>
                                        <div className="text-sm text-muted-foreground">Find and book hostel accommodation</div>
                                    </div>
                                    {data.role === 'student' && (
                                        <div className="absolute top-4 right-4 h-6 w-6 rounded-full bg-primary flex items-center justify-center">
                                            <Check className="h-4 w-4 text-primary-foreground" />
                                        </div>
                                    )}
                                </div>
                            </button>
                            
                        </div>
                        <InputError message={errors.role} className="mt-3" />
                    </div>

                {/* Basic Information */}
                    <div className="space-y-6">
                        <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                    <span className="text-primary font-semibold text-sm">2</span>
                                </div>
                                <h2 className="text-xl font-semibold">Basic Information</h2>
                            </div>
                            <p className="text-muted-foreground">Your personal details</p>
                        </div>
                        
                        <div className="space-y-6">
                            <div className="grid gap-6 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-sm font-medium">Full Name *</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    required
                                    autoFocus
                                    tabIndex={1}
                                    autoComplete="name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    disabled={processing}
                                    placeholder="John Doe"
                                        className="h-11"
                                />
                                <InputError message={errors.name} className="mt-1" />
                            </div>

                                <div className="space-y-2">
                                    <Label htmlFor="email" className="text-sm font-medium">Email Address *</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    required
                                    tabIndex={2}
                                    autoComplete="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    disabled={processing}
                                    placeholder="<EMAIL>"
                                        className="h-11"
                                />
                                <InputError message={errors.email} className="mt-1" />
                            </div>

                                <div className="space-y-2">
                                    <Label htmlFor="phone" className="text-sm font-medium">Phone Number *</Label>
                                <Input
                                    id="phone"
                                    type="tel"
                                    required
                                    tabIndex={3}
                                    value={data.phone}
                                    onChange={(e) => setData('phone', e.target.value)}
                                    disabled={processing}
                                    placeholder="+233 XX XXX XXXX"
                                        className="h-11"
                                />
                                <InputError message={errors.phone} className="mt-1" />
                            </div>

                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="gender" className="text-sm font-medium">Gender *</Label>
                            <Select value={data.gender} onValueChange={(value) => setData('gender', value)}>
                                    <SelectTrigger className="h-11">
                                    <SelectValue placeholder="Select your gender" />
                                </SelectTrigger>
                                <SelectContent>
                                    {genders.map((gender: { value: string; label: string }) => (
                                        <SelectItem key={gender.value} value={gender.value}>{gender.label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <InputError message={errors.gender} className="mt-1" />
                        </div>
                        </div>
                    </div>

                {/* Student Information */}
                {isStudent && (
                        <div className="space-y-6">
                            <div className="text-center">
                                <div className="flex items-center justify-center gap-2 mb-2">
                                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                        <span className="text-primary font-semibold text-sm">3</span>
                                    </div>
                                    <h2 className="text-xl font-semibold">Student Information</h2>
                                </div>
                                <p className="text-muted-foreground">Your academic details</p>
                            </div>
                            
                            <div className="space-y-6">
                                <div className="space-y-2">
                                    <Label htmlFor="university" className="text-sm font-medium">University *</Label>
                                <Select value={data.university} onValueChange={(value) => setData('university', value)}>
                                        <SelectTrigger className="h-11">
                                        <SelectValue placeholder="Select your university" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {universities.map((uni: { id: number; name: string }) => (
                                            <SelectItem key={uni.id} value={uni.name}>{uni.name}</SelectItem>
                                        ))}
                                        <SelectItem key="other" value="Other">Other</SelectItem>
                                    </SelectContent>
                                </Select>
                                <InputError message={errors.university} className="mt-1" />
                            </div>

                                <div className="grid gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="student_id" className="text-sm font-medium">Student ID *</Label>
                                    <Input
                                        id="student_id"
                                        type="text"
                                        required={isStudent}
                                        value={data.student_id}
                                        onChange={(e) => setData('student_id', e.target.value)}
                                        disabled={processing}
                                        placeholder="20210001"
                                            className="h-11"
                                    />
                                    <InputError message={errors.student_id} className="mt-1" />
                                </div>

                                    {/* Remove Program of Study section */}
                                    <div className="space-y-2">
                                        <Label htmlFor="year_of_study" className="text-sm font-medium">Year of Study *</Label>
                                    <Select value={data.year_of_study} onValueChange={(value) => setData('year_of_study', value)}>
                                            <SelectTrigger className="h-11">
                                            <SelectValue placeholder="Select year" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {years_of_study.map((year: { value: string; label: string }) => (
                                                <SelectItem key={year.value} value={year.value}>{year.label}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.year_of_study} className="mt-1" />
                                </div>
                            </div>

                            </div>
                        </div>
                )}

                {/* Emergency Contact */}
                    <div className="space-y-6">
                        <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                    <span className="text-primary font-semibold text-sm">{isStudent ? '4' : '3'}</span>
                                </div>
                                <h2 className="text-xl font-semibold">Emergency Contact</h2>
                            </div>
                            <p className="text-muted-foreground">Person to contact in case of emergency</p>
                        </div>
                        
                        <div className="space-y-6">
                            <div className="grid gap-6 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="emergency_contact_name" className="text-sm font-medium">Contact Name *</Label>
                                <Input
                                    id="emergency_contact_name"
                                    type="text"
                                    required
                                    value={data.emergency_contact_name}
                                    onChange={(e) => setData('emergency_contact_name', e.target.value)}
                                    disabled={processing}
                                    placeholder="Parent/Guardian Name"
                                        className="h-11"
                                />
                                <InputError message={errors.emergency_contact_name} className="mt-1" />
                            </div>

                                <div className="space-y-2">
                                    <Label htmlFor="emergency_contact_phone" className="text-sm font-medium">Contact Phone *</Label>
                                <Input
                                    id="emergency_contact_phone"
                                    type="tel"
                                    required
                                    value={data.emergency_contact_phone}
                                    onChange={(e) => setData('emergency_contact_phone', e.target.value)}
                                    disabled={processing}
                                    placeholder="+233 XX XXX XXXX"
                                        className="h-11"
                                />
                                <InputError message={errors.emergency_contact_phone} className="mt-1" />
                                </div>
                            </div>
                        </div>
                    </div>

                {/* Password */}
                    <div className="space-y-6">
                        <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                    <span className="text-primary font-semibold text-sm">{isStudent ? '5' : '4'}</span>
                                </div>
                                <h2 className="text-xl font-semibold">Security</h2>
                            </div>
                            <p className="text-muted-foreground">Create a secure password for your account</p>
                        </div>
                        
                        <div className="space-y-6">
                            <div className="grid gap-6 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="password" className="text-sm font-medium">Password *</Label>
                                <Input
                                    id="password"
                                    type="password"
                                    required
                                    autoComplete="new-password"
                                    value={data.password}
                                    onChange={(e) => setData('password', e.target.value)}
                                    disabled={processing}
                                    placeholder="Min. 8 characters"
                                        className="h-11"
                                />
                                <InputError message={errors.password} className="mt-1" />
                            </div>

                                <div className="space-y-2">
                                    <Label htmlFor="password_confirmation" className="text-sm font-medium">Confirm Password *</Label>
                                <Input
                                    id="password_confirmation"
                                    type="password"
                                    required
                                    autoComplete="new-password"
                                    value={data.password_confirmation}
                                    onChange={(e) => setData('password_confirmation', e.target.value)}
                                    disabled={processing}
                                    placeholder="Repeat password"
                                        className="h-11"
                                />
                                <InputError message={errors.password_confirmation} className="mt-1" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="space-y-4">
                        <Button type="submit" className="w-full h-12 text-base font-semibold rounded-full" disabled={processing}>
                            {processing && <LoaderCircle className="mr-2 h-5 w-5 animate-spin" />}
                    Create Account
                </Button>

                <div className="text-center text-sm text-muted-foreground">
                    Already have an account?{' '}
                            <TextLink href={route('login')} tabIndex={6} className="font-medium">
                        Log in
                    </TextLink>
                        </div>
                </div>
            </form>
            </div>
        </AuthLayout>
    );
}
