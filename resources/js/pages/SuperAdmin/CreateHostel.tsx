import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm } from '@inertiajs/react';
import { ArrowLeft, Upload, X, Plus, ImageIcon, AlertCircle, Building2, MapPin, Phone, Mail, Globe, Clock, FileText } from 'lucide-react';
import { useState, useRef, useCallback } from 'react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface HostelAdmin {
    id: string;
    name: string;
    email: string;
}

interface Props {
    hostel_admins: HostelAdmin[];
    universities: { id: number; name: string }[];
}

interface SuperAdminHostelForm {
    name: string;
    description: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude: string;
    longitude: string;
    phone: string;
    email: string;
    website: string;

    house_rules: string;
    cancellation_policy: string;
    amenities: string[];
    photos: File[];
    payment_period: 'month' | 'semester' | 'year';
    hostel_type: 'university' | 'homestel' | 'other';
    youtube_links: string[];
    owner_id: string;
    is_active: boolean;
    is_verified: boolean;
    allow_deposit: boolean;
    is_per_semester: boolean;
    room_types: RoomTypeForm[];
    university_id?: string;
    
    // New pricing fields
    base_price: string;
    service_fee: string;
    deposit_percentage: string;
    cancellation_fee: string;
    
    [key: string]: string | boolean | string[] | File[] | File | 'month' | 'semester' | 'year' | 'university' | 'homestel' | 'other' | RoomTypeForm[] | string | number | undefined;
}

interface PhotoPreview {
    file: File;
    url: string;
    id: string;
}

interface RoomTypeForm {
    id: string; // Used for unique key and removal
    name: string;
    capacity: number; // Capacity (persons)
    number_of_beds: number;
    male_rooms: number; // New for Feature 2
    female_rooms: number; // New for Feature 2
    mixed_rooms: number; // New for Feature 2
    [key: string]: string | number;
}

const ghanaianRegions = [
    'Greater Accra', 'Ashanti', 'Central', 'Eastern', 'Northern', 'Upper East',
    'Upper West', 'Volta', 'Western', 'Western North', 'Brong Ahafo',
    'Ahafo', 'Bono East', 'Oti', 'North East', 'Savannah'
];

const amenitiesList = [
    'wifi', 'parking', 'laundry', 'air_conditioning', 'kitchen', 'dining_hall',
    'study_rooms', 'common_room', 'library', 'sports_facilities', 'security',
    'generator', 'water_supply', 'cleaning_service'
];

export default function CreateHostel({ hostel_admins, universities }: Props) {
    const { data, setData, post, processing, errors } = useForm<SuperAdminHostelForm>({
        name: '',
        description: '',
        address: '',
        city: '',
        state: '',
        country: 'Ghana',
        postal_code: '',
        latitude: '',
        longitude: '',
        phone: '',
        email: '',
        website: '',
        house_rules: '',
        cancellation_policy: '',
        amenities: [],
        photos: [],
        payment_period: 'month',
        hostel_type: 'university',
        youtube_links: [''],
        owner_id: '',
        is_active: true,
        is_verified: false,
        allow_deposit: false,
        is_per_semester: true,
        room_types: [],
        
        // New pricing fields
        base_price: '',
        service_fee: '',
        deposit_percentage: '',
        cancellation_fee: '',
    });

    const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
    const [photoPreviews, setPhotoPreviews] = useState<PhotoPreview[]>([]);
    const [isDragOver, setIsDragOver] = useState(false);
    const [uploadErrors, setUploadErrors] = useState<string[]>([]);
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [roomTypes, setRoomTypes] = useState<RoomTypeForm[]>([]);

    const [youtubeLinks, setYoutubeLinks] = useState<string[]>(['']);

    // Helper function to get payment period text
    const getPaymentPeriodText = () => {
        switch (data.payment_period) {
            case 'month':
                return 'Month';
            case 'semester':
                return 'Semester';
            case 'year':
                return 'Academic Year';
            default:
                return 'Period';
        }
    };

    const validateFile = (file: File): string | null => {
        // Check file type
        if (!file.type.startsWith('image/')) {
            return `${file.name} is not an image file`;
        }

        // Check file size (50MB max)
        if (file.size > 50 * 1024 * 1024) {
            return `${file.name} is too large (max 50MB)`;
        }

        // Check file format
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            return `${file.name} format not supported (use JPEG, PNG, or WebP)`;
        }

        return null;
    };

    const processFiles = useCallback((files: FileList | File[]) => {
        const fileArray = Array.from(files);
        const errors: string[] = [];
        const validFiles: File[] = [];
        const newPreviews: PhotoPreview[] = [];

        // Check total files limit
        if (photoPreviews.length + fileArray.length > 10) {
            errors.push('Maximum 10 photos allowed');
            return;
        }

        fileArray.forEach((file) => {
            const error = validateFile(file);
            if (error) {
                errors.push(error);
            } else {
                validFiles.push(file);
                newPreviews.push({
                    file,
                    url: URL.createObjectURL(file),
                    id: Math.random().toString(36).substring(2),
                });
            }
        });

        setUploadErrors(errors);
        
        if (validFiles.length > 0) {
            const updatedPhotos = [...data.photos, ...validFiles];
            const updatedPreviews = [...photoPreviews, ...newPreviews];
            
            setData('photos', updatedPhotos);
            setPhotoPreviews(updatedPreviews);
        }
    }, [data.photos, photoPreviews]);

    const handlePhotoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files) {
            processFiles(files);
        }
        // Reset input value so same file can be selected again
        e.target.value = '';
    };

    const handleRemovePhoto = (photoId: string) => {
        const photoIndex = photoPreviews.findIndex(p => p.id === photoId);
        if (photoIndex !== -1) {
            // Revoke the object URL to free memory
            URL.revokeObjectURL(photoPreviews[photoIndex].url);
            
            // Remove from previews and data
            const updatedPreviews = photoPreviews.filter(p => p.id !== photoId);
            const updatedPhotos = data.photos.filter((_, index) => index !== photoIndex);
            
            setPhotoPreviews(updatedPreviews);
            setData('photos', updatedPhotos);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
        
        const files = e.dataTransfer.files;
        if (files) {
            processFiles(files);
        }
    };

    const handleAmenityToggle = (amenity: string) => {
        setSelectedAmenities(prev => 
            prev.includes(amenity) 
                ? prev.filter(a => a !== amenity)
                : [...prev, amenity]
        );
    };

    const handleAddRoomType = () => {
        setRoomTypes([...roomTypes, { 
            id: Math.random().toString(36).substring(2), 
            name: '', 
            capacity: 1, 
            number_of_beds: 1, 
            male_rooms: 0, 
            female_rooms: 0, 
            mixed_rooms: 0 
        }]);
    };

    const handleRemoveRoomType = (id: string) => {
        setRoomTypes(roomTypes.filter(rt => rt.id !== id));
    };

    const handleRoomTypeChange = (id: string, field: keyof RoomTypeForm, value: string | number) => {
        setRoomTypes(roomTypes.map(rt => {
            if (rt.id === id) {
                if (['capacity', 'number_of_beds', 'male_rooms', 'female_rooms', 'mixed_rooms'].includes(field as string)) {
                    // Convert to number, defaulting to 0 if NaN
                    return { ...rt, [field]: parseFloat(value as string) || 0 };
                }
                return { ...rt, [field]: value };
            }
            return rt;
        }));
    };

    const handleYoutubeLinkChange = (index: number, value: string) => {
        const newLinks = [...youtubeLinks];
        newLinks[index] = value;
        setYoutubeLinks(newLinks);
    };

    const addYoutubeLink = () => {
        setYoutubeLinks([...youtubeLinks, '']);
    };

    const removeYoutubeLink = (index: number) => {
        const newLinks = [...youtubeLinks];
        newLinks.splice(index, 1);
        setYoutubeLinks(newLinks);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        

        
        // Update the data state with all current values
        const formData = {
            ...data,
            amenities: selectedAmenities,
            room_types: roomTypes.filter(rt => rt.name.trim()),
            youtube_links: youtubeLinks.filter(link => link.trim())
        };
        
        // Use router.post with proper FormData for file uploads
        const submitData = new FormData();
        
        // Add all form fields except arrays and files
        Object.entries(formData).forEach(([key, value]) => {
            if (key !== 'photos' && key !== 'amenities' && key !== 'room_types' && key !== 'youtube_links') {
                if (value !== null && value !== undefined) {
                    // Handle boolean values properly for Laravel validation
                    if (typeof value === 'boolean') {
                        submitData.append(key, value ? '1' : '0');
                    } else {
                        submitData.append(key, value.toString());
                    }
                }
            }
        });
        
        // Add amenities
        selectedAmenities.forEach((amenity, index) => {
            submitData.append(`amenities[${index}]`, amenity);
        });
        
        // Add photos
        data.photos.forEach((photo, index) => {
            submitData.append(`photos[${index}]`, photo);
        });
        
        // Add room types
        roomTypes.filter(rt => rt.name.trim()).forEach((roomType, index) => {
            submitData.append(`room_types[${index}][name]`, roomType.name.trim());
            submitData.append(`room_types[${index}][capacity]`, Math.max(1, roomType.capacity || 1).toString());
            submitData.append(`room_types[${index}][beds]`, Math.max(1, roomType.number_of_beds || 1).toString());
            const totalRooms = (roomType.male_rooms || 0) + (roomType.female_rooms || 0) + (roomType.mixed_rooms || 0);
            submitData.append(`room_types[${index}][available_rooms]`, Math.max(1, totalRooms || 1).toString());
            submitData.append(`room_types[${index}][semester_type]`, 'both');
        });
        
        // Add YouTube links
        youtubeLinks.filter(link => link.trim()).forEach((link, index) => {
            submitData.append(`youtube_links[${index}]`, link.trim());
        });
        
        router.post('/admin/hostels', submitData, {
            forceFormData: true,
            onProgress: (progress) => {
                setUploadProgress(Math.round((progress.loaded / progress.total) * 100));
            },
            onStart: () => {
                setIsUploading(true);
                setUploadProgress(0);
            },
            onSuccess: () => {
                setIsUploading(false);
                setUploadProgress(100);
                router.visit('/admin/hostels');
            },
            onError: (errors: any) => {
                setIsUploading(false);
                setUploadProgress(0);
                setUploadErrors(['Form submission failed. Please check the errors above.']);
            },
            onFinish: () => {
                setIsUploading(false);
            }
        });
    };

    const showUniversitySelect = data.hostel_type === 'university';

    return (
        <AppLayout>
            <Head title="Create Hostel" />
            
            <div className="flex h-full flex-1 flex-col gap-4 md:gap-8 p-4 md:p-6 bg-white">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.visit('/admin/hostels')}
                        className="rounded-full"
                    >
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Create New Hostel</h1>
                        <p className="mt-1 text-sm md:text-base text-gray-600">Add a new hostel to the platform</p>
                    </div>
                </div>

                {/* Form */}
                <div className="w-full">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Display any general form errors */}
                        {Object.keys(errors).length > 0 && (
                            <Alert className="border-red-200 bg-red-50">
                                <AlertCircle className="h-4 w-4 text-red-600" />
                                <AlertDescription className="text-red-800">
                                    <div className="font-medium mb-2">Please fix the following errors:</div>
                                    <ul className="list-disc list-inside space-y-1">
                                        {Object.entries(errors).map(([field, message]) => (
                                            <li key={field} className="text-sm">
                                                <strong>{field}:</strong> {message}
                                            </li>
                                        ))}
                                    </ul>
                                </AlertDescription>
                            </Alert>
                        )}
                        
                        {/* Basic Information */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="md:col-span-2">
                                    <Label htmlFor="name">Hostel Name *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={e => setData('name', e.target.value)}
                                        className="mt-1"
                                        placeholder="Enter hostel name"
                                        required
                                    />
                                    {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                                </div>
                                
                                <div className="md:col-span-2">
                                    <Label htmlFor="description">Description *</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={e => setData('description', e.target.value)}
                                        className="mt-1"
                                        placeholder="Describe the hostel, its features, and what makes it special..."
                                        rows={4}
                                        required
                                    />
                                    {errors.description && <p className="text-sm text-red-600 mt-1">{errors.description}</p>}
                                </div>

                                <div className="md:col-span-2">
                                    <Label htmlFor="owner_id">Hostel Owner *</Label>
                                    <select
                                        id="owner_id"
                                        value={data.owner_id}
                                        onChange={e => setData('owner_id', e.target.value)}
                                        className="w-full mt-1 rounded-md border border-gray-300 px-3 py-2"
                                        required
                                    >
                                        <option value="">Select a hostel admin</option>
                                        {hostel_admins.map(admin => (
                                            <option key={admin.id} value={admin.id}>
                                                {admin.name} ({admin.email})
                                            </option>
                                        ))}
                                    </select>
                                    {errors.owner_id && <p className="text-sm text-red-600 mt-1">{errors.owner_id}</p>}
                                </div>
                                {showUniversitySelect && (
                                    <div className="md:col-span-2">
                                        <Label htmlFor="university_id">University *</Label>
                                        <select
                                            id="university_id"
                                            value={data.university_id || ''}
                                            onChange={e => setData('university_id', e.target.value)}
                                            className="w-full mt-1 rounded-md border border-gray-300 px-3 py-2"
                                            required={showUniversitySelect}
                                        >
                                            <option value="">Select a university</option>
                                            {universities.map(u => (
                                                <option key={u.id} value={u.id}>{u.name}</option>
                                            ))}
                                        </select>
                                        {errors.university_id && <p className="text-sm text-red-600 mt-1">{errors.university_id}</p>}
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Location */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Location</h2>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="md:col-span-2">
                                    <Label htmlFor="address">Address *</Label>
                                    <Input
                                        id="address"
                                        value={data.address}
                                        onChange={e => setData('address', e.target.value)}
                                        className="mt-1"
                                        placeholder="Enter full address"
                                        required
                                    />
                                    {errors.address && <p className="text-sm text-red-600 mt-1">{errors.address}</p>}
                                </div>
                                
                                <div>
                                    <Label htmlFor="city">City *</Label>
                                    <Input
                                        id="city"
                                        value={data.city}
                                        onChange={e => setData('city', e.target.value)}
                                        className="mt-1"
                                        placeholder="Enter city"
                                        required
                                    />
                                    {errors.city && <p className="text-sm text-red-600 mt-1">{errors.city}</p>}
                                </div>
                                
                                <div>
                                    <Label htmlFor="state">State/Region *</Label>
                                    <Select onValueChange={(value) => setData('state', value)} required>
                                        <SelectTrigger className="mt-1 w-full">
                                            <SelectValue placeholder="Select Region" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {ghanaianRegions.map((region) => (
                                                <SelectItem key={region} value={region}>
                                                    {region}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.state && <p className="text-sm text-red-600 mt-1">{errors.state}</p>}
                                </div>
                                
                                <div>
                                    <Label htmlFor="country">Country *</Label>
                                    <Input
                                        id="country"
                                        value={data.country}
                                        onChange={e => setData('country', e.target.value)}
                                        className="mt-1"
                                        required
                                    />
                                    {errors.country && <p className="text-sm text-red-600 mt-1">{errors.country}</p>}
                                </div>

                                <div>
                                    <Label htmlFor="postal_code">Postal Code</Label>
                                    <Input
                                        id="postal_code"
                                        value={data.postal_code}
                                        onChange={(e) => setData('postal_code', e.target.value)}
                                        placeholder="00233"
                                    />
                                    {errors.postal_code && (
                                        <p className="text-sm text-red-600 mt-1">{errors.postal_code}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="latitude">Latitude</Label>
                                    <Input
                                        id="latitude"
                                        value={data.latitude}
                                        onChange={(e) => setData('latitude', e.target.value)}
                                        placeholder="5.6037"
                                    />
                                    {errors.latitude && (
                                        <p className="text-sm text-red-600 mt-1">{errors.latitude}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="longitude">Longitude</Label>
                                    <Input
                                        id="longitude"
                                        value={data.longitude}
                                        onChange={(e) => setData('longitude', e.target.value)}
                                        placeholder="-0.1870"
                                    />
                                    {errors.longitude && (
                                        <p className="text-sm text-red-600 mt-1">{errors.longitude}</p>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Operational Details */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Operational Details</h2>
                            <div className="grid gap-4 md:grid-cols-2">
                            </div>

                            <div className="mt-4">
                                <Label htmlFor="house_rules">Hostel Rules</Label>
                                <Textarea
                                    id="house_rules"
                                    value={data.house_rules}
                                    onChange={(e) => setData('house_rules', e.target.value)}
                                    placeholder="e.g., Quiet hours from 10 PM to 6 AM. No opposite gender visitors after 9 PM..."
                                    rows={3}
                                />
                                {errors.house_rules && (
                                    <p className="text-sm text-red-600 mt-1">{errors.house_rules}</p>
                                )}
                            </div>

                            <div className="mt-4">
                                <Label htmlFor="cancellation_policy">Cancellation Policy</Label>
                                <Textarea
                                    id="cancellation_policy"
                                    value={data.cancellation_policy}
                                    onChange={(e) => setData('cancellation_policy', e.target.value)}
                                    placeholder="e.g., Cancellation allowed up to 2 weeks before semester start with full refund..."
                                    rows={3}
                                />
                                {errors.cancellation_policy && (
                                    <p className="text-sm text-red-600 mt-1">{errors.cancellation_policy}</p>
                                )}
                            </div>
                        </div>

                        {/* Booking & Payment Settings (Feature 1 & 3) */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Booking & Payment Settings</h2>
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="payment_period">Payment Period *</Label>
                                    <Select
                                        value={data.payment_period}
                                        onValueChange={(value: 'month' | 'semester' | 'year') => setData('payment_period', value)}
                                        required
                                    >
                                        <SelectTrigger className="mt-1 w-full">
                                            <SelectValue placeholder="Select Payment Period" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="month">Per Month</SelectItem>
                                            <SelectItem value="semester">Per Semester</SelectItem>
                                            <SelectItem value="year">Per Academic Year</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.payment_period && (
                                        <p className="text-sm text-red-600 mt-1">{errors.payment_period}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="hostel_type">Hostel Type *</Label>
                                    <Select
                                        value={data.hostel_type}
                                        onValueChange={(value: 'university' | 'homestel' | 'other') => setData('hostel_type', value)}
                                        required
                                    >
                                        <SelectTrigger className="mt-1 w-full">
                                            <SelectValue placeholder="Select Hostel Type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="university">Hostel</SelectItem>
                                            <SelectItem value="homestel">Homestel</SelectItem>
                                            <SelectItem value="other">Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.hostel_type && (
                                        <p className="text-sm text-red-600 mt-1">{errors.hostel_type}</p>
                                    )}
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="allow_deposit"
                                        checked={data.allow_deposit}
                                        onCheckedChange={(checked) => setData('allow_deposit', checked as boolean)}
                                    />
                                    <Label htmlFor="allow_deposit" className="text-sm font-medium leading-none">
                                        Allow part-payment (deposit)
                                    </Label>
                                    {errors.allow_deposit && <p className="text-sm text-red-600 mt-1">{errors.allow_deposit}</p>}
                                </div>

                                {/* Pricing Section */}
                                <div className="border-t pt-4">
                                    <h3 className="text-md font-medium text-gray-900 mb-3">Pricing Information</h3>
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="base_price">Base Price per {getPaymentPeriodText()} (GHS) *</Label>
                                            <Input
                                                id="base_price"
                                                type="number"
                                                value={data.base_price || ''}
                                                onChange={(e) => setData('base_price', e.target.value)}
                                                placeholder={`e.g., ${data.payment_period === 'month' ? '500' : data.payment_period === 'semester' ? '2000' : '4000'}`}
                                                required
                                                min={0}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Starting price for the most basic room type per {getPaymentPeriodText().toLowerCase()}</p>
                                            {errors.base_price && <p className="text-sm text-red-600 mt-1">{errors.base_price}</p>}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="service_fee">Service Fee (GHS)</Label>
                                            <Input
                                                id="service_fee"
                                                type="number"
                                                value={data.service_fee || ''}
                                                onChange={(e) => setData('service_fee', e.target.value)}
                                                placeholder="e.g., 50"
                                                min={0}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">One-time service fee per booking</p>
                                            {errors.service_fee && <p className="text-sm text-red-600 mt-1">{errors.service_fee}</p>}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="deposit_percentage">Deposit Percentage (%)</Label>
                                            <Input
                                                id="deposit_percentage"
                                                type="number"
                                                value={data.deposit_percentage || ''}
                                                onChange={(e) => setData('deposit_percentage', e.target.value)}
                                                placeholder="e.g., 30"
                                                min={0}
                                                max={100}
                                                disabled={!data.allow_deposit}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Percentage required as deposit when partial payment is allowed</p>
                                            {errors.deposit_percentage && <p className="text-sm text-red-600 mt-1">{errors.deposit_percentage}</p>}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="cancellation_fee">Cancellation Fee (GHS)</Label>
                                            <Input
                                                id="cancellation_fee"
                                                type="number"
                                                value={data.cancellation_fee || ''}
                                                onChange={(e) => setData('cancellation_fee', e.target.value)}
                                                placeholder="e.g., 100"
                                                min={0}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Fee charged for booking cancellations</p>
                                            {errors.cancellation_fee && <p className="text-sm text-red-600 mt-1">{errors.cancellation_fee}</p>}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Hostel Amenities (Feature 2) */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Hostel Amenities</h2>
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                {amenitiesList.map((amenity) => (
                                    <label
                                        key={amenity}
                                        className="flex items-center space-x-2 cursor-pointer"
                                    >
                                        <input
                                            type="checkbox"
                                            checked={selectedAmenities.includes(amenity)}
                                            onChange={() => handleAmenityToggle(amenity)}
                                            className="rounded border-gray-300"
                                        />
                                        <span className="text-sm capitalize">
                                            {amenity.replace('_', ' ')}
                                        </span>
                                    </label>
                                ))}
                            </div>
                            {errors.amenities && <p className="text-sm text-red-600 mt-1">{errors.amenities}</p>}
                        </div>

                        {/* YouTube Video Links (Feature 6) */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">YouTube Video Links</h2>
                            <div className="space-y-4">
                                {youtubeLinks.map((link, index) => (
                                    <div key={index} className="flex items-center gap-2">
                                        <Input
                                            type="url"
                                            value={link}
                                            onChange={(e) => handleYoutubeLinkChange(index, e.target.value)}
                                            placeholder="https://www.youtube.com/watch?v=yourvideo"
                                        />
                                        {youtubeLinks.length > 1 && (
                                            <Button type="button" variant="destructive" onClick={() => removeYoutubeLink(index)}>
                                                <X className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button type="button" variant="outline" onClick={addYoutubeLink}>
                                    Add Another Video Link
                                </Button>
                                {errors.youtube_links && (
                                    <p className="text-sm text-red-600 mt-1">{errors.youtube_links}</p>
                                )}
                            </div>
                        </div>

                        {/* Room Types (Feature 2) */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Room Types</h2>
                            <div className="space-y-4">
                                {roomTypes.length === 0 && (
                                    <div className="text-center py-8 border-2 border-dashed rounded-lg">
                                        <p className="text-gray-500 mb-4">No room types added yet</p>
                                        <Button type="button" onClick={handleAddRoomType} variant="outline">
                                            Add First Room Type
                                        </Button>
                                    </div>
                                )}
                                {roomTypes.map((roomType, index) => (
                                    <div key={roomType.id} className="border p-4 rounded-lg relative">
                                        <Button
                                            type="button"
                                            variant="destructive"
                                            size="icon"
                                            className="absolute top-2 right-2 rounded-full h-6 w-6 z-10"
                                            onClick={() => handleRemoveRoomType(roomType.id)}
                                        >
                                            <X className="h-3 w-3" />
                                        </Button>
                                        <h4 className="font-semibold mb-4">Room Type {index + 1}</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <div>
                                                <Label htmlFor={`room_type_${index}_name`}>Room Type Name *</Label>
                                                <Input
                                                    id={`room_type_${index}_name`}
                                                    value={roomType.name}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'name', e.target.value)}
                                                    placeholder="e.g., 2 in 1"
                                                    required
                                                />
                                                {errors[`room_types.${index}.name`] && <p className="text-sm text-red-600 mt-1">{errors[`room_types.${index}.name`]}</p>}
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_capacity`}>Capacity (persons) *</Label>
                                                <Input
                                                    id={`room_type_${index}_capacity`}
                                                    type="number"
                                                    value={roomType.capacity || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'capacity', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    required
                                                    min={1}
                                                />
                                                {errors[`room_types.${index}.capacity`] && <p className="text-sm text-red-600 mt-1">{errors[`room_types.${index}.capacity`]}</p>}
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_beds`}>Number of Beds *</Label>
                                                <Input
                                                    id={`room_type_${index}_beds`}
                                                    type="number"
                                                    value={roomType.number_of_beds || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'number_of_beds', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    required
                                                    min={1}
                                                />
                                                {errors[`room_types.${index}.number_of_beds`] && <p className="text-sm text-red-600 mt-1">{errors[`room_types.${index}.number_of_beds`]}</p>}
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_male_rooms`}>Number of Male Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_male_rooms`}
                                                    type="number"
                                                    value={roomType.male_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'male_rooms', e.target.value)}
                                                    placeholder="e.g., 5"
                                                    min={0}
                                                />
                                                {errors[`room_types.${index}.male_rooms`] && <p className="text-sm text-red-600 mt-1">{errors[`room_types.${index}.male_rooms`]}</p>}
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_female_rooms`}>Number of Female Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_female_rooms`}
                                                    type="number"
                                                    value={roomType.female_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'female_rooms', e.target.value)}
                                                    placeholder="e.g., 5"
                                                    min={0}
                                                />
                                                {errors[`room_types.${index}.female_rooms`] && <p className="text-sm text-red-600 mt-1">{errors[`room_types.${index}.female_rooms`]}</p>}
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_mixed_rooms`}>Number of Mixed Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_mixed_rooms`}
                                                    type="number"
                                                    value={roomType.mixed_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'mixed_rooms', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    min={0}
                                                />
                                                {errors[`room_types.${index}.mixed_rooms`] && <p className="text-sm text-red-600 mt-1">{errors[`room_types.${index}.mixed_rooms`]}</p>}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                <Button type="button" onClick={handleAddRoomType} variant="outline" className="w-full">
                                    Add Another Room Type
                                </Button>
                            </div>
                        </div>

                        {/* Hostel Photos */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Hostel Photos</h2>
                            <div
                                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                                    isDragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
                                }`}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                                onDrop={handleDrop}
                            >
                                <input
                                    type="file"
                                    id="photos"
                                    multiple
                                    accept="image/*"
                                    onChange={handlePhotoSelect}
                                    className="hidden"
                                />
                                <label htmlFor="photos" className="cursor-pointer">
                                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                    <p className="mt-2 text-sm text-gray-600">Drag & drop photos here, or click to browse</p>
                                    <p className="text-xs text-gray-500">(Max 10 photos)</p>
                                </label>
                            </div>
                            {errors.photos && <p className="text-sm text-red-600 mt-1">{errors.photos}</p>}
                            {/* Display upload errors */}
                            {uploadErrors.length > 0 && (
                                <Alert className="border-red-200 bg-red-50 mt-4">
                                    <AlertCircle className="h-4 w-4 text-red-600" />
                                    <AlertDescription className="text-red-800">
                                        <div className="font-medium mb-2">Photo upload errors:</div>
                                        <ul className="list-disc list-inside space-y-1">
                                            {uploadErrors.map((error, index) => (
                                                <li key={index} className="text-sm">{error}</li>
                                            ))}
                                        </ul>
                                    </AlertDescription>
                                </Alert>
                            )}
                            
                            {/* Display photo-specific validation errors */}
                            {Object.keys(errors).filter(key => key.startsWith('photos.')).length > 0 && (
                                <Alert className="border-red-200 bg-red-50 mt-4">
                                    <AlertCircle className="h-4 w-4 text-red-600" />
                                    <AlertDescription className="text-red-800">
                                        <div className="font-medium mb-2">Photo validation errors:</div>
                                        <ul className="list-disc list-inside space-y-1">
                                            {Object.keys(errors).filter(key => key.startsWith('photos.')).map((key) => (
                                                <li key={key} className="text-sm">
                                                    <strong>{key}:</strong> {errors[key]}
                                                </li>
                                            ))}
                                        </ul>
                                    </AlertDescription>
                                </Alert>
                            )}
                            
                            <div className="mt-4 grid grid-cols-2 gap-4">
                                {photoPreviews.map((preview, index) => (
                                    <div key={preview.id} className="relative">
                                        <img
                                            src={preview.url}
                                            alt={`Photo ${index + 1}`}
                                            className="h-32 w-full object-cover rounded-lg"
                                        />
                                        <Button
                                            type="button"
                                            onClick={() => handleRemovePhoto(preview.id)}
                                            variant="destructive"
                                            size="icon"
                                            className="absolute top-1 right-1 rounded-full h-6 w-6"
                                        >
                                            <X className="h-3 w-3" />
                                        </Button>
                                        <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                                            {preview.file.name}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Super Admin Specific Settings */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Admin Settings</h2>
                            <div className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="is_active"
                                        checked={data.is_active}
                                        onCheckedChange={(checked) => setData('is_active', checked as boolean)}
                                    />
                                    <Label htmlFor="is_active" className="text-sm font-medium leading-none">
                                        Hostel is Active (visible to users)
                                    </Label>
                                    {errors.is_active && <p className="text-sm text-red-600 mt-1">{errors.is_active}</p>}
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="is_verified"
                                        checked={data.is_verified}
                                        onCheckedChange={(checked) => setData('is_verified', checked as boolean)}
                                    />
                                    <Label htmlFor="is_verified" className="text-sm font-medium leading-none">
                                        Hostel is Verified
                                    </Label>
                                    {errors.is_verified && <p className="text-sm text-red-600 mt-1">{errors.is_verified}</p>}
                                </div>
                            </div>
                        </div>

                        <div className="flex justify-end gap-4">
                            <Button type="button" variant="outline" onClick={() => router.visit('/admin/hostels')}>
                                Cancel
                            </Button>
                            <div className="flex flex-col items-end gap-2">
                                {isUploading && (
                                    <div className="w-64">
                                        <div className="flex justify-between text-xs text-gray-600 mb-1">
                                            <span>Uploading hostel data...</span>
                                            <span>{uploadProgress}%</span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div 
                                                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                                style={{ width: `${uploadProgress}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                )}
                                <Button type="submit" disabled={processing || isUploading}>
                                    {isUploading ? 'Uploading...' : processing ? 'Creating...' : 'Create Hostel'}
                                </Button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}
