import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    Search, CreditCard, CheckCircle, Clock, XCircle,
    DollarSign, TrendingUp, AlertCircle, Calendar, User, Building2
} from 'lucide-react';
import { useState } from 'react';

interface Payment {
    id: string;
    booking_reference: string;
    total_amount: number;
    payment_status: string;
    status: string;
    created_at: string;
    user: {
        name: string;
        email: string;
    };
    hostel: {
        name: string;
        city: string;
    };
}

interface Props {
    payments: {
        data: Payment[];
        links: any[];
        meta: any;
    };
    stats: {
        total_payments: number;
        paid_payments: number;
        pending_payments: number;
        failed_payments: number;
        total_revenue: number;
        this_month_revenue: number;
    };
    filters: {
        status?: string;
        search?: string;
        date_from?: string;
        date_to?: string;
    };
}

export default function Payments({ payments, stats, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const handleSearch = () => {
        router.get('/admin/payments', {
            search: searchTerm,
            status: selectedStatus,
            date_from: dateFrom,
            date_to: dateTo,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatus('');
        setDateFrom('');
        setDateTo('');
        router.get('/admin/payments', {}, {
            preserveState: true,
            replace: true,
        });
    };

    const getPaymentStatusBadge = (status: string) => {
        const config = {
            paid: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Paid' },
            pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending' },
            failed: { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Failed' },
        };
        
        const statusConfig = config[status as keyof typeof config];
        if (!statusConfig) return null;
        
        const Icon = statusConfig.icon;
        return (
            <Badge className={`${statusConfig.color} gap-1`}>
                <Icon className="h-3 w-3" />
                {statusConfig.label}
            </Badge>
        );
    };

    const getBookingStatusBadge = (status: string) => {
        const config = {
            pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
            confirmed: { color: 'bg-blue-100 text-blue-800', label: 'Confirmed' },
            checked_in: { color: 'bg-green-100 text-green-800', label: 'Checked In' },
            checked_out: { color: 'bg-gray-100 text-gray-800', label: 'Completed' },
            cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' },
        };
        
        const statusConfig = config[status as keyof typeof config];
        if (!statusConfig) return null;
        
        return (
            <Badge className={statusConfig.color}>
                {statusConfig.label}
            </Badge>
        );
    };

    return (
        <AppLayout>
            <Head title="Payment Management" />
            
            <div className="flex h-full flex-1 flex-col gap-8 p-6 bg-white">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Payment Management</h1>
                        <p className="mt-2 text-gray-600">Monitor and manage all platform payments and transactions</p>
                    </div>
                </div>

                {/* Stats */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-6">
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <CreditCard className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Payments</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.total_payments}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <CheckCircle className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Paid</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.paid_payments}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <Clock className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Pending</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.pending_payments}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-red-100 rounded-lg">
                                <XCircle className="h-6 w-6 text-red-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Failed</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.failed_payments}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <DollarSign className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.total_revenue)}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <TrendingUp className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">This Month</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.this_month_revenue)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="p-6 border rounded-lg bg-white">
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                        <div>
                            <Label htmlFor="search">Search</Label>
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <Input
                                    id="search"
                                    placeholder="Search by reference, user, or hostel..."
                                    value={searchTerm}
                                    onChange={e => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="status">Payment Status</Label>
                            <select
                                id="status"
                                value={selectedStatus}
                                onChange={e => setSelectedStatus(e.target.value)}
                                className="w-full rounded-md border border-gray-300 px-3 py-2"
                            >
                                <option value="">All Status</option>
                                <option value="paid">Paid</option>
                                <option value="pending">Pending</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>
                        <div>
                            <Label htmlFor="date_from">Date From</Label>
                            <Input
                                id="date_from"
                                type="date"
                                value={dateFrom}
                                onChange={e => setDateFrom(e.target.value)}
                            />
                        </div>
                        <div>
                            <Label htmlFor="date_to">Date To</Label>
                            <Input
                                id="date_to"
                                type="date"
                                value={dateTo}
                                onChange={e => setDateTo(e.target.value)}
                            />
                        </div>
                        <div className="flex gap-2">
                            <Button onClick={handleSearch}>
                                <Search className="mr-2 h-4 w-4" />
                                Search
                            </Button>
                            <Button variant="outline" onClick={clearFilters}>
                                Clear
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Payments Table */}
                <div className="border rounded-lg bg-white">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="border-b bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hostel</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Status</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking Status</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                                {payments.data.map((payment) => (
                                    <tr key={payment.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4">
                                            <div className="flex items-center">
                                                <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                                                    <CreditCard className="h-5 w-5 text-gray-500" />
                                                </div>
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-gray-900">{payment.booking_reference}</div>
                                                    <div className="text-sm text-gray-500">Booking ID: {payment.id}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex items-center">
                                                <User className="h-4 w-4 text-gray-400 mr-2" />
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{payment.user.name}</div>
                                                    <div className="text-sm text-gray-500">{payment.user.email}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex items-center">
                                                <Building2 className="h-4 w-4 text-gray-400 mr-2" />
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{payment.hostel.name}</div>
                                                    <div className="text-sm text-gray-500">{payment.hostel.city}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="text-sm font-bold text-gray-900">{formatCurrency(payment.total_amount)}</div>
                                        </td>
                                        <td className="px-6 py-4">
                                            {getPaymentStatusBadge(payment.payment_status)}
                                        </td>
                                        <td className="px-6 py-4">
                                            {getBookingStatusBadge(payment.status)}
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex items-center text-sm text-gray-500">
                                                <Calendar className="h-4 w-4 mr-1" />
                                                {new Date(payment.created_at).toLocaleDateString()}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 text-right">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => router.visit(`/bookings/${payment.id}`)}
                                            >
                                                View Details
                                            </Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination would go here */}
                    {payments.data.length === 0 && (
                        <div className="text-center py-12">
                            <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-4 text-lg font-medium text-gray-900">No payments found</h3>
                            <p className="mt-2 text-gray-600">No payments match your current filters.</p>
                        </div>
                    )}
                </div>

                {/* Payment Insights */}
                <div className="grid gap-6 md:grid-cols-2">
                    <div className="p-6 border rounded-lg bg-white">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Success Rate</h3>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Success Rate</span>
                                <span className="text-lg font-bold text-green-600">
                                    {stats.total_payments > 0 ? Math.round((stats.paid_payments / stats.total_payments) * 100) : 0}%
                                </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                    className="bg-green-600 h-2 rounded-full" 
                                    style={{
                                        width: `${stats.total_payments > 0 ? (stats.paid_payments / stats.total_payments) * 100 : 0}%`
                                    }}
                                ></div>
                            </div>
                            <div className="grid grid-cols-3 gap-4 text-center text-sm">
                                <div>
                                    <div className="font-medium text-green-600">{stats.paid_payments}</div>
                                    <div className="text-gray-500">Paid</div>
                                </div>
                                <div>
                                    <div className="font-medium text-yellow-600">{stats.pending_payments}</div>
                                    <div className="text-gray-500">Pending</div>
                                </div>
                                <div>
                                    <div className="font-medium text-red-600">{stats.failed_payments}</div>
                                    <div className="text-gray-500">Failed</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="p-6 border rounded-lg bg-white">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Summary</h3>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Total Revenue</span>
                                <span className="text-lg font-bold text-gray-900">
                                    {formatCurrency(stats.total_revenue)}
                                </span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">This Month</span>
                                <span className="text-lg font-bold text-green-600">
                                    {formatCurrency(stats.this_month_revenue)}
                                </span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Average per Payment</span>
                                <span className="text-lg font-bold text-gray-900">
                                    {formatCurrency(stats.paid_payments > 0 ? stats.total_revenue / stats.paid_payments : 0)}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
} 