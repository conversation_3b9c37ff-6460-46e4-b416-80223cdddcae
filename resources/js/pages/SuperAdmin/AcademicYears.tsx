import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, router } from '@inertiajs/react';
import { Plus, Edit, Trash2, Calendar, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import { useState } from 'react';
import { type BreadcrumbItem } from '@/types';

interface AcademicYear {
    id: number;
    name: string;
    display_name: string;
    start_date: string;
    end_date: string;
    is_active: boolean;
    is_current: boolean;
    created_at: string;
    updated_at: string;
}

interface Props {
    academicYears: AcademicYear[];
}

export default function AcademicYears({ academicYears }: Props) {
    const [editingYear, setEditingYear] = useState<AcademicYear | null>(null);
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [yearToDelete, setYearToDelete] = useState<AcademicYear | null>(null);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        name: '',
        display_name: '',
        start_date: '',
        end_date: '',
        is_active: true,
        is_current: false,
    });

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Super Admin', href: '/admin/analytics' },
        { title: 'Academic Years', href: '#' },
    ];

    const handleCreate = () => {
        post(route('admin.academic-years.store'), {
            onSuccess: () => {
                setIsCreateDialogOpen(false);
                reset();
            },
        });
    };

    const handleEdit = () => {
        if (!editingYear) return;
        
        put(route('admin.academic-years.update', editingYear.id), {
            onSuccess: () => {
                setIsEditDialogOpen(false);
                setEditingYear(null);
                reset();
            },
        });
    };

    const handleDelete = () => {
        if (!yearToDelete) return;
        
        router.delete(route('admin.academic-years.destroy', yearToDelete.id), {
            onSuccess: () => {
                setIsDeleteDialogOpen(false);
                setYearToDelete(null);
            },
        });
    };

    const handleSetCurrent = (academicYear: AcademicYear) => {
        router.post(route('admin.academic-years.set-current', academicYear.id));
    };

    const openCreateDialog = () => {
        reset();
        setIsCreateDialogOpen(true);
    };

    const openEditDialog = (academicYear: AcademicYear) => {
        setEditingYear(academicYear);
        setData({
            name: academicYear.name,
            display_name: academicYear.display_name,
            start_date: academicYear.start_date,
            end_date: academicYear.end_date,
            is_active: academicYear.is_active,
            is_current: academicYear.is_current,
        });
        setIsEditDialogOpen(true);
    };

    const openDeleteDialog = (academicYear: AcademicYear) => {
        setYearToDelete(academicYear);
        setIsDeleteDialogOpen(true);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Academic Years Management" />
            
            <div className="container mx-auto px-4 py-8">
                <div className="flex justify-between items-center mb-8">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Academic Years</h1>
                        <p className="text-gray-600 mt-2">Manage academic years for the platform</p>
                    </div>
                    <Button onClick={openCreateDialog} className="flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        Add Academic Year
                    </Button>
                </div>

                {/* Current Academic Year Alert */}
                {academicYears.find(ay => ay.is_current) && (
                    <Alert className="mb-6">
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Current Academic Year:</strong> {academicYears.find(ay => ay.is_current)?.display_name}
                        </AlertDescription>
                    </Alert>
                )}

                {/* Academic Years Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {academicYears.map((academicYear) => (
                        <Card key={academicYear.id} className="relative">
                            <CardHeader>
                                <div className="flex justify-between items-start">
                                    <div>
                                        <CardTitle className="text-lg">{academicYear.display_name}</CardTitle>
                                        <CardDescription>{academicYear.name}</CardDescription>
                                    </div>
                                    <div className="flex gap-1">
                                        {academicYear.is_current && (
                                            <Badge variant="default" className="bg-green-100 text-green-800">
                                                Current
                                            </Badge>
                                        )}
                                        {academicYear.is_active ? (
                                            <Badge variant="outline" className="text-green-600">
                                                Active
                                            </Badge>
                                        ) : (
                                            <Badge variant="outline" className="text-gray-500">
                                                Inactive
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <Calendar className="h-4 w-4" />
                                        <span>{formatDate(academicYear.start_date)} - {formatDate(academicYear.end_date)}</span>
                                    </div>
                                    
                                    <div className="flex gap-2 pt-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditDialog(academicYear)}
                                            className="flex-1"
                                        >
                                            <Edit className="h-4 w-4 mr-1" />
                                            Edit
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openDeleteDialog(academicYear)}
                                            className="flex-1"
                                            disabled={academicYear.is_current}
                                        >
                                            <Trash2 className="h-4 w-4 mr-1" />
                                            Delete
                                        </Button>
                                    </div>
                                    
                                    {!academicYear.is_current && academicYear.is_active && (
                                        <Button
                                            variant="secondary"
                                            size="sm"
                                            onClick={() => handleSetCurrent(academicYear)}
                                            className="w-full"
                                        >
                                            <Settings className="h-4 w-4 mr-1" />
                                            Set as Current
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Create Dialog */}
                <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                    <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>Add Academic Year</DialogTitle>
                            <DialogDescription>
                                Create a new academic year for the platform.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Name</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="2024/2025"
                                    />
                                    {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="display_name">Display Name</Label>
                                    <Input
                                        id="display_name"
                                        value={data.display_name}
                                        onChange={(e) => setData('display_name', e.target.value)}
                                        placeholder="2024/2025 Academic Year"
                                    />
                                    {errors.display_name && <p className="text-sm text-red-600">{errors.display_name}</p>}
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="start_date">Start Date</Label>
                                    <Input
                                        id="start_date"
                                        type="date"
                                        value={data.start_date}
                                        onChange={(e) => setData('start_date', e.target.value)}
                                    />
                                    {errors.start_date && <p className="text-sm text-red-600">{errors.start_date}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="end_date">End Date</Label>
                                    <Input
                                        id="end_date"
                                        type="date"
                                        value={data.end_date}
                                        onChange={(e) => setData('end_date', e.target.value)}
                                    />
                                    {errors.end_date && <p className="text-sm text-red-600">{errors.end_date}</p>}
                                </div>
                            </div>
                            <div className="flex gap-4">
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="is_active"
                                        checked={data.is_active}
                                        onChange={(e) => setData('is_active', e.target.checked)}
                                        className="rounded"
                                    />
                                    <Label htmlFor="is_active">Active</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="is_current"
                                        checked={data.is_current}
                                        onChange={(e) => setData('is_current', e.target.checked)}
                                        className="rounded"
                                    />
                                    <Label htmlFor="is_current">Current</Label>
                                </div>
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleCreate} disabled={processing}>
                                {processing ? 'Creating...' : 'Create'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Edit Dialog */}
                <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                    <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>Edit Academic Year</DialogTitle>
                            <DialogDescription>
                                Update the academic year details.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="edit_name">Name</Label>
                                    <Input
                                        id="edit_name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="2024/2025"
                                    />
                                    {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="edit_display_name">Display Name</Label>
                                    <Input
                                        id="edit_display_name"
                                        value={data.display_name}
                                        onChange={(e) => setData('display_name', e.target.value)}
                                        placeholder="2024/2025 Academic Year"
                                    />
                                    {errors.display_name && <p className="text-sm text-red-600">{errors.display_name}</p>}
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="edit_start_date">Start Date</Label>
                                    <Input
                                        id="edit_start_date"
                                        type="date"
                                        value={data.start_date}
                                        onChange={(e) => setData('start_date', e.target.value)}
                                    />
                                    {errors.start_date && <p className="text-sm text-red-600">{errors.start_date}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="edit_end_date">End Date</Label>
                                    <Input
                                        id="edit_end_date"
                                        type="date"
                                        value={data.end_date}
                                        onChange={(e) => setData('end_date', e.target.value)}
                                    />
                                    {errors.end_date && <p className="text-sm text-red-600">{errors.end_date}</p>}
                                </div>
                            </div>
                            <div className="flex gap-4">
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="edit_is_active"
                                        checked={data.is_active}
                                        onChange={(e) => setData('is_active', e.target.checked)}
                                        className="rounded"
                                    />
                                    <Label htmlFor="edit_is_active">Active</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="edit_is_current"
                                        checked={data.is_current}
                                        onChange={(e) => setData('is_current', e.target.checked)}
                                        className="rounded"
                                    />
                                    <Label htmlFor="edit_is_current">Current</Label>
                                </div>
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleEdit} disabled={processing}>
                                {processing ? 'Updating...' : 'Update'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Delete Dialog */}
                <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Delete Academic Year</DialogTitle>
                            <DialogDescription>
                                Are you sure you want to delete "{yearToDelete?.display_name}"? This action cannot be undone.
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button variant="destructive" onClick={handleDelete} disabled={processing}>
                                {processing ? 'Deleting...' : 'Delete'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
} 