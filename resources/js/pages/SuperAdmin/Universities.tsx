import { useForm, router, Head, usePage } from '@inertiajs/react';
import { useState, FormEventHandler, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { PlusCircle, Edit, Trash2, ArrowLeft, Building2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface University {
    id: number;
    name: string;
}

interface Props {
    universities: University[];
}

export default function Universities({ universities }: Props) {
    const { flash = {} } = usePage().props as any;

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalType, setModalType] = useState<'create' | 'edit'>('create');
    const [editingUniversity, setEditingUniversity] = useState<University | null>(null);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        name: '',
    });

    useEffect(() => {
        if (!isModalOpen) {
            reset();
            setEditingUniversity(null);
        }
    }, [isModalOpen, reset]);

    const openCreateModal = () => {
        setModalType('create');
        setIsModalOpen(true);
    };

    const openEditModal = (university: University) => {
        setModalType('edit');
        setEditingUniversity(university);
        setData('name', university.name);
        setIsModalOpen(true);
    };

    const handleCreateSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('admin.universities.store'), {
            onSuccess: () => {
                setIsModalOpen(false);
                reset();
            },
        });
    };

    const handleUpdateSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        if (editingUniversity) {
            put(route('admin.universities.update', editingUniversity.id), {
                onSuccess: () => {
                    setIsModalOpen(false);
                    reset();
                },
            });
        }
    };

    const handleDelete = (id: number) => {
        if (confirm('Are you sure you want to delete this university?')) {
            router.delete(route('admin.universities.destroy', id), {
                preserveScroll: true,
            });
        }
    };

    return (
        <AppLayout>
            <Head title="Manage Universities" />
            
            <div className="flex h-full flex-1 flex-col gap-4 md:gap-8 p-4 md:p-6 bg-white">
                {/* Header */}
                <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.visit('/dashboard')}
                            className="rounded-full"
                        >
                            <ArrowLeft className="h-4 w-4" />
                        </Button>
                        <div>
                            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Manage Universities</h1>
                            <p className="mt-1 text-sm md:text-base text-gray-600">Add, edit, or delete universities on the platform.</p>
                        </div>
                    </div>
                    <Button onClick={openCreateModal} className="flex items-center gap-2 rounded-full">
                        <PlusCircle className="h-4 w-4" /> Add University
                    </Button>
                </div>

                {flash.success && <div className="mb-4 p-3 bg-green-100 text-green-800 rounded">{flash.success}</div>}
                {flash.error && <div className="mb-4 p-3 bg-red-100 text-red-800 rounded">{flash.error}</div>}

                {/* Universities List */}
                <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                    {universities.length > 0 ? (
                        universities.map((university) => (
                            <div key={university.id} className="relative rounded-lg border bg-card text-card-foreground shadow-sm p-4 flex flex-col gap-3">
                                <div className="flex items-center gap-3">
                                    <Building2 className="h-5 w-5 text-primary flex-shrink-0" />
                                    <h3 className="text-lg font-semibold truncate">{university.name}</h3>
                                </div>
                                <div className="flex justify-end gap-2 mt-auto">
                                    <Button variant="outline" size="sm" onClick={() => openEditModal(university)}>
                                        <Edit className="h-4 w-4 mr-2" /> Edit
                                    </Button>
                                    <Button variant="destructive" size="sm" onClick={() => handleDelete(university.id)}>
                                        <Trash2 className="h-4 w-4 mr-2" /> Delete
                                    </Button>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="col-span-full h-24 flex items-center justify-center text-muted-foreground border rounded-lg">
                            No universities found.
                        </div>
                    )}
                </div>

                {/* Create/Edit University Modal */}
                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                    <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>{modalType === 'create' ? 'Add New University' : 'Edit University'}</DialogTitle>
                            <DialogDescription>
                                {modalType === 'create' ? 'Enter the name of the university to add it to the platform.' : 'Update the name of the university.'}
                            </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={modalType === 'create' ? handleCreateSubmit : handleUpdateSubmit} className="space-y-4">
                            <div className="grid gap-4 py-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">University Name</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        className={cn({'border-red-500': errors.name})}
                                        required
                                    />
                                    {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                                </div>
                            </div>
                            <DialogFooter>
                                <Button type="submit" disabled={processing}>
                                    {processing ? (modalType === 'create' ? 'Adding...' : 'Updating...') : (modalType === 'create' ? 'Add University' : 'Save Changes')}
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
} 