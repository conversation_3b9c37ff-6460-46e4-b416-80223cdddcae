import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    Search, CheckCircle, XCircle, Eye, MapPin, Plus,
    Building2, Shield, ShieldOff, Activity, Users, Edit, Trash2
} from 'lucide-react';
import { useState } from 'react';

interface Hostel {
    id: string;
    name: string;
    city: string;
    state: string;
    address: string;
    is_active: boolean;
    is_verified: boolean;
    bookings_count: number;
    created_at: string;
    owner: {
        name: string;
        email: string;
    };
}

interface Props {
    hostels: {
        data: Hostel[];
        links: any[];
        meta: any;
    };
    stats: {
        total_hostels: number;
        active_hostels: number;
        verified_hostels: number;
        pending_verification: number;
    };
    filters: {
        search?: string;
        status?: string;
        verification?: string;
    };
}

export default function Hostels({ hostels, stats, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [selectedVerification, setSelectedVerification] = useState(filters.verification || '');
    const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

    const handleSearch = () => {
        router.get('/admin/hostels', {
            search: searchTerm,
            status: selectedStatus,
            verification: selectedVerification,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatus('');
        setSelectedVerification('');
        router.get('/admin/hostels', {}, {
            preserveState: true,
            replace: true,
        });
    };

    const toggleVerification = (hostel: Hostel) => {
        router.post(`/admin/hostels/${hostel.id}/toggle-verification`, {}, {
            preserveState: true,
        });
    };

    const handleDeleteHostel = (hostel: Hostel) => {
        if (hostel.bookings_count > 0) {
            alert('Cannot delete hostel with existing bookings. Please resolve all bookings first.');
            return;
        }
        
        if (confirm(`Are you sure you want to delete "${hostel.name}"? This action cannot be undone.`)) {
            router.delete(`/admin/hostels/${hostel.id}`, {
                preserveState: true,
                onSuccess: () => {
                    setDeleteConfirm(null);
                },
                onError: (errors) => {
                    console.error('Delete error:', errors);
                }
            });
        }
    };

    const getStatusBadge = (isActive: boolean) => {
        return (
            <Badge className={isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {isActive ? 'Active' : 'Inactive'}
            </Badge>
        );
    };

    const getVerificationBadge = (isVerified: boolean) => {
        return (
            <Badge className={isVerified ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}>
                {isVerified ? 'Verified' : 'Pending'}
            </Badge>
        );
    };

    return (
        <AppLayout>
            <Head title="Hostel Management" />
            
            <div className="flex h-full flex-1 flex-col gap-4 md:gap-8 p-4 md:p-6 bg-white">
                {/* Header */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Hostel Management</h1>
                        <p className="mt-1 md:mt-2 text-sm md:text-base text-gray-600">Manage and oversee all hostels on the platform</p>
                    </div>
                    <Button 
                        onClick={() => router.visit('/admin/hostels/create')}
                        className="w-full sm:w-auto rounded-full"
                    >
                        <Plus className="mr-2 h-4 w-4" />
                        Create Hostel
                    </Button>
                </div>

                {/* Stats */}
                <div className="grid gap-4 grid-cols-2 lg:grid-cols-4">
                    <div className="p-4 md:p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                                <Building2 className="h-4 w-4 md:h-6 md:w-6 text-blue-600" />
                            </div>
                            <div className="ml-3 md:ml-4 min-w-0">
                                <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Total Hostels</p>
                                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.total_hostels}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-4 md:p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                                <Activity className="h-4 w-4 md:h-6 md:w-6 text-green-600" />
                            </div>
                            <div className="ml-3 md:ml-4 min-w-0">
                                <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Active Hostels</p>
                                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.active_hostels}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-4 md:p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                                <Shield className="h-4 w-4 md:h-6 md:w-6 text-blue-600" />
                            </div>
                            <div className="ml-3 md:ml-4 min-w-0">
                                <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Verified</p>
                                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.verified_hostels}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-4 md:p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-yellow-100 rounded-lg flex-shrink-0">
                                <ShieldOff className="h-4 w-4 md:h-6 md:w-6 text-yellow-600" />
                            </div>
                            <div className="ml-3 md:ml-4 min-w-0">
                                <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Pending</p>
                                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.pending_verification}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="p-4 md:p-6 border rounded-lg bg-white">
                    <div className="flex flex-col gap-4">
                        <div className="flex-1">
                            <Label htmlFor="search" className="text-sm font-medium">Search Hostels</Label>
                            <div className="relative mt-1">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <Input
                                    id="search"
                                    placeholder="Search by name or city..."
                                    value={searchTerm}
                                    onChange={e => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        
                        <div className="grid gap-4 grid-cols-1 sm:grid-cols-3">
                            <div>
                                <Label htmlFor="status" className="text-sm font-medium">Status</Label>
                                <select
                                    id="status"
                                    value={selectedStatus}
                                    onChange={e => setSelectedStatus(e.target.value)}
                                    className="w-full mt-1 rounded-md border border-gray-300 px-3 py-2 text-sm"
                                >
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                            <div>
                                <Label htmlFor="verification" className="text-sm font-medium">Verification</Label>
                                <select
                                    id="verification"
                                    value={selectedVerification}
                                    onChange={e => setSelectedVerification(e.target.value)}
                                    className="w-full mt-1 rounded-md border border-gray-300 px-3 py-2 text-sm"
                                >
                                    <option value="">All</option>
                                    <option value="verified">Verified</option>
                                    <option value="unverified">Unverified</option>
                                </select>
                            </div>
                            <div className="flex flex-col justify-end">
                                <div className="flex gap-2">
                                    <Button onClick={handleSearch} className="flex-1 rounded-full">
                                        <Search className="mr-2 h-4 w-4" />
                                        Search
                                    </Button>
                                    <Button variant="outline" onClick={clearFilters} className="flex-1 rounded-full">
                                        Clear
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Desktop Table View */}
                <div className="hidden lg:block border rounded-lg bg-white">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="border-b bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hostel</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Verification</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bookings</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                                {hostels.data.map((hostel) => (
                                    <tr key={hostel.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4">
                                            <div className="flex items-center">
                                                <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                                                    <Building2 className="h-5 w-5 text-gray-500" />
                                                </div>
                                                <div className="ml-4 min-w-0">
                                                    <div className="text-sm font-medium text-gray-900 truncate">{hostel.name}</div>
                                                    <div className="text-sm text-gray-500 truncate">{hostel.address}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="text-sm text-gray-900 truncate">{hostel.owner.name}</div>
                                            <div className="text-sm text-gray-500 truncate">{hostel.owner.email}</div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex items-center text-sm text-gray-900">
                                                <MapPin className="h-4 w-4 text-gray-400 mr-1 flex-shrink-0" />
                                                <span className="truncate">{hostel.city}, {hostel.state}</span>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            {getStatusBadge(hostel.is_active)}
                                        </td>
                                        <td className="px-6 py-4">
                                            {getVerificationBadge(hostel.is_verified)}
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex items-center text-sm text-gray-900">
                                                <Users className="h-4 w-4 text-gray-400 mr-1" />
                                                {hostel.bookings_count}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 text-sm text-gray-500">
                                            {new Date(hostel.created_at).toLocaleDateString()}
                                        </td>
                                        <td className="px-6 py-4 text-right">
                                            <div className="flex items-center justify-end gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => router.visit(`/hostels/${hostel.id}`)}
                                                    className="rounded-full"
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => router.visit(`/admin/hostels/${hostel.id}/edit`)}
                                                    className="rounded-full"
                                                >
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => toggleVerification(hostel)}
                                                    className={`rounded-full ${hostel.is_verified ? 'text-yellow-600 hover:text-yellow-700' : 'text-green-600 hover:text-green-700'}`}
                                                >
                                                    {hostel.is_verified ? (
                                                        <ShieldOff className="h-4 w-4" />
                                                    ) : (
                                                        <Shield className="h-4 w-4" />
                                                    )}
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleDeleteHostel(hostel)}
                                                    className="rounded-full text-red-600 hover:text-red-700 hover:border-red-300"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Mobile Card View */}
                <div className="lg:hidden space-y-4">
                    {hostels.data.map((hostel) => (
                        <div key={hostel.id} className="border rounded-lg bg-white p-4">
                            <div className="flex items-start justify-between mb-3">
                                <div className="flex items-center min-w-0 flex-1">
                                    <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                                        <Building2 className="h-5 w-5 text-gray-500" />
                                    </div>
                                    <div className="ml-3 min-w-0 flex-1">
                                        <h3 className="text-sm font-medium text-gray-900 truncate">{hostel.name}</h3>
                                        <p className="text-xs text-gray-500 truncate">{hostel.address}</p>
                                    </div>
                                </div>
                                <div className="flex gap-1 ml-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.visit(`/hostels/${hostel.id}`)}
                                        className="p-2 rounded-full"
                                    >
                                        <Eye className="h-3 w-3" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.visit(`/admin/hostels/${hostel.id}/edit`)}
                                        className="p-2 rounded-full"
                                    >
                                        <Edit className="h-3 w-3" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => toggleVerification(hostel)}
                                        className={`p-2 rounded-full ${hostel.is_verified ? 'text-yellow-600' : 'text-green-600'}`}
                                    >
                                        {hostel.is_verified ? (
                                            <ShieldOff className="h-3 w-3" />
                                        ) : (
                                            <Shield className="h-3 w-3" />
                                        )}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleDeleteHostel(hostel)}
                                        className="p-2 rounded-full text-red-600 hover:text-red-700 hover:border-red-300"
                                    >
                                        <Trash2 className="h-3 w-3" />
                                    </Button>
                                </div>
                            </div>
                            
                            <div className="grid grid-cols-2 gap-3 text-xs">
                                <div>
                                    <span className="text-gray-500">Owner:</span>
                                    <p className="font-medium text-gray-900 truncate">{hostel.owner.name}</p>
                                    <p className="text-gray-500 truncate">{hostel.owner.email}</p>
                                </div>
                                <div>
                                    <span className="text-gray-500">Location:</span>
                                    <p className="font-medium text-gray-900 flex items-center">
                                        <MapPin className="h-3 w-3 text-gray-400 mr-1" />
                                        <span className="truncate">{hostel.city}, {hostel.state}</span>
                                    </p>
                                </div>
                                <div>
                                    <span className="text-gray-500">Status:</span>
                                    <div className="mt-1">
                                        {getStatusBadge(hostel.is_active)}
                                    </div>
                                </div>
                                <div>
                                    <span className="text-gray-500">Verification:</span>
                                    <div className="mt-1">
                                        {getVerificationBadge(hostel.is_verified)}
                                    </div>
                                </div>
                                <div>
                                    <span className="text-gray-500">Bookings:</span>
                                    <p className="font-medium text-gray-900 flex items-center">
                                        <Users className="h-3 w-3 text-gray-400 mr-1" />
                                        {hostel.bookings_count}
                                    </p>
                                </div>
                                <div>
                                    <span className="text-gray-500">Created:</span>
                                    <p className="font-medium text-gray-900">
                                        {new Date(hostel.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Quick Actions */}
                <div className="p-4 md:p-6 border rounded-lg bg-white">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
                        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div className="flex items-center">
                                <ShieldOff className="h-6 w-6 md:h-8 md:w-8 text-yellow-600 flex-shrink-0" />
                                <div className="ml-3 min-w-0">
                                    <p className="text-sm font-medium text-yellow-900">Pending Verification</p>
                                    <p className="text-xl md:text-2xl font-bold text-yellow-900">{stats.pending_verification}</p>
                                </div>
                            </div>
                            <Button 
                                variant="outline" 
                                className="w-full mt-3 rounded-full"
                                onClick={() => router.get('/admin/hostels', { verification: 'unverified' })}
                            >
                                Review Pending
                            </Button>
                        </div>
                        
                        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <div className="flex items-center">
                                <XCircle className="h-6 w-6 md:h-8 md:w-8 text-red-600 flex-shrink-0" />
                                <div className="ml-3 min-w-0">
                                    <p className="text-sm font-medium text-red-900">Inactive Hostels</p>
                                    <p className="text-xl md:text-2xl font-bold text-red-900">{stats.total_hostels - stats.active_hostels}</p>
                                </div>
                            </div>
                            <Button 
                                variant="outline" 
                                className="w-full mt-3 rounded-full"
                                onClick={() => router.get('/admin/hostels', { status: 'inactive' })}
                            >
                                Review Inactive
                            </Button>
                        </div>
                        
                        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div className="flex items-center">
                                <CheckCircle className="h-6 w-6 md:h-8 md:w-8 text-green-600 flex-shrink-0" />
                                <div className="ml-3 min-w-0">
                                    <p className="text-sm font-medium text-green-900">Verified & Active</p>
                                    <p className="text-xl md:text-2xl font-bold text-green-900">{stats.verified_hostels}</p>
                                </div>
                            </div>
                            <Button 
                                variant="outline" 
                                className="w-full mt-3 rounded-full"
                                onClick={() => router.get('/admin/hostels', { verification: 'verified', status: 'active' })}
                            >
                                View Active
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
} 