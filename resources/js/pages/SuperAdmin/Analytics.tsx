import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    TrendingUp, Users, Building2, Calendar, DollarSign,
    BarChart3, Activity, CreditCard, CheckCircle, Clock,
    AlertCircle, XCircle, Download
} from 'lucide-react';
import { useState } from 'react';

interface Props {
    stats: {
        platform_overview: {
            total_users: number;
            total_hostels: number;
            total_bookings: number;
            total_revenue: number;
            active_hostels: number;
            verified_hostels: number;
        };
        recent_activity: {
            new_users: number;
            new_hostels: number;
            new_bookings: number;
            revenue_period: number;
        };
        booking_stats: {
            pending: number;
            confirmed: number;
            checked_in: number;
            completed: number;
            cancelled: number;
        };
        payment_stats: {
            paid: number;
            pending: number;
            failed: number;
        };
    };
    bookingChartData: Array<{date: string; count: number}>;
    revenueChartData: Array<{date: string; revenue: number}>;
    period: string;
}

export default function Analytics({ stats, bookingChartData, revenueChartData, period }: Props) {
    const [selectedPeriod, setSelectedPeriod] = useState(period);

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const handlePeriodChange = (newPeriod: string) => {
        setSelectedPeriod(newPeriod);
        router.get('/admin/analytics', { period: newPeriod }, {
            preserveState: true,
            replace: true,
        });
    };

    const getStatusColor = (status: string) => {
        const colors = {
            pending: 'bg-yellow-100 text-yellow-800',
            confirmed: 'bg-blue-100 text-blue-800',
            checked_in: 'bg-green-100 text-green-800',
            completed: 'bg-gray-100 text-gray-800',
            cancelled: 'bg-red-100 text-red-800',
            paid: 'bg-green-100 text-green-800',
            failed: 'bg-red-100 text-red-800',
        };
        return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
    };

    return (
        <AppLayout>
            <Head title="Platform Analytics" />
            
            <div className="flex h-full flex-1 flex-col gap-4 md:gap-8 p-3 md:p-6 bg-white">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Platform Analytics</h1>
                        <p className="mt-1 md:mt-2 text-sm md:text-base text-gray-600">Comprehensive insights into platform performance</p>
                    </div>
                    <div className="flex flex-col gap-2 md:flex-row md:items-center md:gap-4">
                        <select
                            value={selectedPeriod}
                            onChange={e => handlePeriodChange(e.target.value)}
                            className="rounded-md border border-gray-300 px-3 py-2 text-sm"
                        >
                            <option value="7">Last 7 days</option>
                            <option value="30">Last 30 days</option>
                            <option value="90">Last 90 days</option>
                            <option value="365">Last year</option>
                        </select>
                        <Button variant="outline" className="text-sm">
                            <Download className="mr-2 h-4 w-4" />
                            Export Report
                        </Button>
                    </div>
                </div>

                {/* Platform Overview */}
                <div>
                    <h2 className="text-lg md:text-xl font-semibold text-gray-900 mb-3 md:mb-4">Platform Overview</h2>
                    <div className="grid gap-3 md:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                        <div className="p-4 md:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                                    <Users className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
                                </div>
                                <div className="ml-3 md:ml-4 min-w-0">
                                    <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Total Users</p>
                                    <p className="text-xl md:text-2xl font-bold text-gray-900">{stats.platform_overview.total_users.toLocaleString()}</p>
                                    <p className="text-xs text-green-600">+{stats.recent_activity.new_users} last {selectedPeriod} days</p>
                                </div>
                            </div>
                        </div>

                        <div className="p-4 md:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-purple-100 rounded-lg flex-shrink-0">
                                    <Building2 className="h-5 w-5 md:h-6 md:w-6 text-purple-600" />
                                </div>
                                <div className="ml-3 md:ml-4 min-w-0">
                                    <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Total Hostels</p>
                                    <p className="text-xl md:text-2xl font-bold text-gray-900">{stats.platform_overview.total_hostels}</p>
                                    <p className="text-xs text-green-600">+{stats.recent_activity.new_hostels} last {selectedPeriod} days</p>
                                </div>
                            </div>
                        </div>

                        <div className="p-4 md:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                                    <Calendar className="h-5 w-5 md:h-6 md:w-6 text-green-600" />
                                </div>
                                <div className="ml-3 md:ml-4 min-w-0">
                                    <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Total Bookings</p>
                                    <p className="text-xl md:text-2xl font-bold text-gray-900">{stats.platform_overview.total_bookings.toLocaleString()}</p>
                                    <p className="text-xs text-green-600">+{stats.recent_activity.new_bookings} last {selectedPeriod} days</p>
                                </div>
                            </div>
                        </div>

                        <div className="p-4 md:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-yellow-100 rounded-lg flex-shrink-0">
                                    <DollarSign className="h-5 w-5 md:h-6 md:w-6 text-yellow-600" />
                                </div>
                                <div className="ml-3 md:ml-4 min-w-0">
                                    <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Total Revenue</p>
                                    <p className="text-xl md:text-2xl font-bold text-gray-900">{formatCurrency(stats.platform_overview.total_revenue)}</p>
                                    <p className="text-xs text-green-600">{formatCurrency(stats.recent_activity.revenue_period)} last {selectedPeriod} days</p>
                                </div>
                            </div>
                        </div>

                        <div className="p-4 md:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                                    <CheckCircle className="h-5 w-5 md:h-6 md:w-6 text-green-600" />
                                </div>
                                <div className="ml-3 md:ml-4 min-w-0">
                                    <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Active Hostels</p>
                                    <p className="text-xl md:text-2xl font-bold text-gray-900">{stats.platform_overview.active_hostels}</p>
                                    <p className="text-xs text-gray-600">of {stats.platform_overview.total_hostels} total</p>
                                </div>
                            </div>
                        </div>

                        <div className="p-4 md:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                                    <Activity className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
                                </div>
                                <div className="ml-3 md:ml-4 min-w-0">
                                    <p className="text-xs md:text-sm font-medium text-gray-600 truncate">Verified Hostels</p>
                                    <p className="text-xl md:text-2xl font-bold text-gray-900">{stats.platform_overview.verified_hostels}</p>
                                    <p className="text-xs text-gray-600">{Math.round((stats.platform_overview.verified_hostels / stats.platform_overview.total_hostels) * 100)}% verification rate</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Booking Analytics */}
                <div className="grid gap-4 md:gap-8 lg:grid-cols-2">
                    <div>
                        <h2 className="text-lg md:text-xl font-semibold text-gray-900 mb-3 md:mb-4">Booking Status Breakdown</h2>
                        <div className="p-4 md:p-6 border rounded-lg bg-white">
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock className="h-4 w-4 text-yellow-600" />
                                        <span className="text-sm font-medium">Pending</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Badge className={getStatusColor('pending')}>
                                            {stats.booking_stats.pending}
                                        </Badge>
                                    </div>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <CheckCircle className="h-4 w-4 text-blue-600" />
                                        <span className="text-sm font-medium">Confirmed</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Badge className={getStatusColor('confirmed')}>
                                            {stats.booking_stats.confirmed}
                                        </Badge>
                                    </div>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Activity className="h-4 w-4 text-green-600" />
                                        <span className="text-sm font-medium">Checked In</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Badge className={getStatusColor('checked_in')}>
                                            {stats.booking_stats.checked_in}
                                        </Badge>
                                    </div>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <CheckCircle className="h-4 w-4 text-gray-600" />
                                        <span className="text-sm font-medium">Completed</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Badge className={getStatusColor('completed')}>
                                            {stats.booking_stats.completed}
                                        </Badge>
                                    </div>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <XCircle className="h-4 w-4 text-red-600" />
                                        <span className="text-sm font-medium">Cancelled</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Badge className={getStatusColor('cancelled')}>
                                            {stats.booking_stats.cancelled}
                                        </Badge>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h2 className="text-lg md:text-xl font-semibold text-gray-900 mb-3 md:mb-4">Payment Status</h2>
                        <div className="p-4 md:p-6 border rounded-lg bg-white">
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm font-medium">Paid</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Badge className={getStatusColor('paid')}>
                                            {stats.payment_stats.paid}
                                        </Badge>
                                        <span className="text-xs text-gray-500">
                                            {Math.round((stats.payment_stats.paid / (stats.payment_stats.paid + stats.payment_stats.pending + stats.payment_stats.failed)) * 100)}%
                                        </span>
                                    </div>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock className="h-4 w-4 text-yellow-600" />
                                        <span className="text-sm font-medium">Pending</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Badge className={getStatusColor('pending')}>
                                            {stats.payment_stats.pending}
                                        </Badge>
                                        <span className="text-xs text-gray-500">
                                            {Math.round((stats.payment_stats.pending / (stats.payment_stats.paid + stats.payment_stats.pending + stats.payment_stats.failed)) * 100)}%
                                        </span>
                                    </div>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <AlertCircle className="h-4 w-4 text-red-600" />
                                        <span className="text-sm font-medium">Failed</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Badge className={getStatusColor('failed')}>
                                            {stats.payment_stats.failed}
                                        </Badge>
                                        <span className="text-xs text-gray-500">
                                            {Math.round((stats.payment_stats.failed / (stats.payment_stats.paid + stats.payment_stats.pending + stats.payment_stats.failed)) * 100)}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Charts Placeholder */}
                <div className="grid gap-4 md:gap-8 lg:grid-cols-2">
                    <div className="p-4 md:p-6 border rounded-lg bg-white">
                        <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Bookings Over Time</h3>
                        <div className="h-48 md:h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                            <div className="text-center">
                                <BarChart3 className="h-8 w-8 md:h-12 md:w-12 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm md:text-base text-gray-500">Chart visualization</p>
                                <p className="text-xs text-gray-400">({bookingChartData.length} data points)</p>
                            </div>
                        </div>
                    </div>

                    <div className="p-4 md:p-6 border rounded-lg bg-white">
                        <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Revenue Over Time</h3>
                        <div className="h-48 md:h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                            <div className="text-center">
                                <TrendingUp className="h-8 w-8 md:h-12 md:w-12 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm md:text-base text-gray-500">Chart visualization</p>
                                <p className="text-xs text-gray-400">({revenueChartData.length} data points)</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Key Metrics Summary */}
                <div className="p-4 md:p-6 border rounded-lg bg-white">
                    <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Key Performance Indicators</h3>
                    <div className="grid gap-3 md:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                        <div className="text-center p-3 md:p-4 bg-gray-50 rounded-lg">
                            <p className="text-xs md:text-sm text-gray-600">Conversion Rate</p>
                            <p className="text-lg md:text-2xl font-bold text-gray-900">
                                {Math.round((stats.booking_stats.confirmed / stats.platform_overview.total_bookings) * 100)}%
                            </p>
                            <p className="text-xs text-gray-500">Bookings confirmed</p>
                        </div>
                        <div className="text-center p-3 md:p-4 bg-gray-50 rounded-lg">
                            <p className="text-xs md:text-sm text-gray-600">Avg. Revenue per Booking</p>
                            <p className="text-lg md:text-2xl font-bold text-gray-900">
                                {formatCurrency(stats.platform_overview.total_revenue / stats.platform_overview.total_bookings)}
                            </p>
                            <p className="text-xs text-gray-500">Per booking</p>
                        </div>
                        <div className="text-center p-3 md:p-4 bg-gray-50 rounded-lg">
                            <p className="text-xs md:text-sm text-gray-600">Hostel Utilization</p>
                            <p className="text-lg md:text-2xl font-bold text-gray-900">
                                {Math.round((stats.platform_overview.active_hostels / stats.platform_overview.total_hostels) * 100)}%
                            </p>
                            <p className="text-xs text-gray-500">Hostels active</p>
                        </div>
                        <div className="text-center p-3 md:p-4 bg-gray-50 rounded-lg">
                            <p className="text-xs md:text-sm text-gray-600">Payment Success Rate</p>
                            <p className="text-lg md:text-2xl font-bold text-gray-900">
                                {Math.round((stats.payment_stats.paid / (stats.payment_stats.paid + stats.payment_stats.failed)) * 100)}%
                            </p>
                            <p className="text-xs text-gray-500">Payments successful</p>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
} 