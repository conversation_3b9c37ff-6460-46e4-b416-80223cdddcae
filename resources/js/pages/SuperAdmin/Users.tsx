import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm, usePage } from '@inertiajs/react';
import { 
    Plus, Search, Edit, Trash2, Shield, ShieldOff,
    Users as UsersIcon, GraduationCap, Building, CheckCircle, Clock, User
} from 'lucide-react';
import { useState } from 'react';

interface User {
    id: string;
    name: string;
    email: string;
    role: string;
    university?: string;
    student_id?: string;
    phone?: string;
    is_verified: boolean;
    created_at: string;
}

interface Props {
    users: {
        data: User[];
        links: any[];
        meta: any;
    };
    stats: {
        total_users: number;
        students: number;
        hostel_admins: number;
        verified_users: number;
        new_this_month: number;
    };
    filters: {
        search?: string;
        role?: string;
        university?: string;
    };
    universities: string[];
}

export default function Users({ users, stats, filters, universities }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedRole, setSelectedRole] = useState(filters.role || '');
    const [selectedUniversity, setSelectedUniversity] = useState(filters.university || '');
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [editDialogOpen, setEditDialogOpen] = useState(false);
    const [editingUser, setEditingUser] = useState<User | null>(null);

    const { data: createData, setData: setCreateData, post: createPost, processing: createProcessing, reset: createReset } = useForm({
        name: '',
        email: '',
        password: '',
        role: 'student',
        phone: '',
        university: '',
        student_id: '',
    });

    const { data: editData, setData: setEditData, put: editPut, processing: editProcessing } = useForm({
        name: '',
        email: '',
        role: '',
        phone: '',
        university: '',
        student_id: '',
        is_verified: false,
    });

    const handleSearch = () => {
        router.get('/admin/users', {
            search: searchTerm,
            role: selectedRole,
            university: selectedUniversity,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedRole('');
        setSelectedUniversity('');
        router.get('/admin/users', {}, {
            preserveState: true,
            replace: true,
        });
    };

    const handleCreateUser = (e: React.FormEvent) => {
        e.preventDefault();
        createPost('/admin/users', {
            onSuccess: () => {
                setCreateDialogOpen(false);
                createReset();
            }
        });
    };

    const handleEditUser = (user: User) => {
        setEditingUser(user);
        setEditData({
            name: user.name,
            email: user.email,
            role: user.role,
            phone: user.phone || '',
            university: user.university || '',
            student_id: user.student_id || '',
            is_verified: user.is_verified,
        });
        setEditDialogOpen(true);
    };

    const handleUpdateUser = (e: React.FormEvent) => {
        e.preventDefault();
        if (!editingUser) return;
        
        editPut(`/admin/users/${editingUser.id}`, {
            onSuccess: () => {
                setEditDialogOpen(false);
                setEditingUser(null);
            }
        });
    };

    const toggleVerification = (user: User) => {
        router.post(`/admin/users/${user.id}/toggle-verification`, {}, {
            preserveState: true,
        });
    };

    const deleteUser = (user: User) => {
        if (confirm(`Are you sure you want to delete ${user.name}?`)) {
            router.delete(`/admin/users/${user.id}`, {
                preserveState: true,
            });
        }
    };

    const getRoleBadge = (role: string) => {
        const config = {
            student: { color: 'bg-blue-100 text-blue-800', icon: GraduationCap, label: 'Student' },
            hostel_admin: { color: 'bg-purple-100 text-purple-800', icon: Building, label: 'Hostel Admin' },
            super_admin: { color: 'bg-red-100 text-red-800', icon: Shield, label: 'Super Admin' },
        };
        
        const roleConfig = config[role as keyof typeof config];
        if (!roleConfig) return null;
        
        const Icon = roleConfig.icon;
        return (
            <Badge className={`${roleConfig.color} gap-1`}>
                <Icon className="h-3 w-3" />
                {roleConfig.label}
            </Badge>
        );
    };

    return (
        <AppLayout>
            <Head title="User Management" />
            
            <div className="flex h-full flex-1 flex-col gap-8 p-6 bg-white">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
                        <p className="mt-2 text-gray-600">Manage all platform users, roles, and permissions</p>
                    </div>
                    <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Create User
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <form onSubmit={handleCreateUser}>
                                <DialogHeader>
                                    <DialogTitle>Create New User</DialogTitle>
                                    <DialogDescription>Add a new user to the platform</DialogDescription>
                                </DialogHeader>
                                <div className="grid gap-4 py-4">
                                    <div>
                                        <Label htmlFor="name">Name</Label>
                                        <Input
                                            id="name"
                                            value={createData.name}
                                            onChange={e => setCreateData('name', e.target.value)}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="email">Email</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={createData.email}
                                            onChange={e => setCreateData('email', e.target.value)}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="password">Password</Label>
                                        <Input
                                            id="password"
                                            type="password"
                                            value={createData.password}
                                            onChange={e => setCreateData('password', e.target.value)}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="role">Role</Label>
                                        <select
                                            id="role"
                                            value={createData.role}
                                            onChange={e => setCreateData('role', e.target.value)}
                                            className="w-full rounded-md border border-gray-300 px-3 py-2"
                                        >
                                            <option value="student">Student</option>
                                            <option value="hostel_admin">Hostel Admin</option>
                                            <option value="super_admin">Super Admin</option>
                                        </select>
                                    </div>
                                    {createData.role === 'student' && (
                                        <>
                                            <div>
                                                <Label htmlFor="student_id">Student ID</Label>
                                                <Input
                                                    id="student_id"
                                                    value={createData.student_id}
                                                    onChange={e => setCreateData('student_id', e.target.value)}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="university">University</Label>
                                                <Input
                                                    id="university"
                                                    value={createData.university}
                                                    onChange={e => setCreateData('university', e.target.value)}
                                                />
                                            </div>
                                        </>
                                    )}
                                    <div>
                                        <Label htmlFor="phone">Phone</Label>
                                        <Input
                                            id="phone"
                                            value={createData.phone}
                                            onChange={e => setCreateData('phone', e.target.value)}
                                        />
                                    </div>
                                </div>
                                <DialogFooter>
                                    <Button type="submit" disabled={createProcessing}>
                                        {createProcessing ? 'Creating...' : 'Create User'}
                                    </Button>
                                </DialogFooter>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>

                {/* Stats */}
                <div className="grid gap-6 md:grid-cols-5">
                    <div className="p-4 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <UsersIcon className="h-8 w-8 text-blue-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Total Users</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.total_users}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-4 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <GraduationCap className="h-8 w-8 text-green-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Students</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.students}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-4 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <Building className="h-8 w-8 text-purple-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Hostel Admins</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.hostel_admins}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-4 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <CheckCircle className="h-8 w-8 text-green-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Verified</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.verified_users}</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-4 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <Clock className="h-8 w-8 text-orange-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">New This Month</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.new_this_month}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="p-6 border rounded-lg bg-white">
                    <div className="flex flex-wrap gap-4 items-end">
                        <div className="flex-1 min-w-[200px]">
                            <Label htmlFor="search">Search Users</Label>
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <Input
                                    id="search"
                                    placeholder="Search by name, email, or student ID..."
                                    value={searchTerm}
                                    onChange={e => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="role">Role</Label>
                            <select
                                id="role"
                                value={selectedRole}
                                onChange={e => setSelectedRole(e.target.value)}
                                className="w-[150px] rounded-md border border-gray-300 px-3 py-2"
                            >
                                <option value="">All Roles</option>
                                <option value="student">Student</option>
                                <option value="hostel_admin">Hostel Admin</option>
                                <option value="super_admin">Super Admin</option>
                            </select>
                        </div>
                        <div>
                            <Label htmlFor="university">University</Label>
                            <select
                                id="university"
                                value={selectedUniversity}
                                onChange={e => setSelectedUniversity(e.target.value)}
                                className="w-[200px] rounded-md border border-gray-300 px-3 py-2"
                            >
                                <option value="">All Universities</option>
                                {universities.map(uni => (
                                    <option key={uni} value={uni}>{uni}</option>
                                ))}
                            </select>
                        </div>
                        <Button onClick={handleSearch}>
                            <Search className="mr-2 h-4 w-4" />
                            Search
                        </Button>
                        <Button variant="outline" onClick={clearFilters}>
                            Clear
                        </Button>
                    </div>
                </div>

                {/* Users Table */}
                <div className="border rounded-lg bg-white">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="border-b bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">University</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                                {users.data.map((user) => (
                                    <tr key={user.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4">
                                            <div className="flex items-center">
                                                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                    <User className="h-5 w-5 text-gray-500" />
                                                </div>
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-gray-900">{user.name}</div>
                                                    <div className="text-sm text-gray-500">{user.email}</div>
                                                    {user.student_id && (
                                                        <div className="text-xs text-gray-400">ID: {user.student_id}</div>
                                                    )}
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            {getRoleBadge(user.role)}
                                        </td>
                                        <td className="px-6 py-4 text-sm text-gray-900">
                                            {user.university || '-'}
                                        </td>
                                        <td className="px-6 py-4">
                                            <Badge className={user.is_verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                                                {user.is_verified ? 'Verified' : 'Unverified'}
                                            </Badge>
                                        </td>
                                        <td className="px-6 py-4 text-sm text-gray-500">
                                            {new Date(user.created_at).toLocaleDateString()}
                                        </td>
                                        <td className="px-6 py-4 text-right">
                                            <div className="flex items-center justify-end gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleEditUser(user)}
                                                >
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => toggleVerification(user)}
                                                >
                                                    {user.is_verified ? (
                                                        <ShieldOff className="h-4 w-4" />
                                                    ) : (
                                                        <Shield className="h-4 w-4" />
                                                    )}
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => deleteUser(user)}
                                                    className="text-red-600 hover:text-red-700"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Edit User Dialog */}
                <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                    <DialogContent>
                        <form onSubmit={handleUpdateUser}>
                            <DialogHeader>
                                <DialogTitle>Edit User</DialogTitle>
                                <DialogDescription>Update user information and permissions</DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                                <div>
                                    <Label htmlFor="edit-name">Name</Label>
                                    <Input
                                        id="edit-name"
                                        value={editData.name}
                                        onChange={e => setEditData('name', e.target.value)}
                                        required
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="edit-email">Email</Label>
                                    <Input
                                        id="edit-email"
                                        type="email"
                                        value={editData.email}
                                        onChange={e => setEditData('email', e.target.value)}
                                        required
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="edit-role">Role</Label>
                                    <select
                                        id="edit-role"
                                        value={editData.role}
                                        onChange={e => setEditData('role', e.target.value)}
                                        className="w-full rounded-md border border-gray-300 px-3 py-2"
                                    >
                                        <option value="student">Student</option>
                                        <option value="hostel_admin">Hostel Admin</option>
                                        <option value="super_admin">Super Admin</option>
                                    </select>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="edit-verified"
                                        checked={editData.is_verified}
                                        onChange={e => setEditData('is_verified', e.target.checked)}
                                        className="rounded"
                                    />
                                    <Label htmlFor="edit-verified">Verified User</Label>
                                </div>
                            </div>
                            <DialogFooter>
                                <Button type="submit" disabled={editProcessing}>
                                    {editProcessing ? 'Updating...' : 'Update User'}
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
} 