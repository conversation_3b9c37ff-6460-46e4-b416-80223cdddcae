import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm } from '@inertiajs/react';
import { ArrowLeft, Upload, X, Plus, ImageIcon, AlertCircle, Building2, MapPin, Phone, Mail, Globe, Clock, FileText } from 'lucide-react';
import { useState, useEffect, useRef, useCallback } from 'react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface Hostel {
    id: string;
    name: string;
    description: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude: string;
    longitude: string;
    phone: string;
    email: string;
    website: string;
    house_rules: string;
    cancellation_policy: string;
    amenities: string[];
    photos: string[]; // URLs for existing photos
    payment_period: 'month' | 'semester' | 'year';
    hostel_type: 'university' | 'homestel' | 'other';
    youtube_links: string[];
    owner_id: string;
    is_active: boolean;
    is_verified: boolean;
    allow_deposit: boolean;
    is_per_semester: boolean;
    room_types: RoomType[];
    base_price?: number;
    service_fee?: number;
    deposit_percentage?: number;
    cancellation_fee?: number;
}

interface HostelAdmin {
    id: string;
    name: string;
    email: string;
}

interface Props {
    hostel: Hostel;
    hostel_admins: HostelAdmin[];
}

interface HostelForm {
    _method?: 'put';
    name: string;
    description: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude: string;
    longitude: string;
    phone: string;
    email: string;
    website: string;
    house_rules: string;
    cancellation_policy: string;
    amenities: string[];
    photos: (File | string)[];
    payment_period: 'month' | 'semester' | 'year';
    hostel_type: 'university' | 'homestel' | 'other';
    youtube_links: string[];
    owner_id: string;
    is_active: boolean;
    is_verified: boolean;
    allow_deposit: boolean;
    is_per_semester: boolean;
    room_types: RoomTypeForm[];
    
    
    base_price: string;
    service_fee: string;
    deposit_percentage: string;
    cancellation_fee: string;
    
    [key: string]: any;
}

interface PhotoPreview {
    id: string;
    url: string;
    isNew: boolean;
    file?: File; // Only present for new files
}

interface RoomType {
    id: string;
    name: string;
    capacity: number;
    beds: number;
    available_rooms: number;
    gender_type: string;
    number_of_beds?: number;
    male_rooms?: number;
    female_rooms?: number;
    mixed_rooms?: number;
}

interface RoomTypeForm {
    id: string; // Used for unique key and removal, and for existing room types
    name: string;
    capacity: number; // Capacity (persons)
    number_of_beds: number;
    male_rooms: number;
    female_rooms: number;
    mixed_rooms: number;
    _method?: 'put'; // For existing room types in backend
    [key: string]: string | number | undefined;
}

const ghanaianRegions = [
    'Greater Accra', 'Ashanti', 'Central', 'Eastern', 'Northern', 'Upper East',
    'Upper West', 'Volta', 'Western', 'Western North', 'Brong Ahafo',
    'Ahafo', 'Bono East', 'Oti', 'North East', 'Savannah'
];

const amenitiesList = [
    'wifi', 'parking', 'laundry', 'air_conditioning', 'kitchen', 'dining_hall',
    'study_rooms', 'common_room', 'library', 'sports_facilities', 'security',
    'generator', 'water_supply', 'cleaning_service'
];

export default function EditHostel({ hostel, hostel_admins }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        _method: 'put',
        name: hostel.name,
        description: hostel.description,
        address: hostel.address,
        city: hostel.city,
        state: hostel.state,
        country: hostel.country,
        postal_code: hostel.postal_code || '',
        latitude: hostel.latitude || '',
        longitude: hostel.longitude || '',
        phone: hostel.phone,
        email: hostel.email,
        website: hostel.website || '',
        house_rules: hostel.house_rules || '',
        cancellation_policy: hostel.cancellation_policy || '',
        amenities: hostel.amenities || [],
        photos: hostel.photos || [], 
        payment_period: hostel.payment_period as 'month' | 'semester' | 'year',
        hostel_type: hostel.hostel_type as 'university' | 'homestel' | 'other',
        youtube_links: hostel.youtube_links || [''],
        owner_id: hostel.owner_id,
        is_active: hostel.is_active,
        is_verified: hostel.is_verified,
        allow_deposit: hostel.allow_deposit,
        is_per_semester: hostel.is_per_semester,
        room_types: hostel.room_types && hostel.room_types.length > 0
            ? hostel.room_types.map(rt => ({
                id: rt.id.toString(),
                name: rt.name,
                capacity: rt.capacity,
                number_of_beds: typeof rt.beds === 'number' ? rt.beds : 0,
                male_rooms: 0, // Initialize as 0, let admin distribute manually
                female_rooms: 0, // Initialize as 0, let admin distribute manually
                mixed_rooms: rt.available_rooms || 0, // Put all available rooms in mixed for now
            }))
            : [{ id: Math.random().toString(36).substring(2), name: '', capacity: 0, number_of_beds: 0, male_rooms: 0, female_rooms: 0, mixed_rooms: 0 }],
        
        base_price: hostel !== undefined && hostel.base_price !== null ? hostel.base_price : '',
        service_fee: hostel.service_fee !== undefined && hostel.service_fee !== null ? hostel.service_fee : '',
        deposit_percentage: hostel.deposit_percentage !== undefined && hostel.deposit_percentage !== null ? hostel.deposit_percentage : '',
        cancellation_fee: hostel.cancellation_fee !== undefined && hostel.cancellation_fee !== null ? hostel.cancellation_fee : '',
    });

    const [selectedAmenities, setSelectedAmenities] = useState<string[]>(hostel.amenities || []);
    const [photoPreviews, setPhotoPreviews] = useState<PhotoPreview[]>(
        (hostel.photos || []).map(url => ({ id: Math.random().toString(36).substring(2), url, isNew: false }))
    );
    const [isDragOver, setIsDragOver] = useState(false);
    const [uploadErrors, setUploadErrors] = useState<string[]>([]);
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [roomTypes, setRoomTypes] = useState<RoomTypeForm[]>(
        hostel.room_types && hostel.room_types.length > 0
            ? hostel.room_types.map(rt => ({
                id: rt.id.toString(),
                name: rt.name,
                capacity: rt.capacity,
                number_of_beds: rt.beds,
                male_rooms: 0, // Initialize as 0, let admin distribute manually
                female_rooms: 0, // Initialize as 0, let admin distribute manually
                mixed_rooms: rt.available_rooms || 0, // Put all available rooms in mixed for now
            }))
            : [{ id: Math.random().toString(36).substring(2), name: '', capacity: 0, number_of_beds: 0, male_rooms: 0, female_rooms: 0, mixed_rooms: 0 }]
    );

    const [youtubeLinks, setYoutubeLinks] = useState<string[]>(hostel.youtube_links && hostel.youtube_links.length > 0 ? hostel.youtube_links : ['']);
    const [showRoomDistributionDialog, setShowRoomDistributionDialog] = useState(true);

    // Helper function to get payment period text
    const getPaymentPeriodText = () => {
        switch (data.payment_period) {
            case 'month':
                return 'Month';
            case 'semester':
                return 'Semester';
            case 'year':
                return 'Academic Year';
            default:
                return 'Period';
        }
    };

    const validateFile = (file: File): string | null => {
        if (!file.type.startsWith('image/')) {
            return `${file.name} is not an image file`;
        }
        if (file.size > 50 * 1024 * 1024) {
            return `${file.name} is too large (max 50MB)`;
        }
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            return `${file.name} format not supported (use JPEG, PNG, or WebP)`;
        }
        return null;
    };

    const processFiles = useCallback((files: FileList | File[]) => {
        const fileArray = Array.from(files);
        const errors: string[] = [];
        const validFiles: File[] = [];
        const newPreviews: PhotoPreview[] = [];

        if (photoPreviews.length + fileArray.length > 10) {
            errors.push('Maximum 10 photos allowed');
            return;
        }

        fileArray.forEach((file) => {
            const error = validateFile(file);
            if (error) {
                errors.push(error);
            } else {
                validFiles.push(file);
                newPreviews.push({
                    file,
                    url: URL.createObjectURL(file),
                    id: Math.random().toString(36).substring(2),
                    isNew: true,
                });
            }
        });

        setUploadErrors(errors);
        
        if (validFiles.length > 0) {
            const updatedPhotos = [...data.photos, ...validFiles];
            const updatedPreviews = [...photoPreviews, ...newPreviews];
            
            setData('photos', updatedPhotos);
            setPhotoPreviews(updatedPreviews);
        }
    }, [data.photos, photoPreviews]);

    const handlePhotoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files) {
            processFiles(files);
        }
        e.target.value = '';
    };

    const handleRemovePhoto = (photoId: string, isNew: boolean) => {
        setPhotoPreviews(prev => {
            const photoToRemove = prev.find(p => p.id === photoId);
            if (photoToRemove && photoToRemove.isNew && photoToRemove.file) {
                URL.revokeObjectURL(photoToRemove.url);
            }
            return prev.filter(p => p.id !== photoId);
        });

        if (isNew) {
            const updatedPhotos = data.photos.filter((photo) => {
                // Filter out the new file by its URL or name if available
                if (photo instanceof File && photo.name === photoPreviews.find(p => p.id === photoId)?.file?.name) {
                    return false;
                }
                return true;
            });
            setData('photos', updatedPhotos);
        } else {
            // For existing photos, filter by URL
            const updatedPhotos = data.photos.filter((photo) => {
                if (typeof photo === 'string' && photo === photoPreviews.find(p => p.id === photoId)?.url) {
                    return false;
                }
                return true;
            });
            setData('photos', updatedPhotos);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
        
        const files = e.dataTransfer.files;
        if (files) {
            processFiles(files);
        }
    };

    const handleAmenityToggle = (amenity: string) => {
        setSelectedAmenities(prev => 
            prev.includes(amenity) 
                ? prev.filter(a => a !== amenity)
                : [...prev, amenity]
        );
    };

    const handleAddRoomType = () => {
        setRoomTypes([...roomTypes, { id: Math.random().toString(36).substring(2), name: '', capacity: 0, number_of_beds: 0, male_rooms: 0, female_rooms: 0, mixed_rooms: 0 }]);
    };

    const handleRemoveRoomType = (id: string) => {
        setRoomTypes(roomTypes.filter(rt => rt.id !== id));
    };

    const handleRoomTypeChange = (id: string, field: keyof RoomTypeForm, value: string | number) => {
        setRoomTypes(roomTypes.map(rt => {
            if (rt.id === id) {
                if (['capacity', 'number_of_beds', 'male_rooms', 'female_rooms', 'mixed_rooms'].includes(field as string)) {
                    // Convert to number, defaulting to 0 if NaN
                    return { ...rt, [field]: parseFloat(value as string) || 0 };
                }
                return { ...rt, [field]: value };
            }
            return rt;
        }));
    };

    const handleYoutubeLinkChange = (index: number, value: string) => {
        const newLinks = [...youtubeLinks];
        newLinks[index] = value;
        setYoutubeLinks(newLinks);
        setData('youtube_links', newLinks); // Update form data
    };

    const addYoutubeLink = () => {
        setYoutubeLinks([...youtubeLinks, '']);
        setData('youtube_links', [...youtubeLinks, '']); // Update form data
    };

    const removeYoutubeLink = (index: number) => {
        const newLinks = [...youtubeLinks];
        newLinks.splice(index, 1);
        setYoutubeLinks(newLinks);
        setData('youtube_links', newLinks); // Update form data
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = new FormData();
        
        Object.entries(data).forEach(([key, value]) => {
            if (key === 'photos') {
                (value as (File | string)[]).forEach((photo, index) => {
                    if (photo instanceof File) {
                        formData.append(`photos[${index}]`, photo);
                    }
                });
            } else if (key === 'amenities') {
                (value as string[]).forEach((amenity, index) => {
                    formData.append(`amenities[${index}]`, amenity);
                });
            } else if (key === 'youtube_links') {
                (value as string[]).forEach((link, index) => {
                    formData.append(`youtube_links[${index}]`, link);
                });
            } else if (key === 'room_types') {
                // Room types are handled separately below
            } else if (key !== '_method') {
                // Handle boolean values properly for Laravel validation
                if (typeof value === 'boolean') {
                    formData.append(key, value ? '1' : '0');
                } else {
                    formData.append(key, value as string);
                }
            }
        });

        // Append existing photo URLs separately
        photoPreviews.forEach((preview) => {
            if (!preview.isNew) {
                formData.append('existing_photos[]', preview.url);
            }
        });

        // Append room types
        roomTypes.forEach((roomType, index) => {
            formData.append(`room_types[${index}][id]`, roomType.id);
            formData.append(`room_types[${index}][name]`, roomType.name);
            formData.append(`room_types[${index}][capacity]`, (roomType.capacity || 1).toString());
            formData.append(`room_types[${index}][beds]`, (roomType.number_of_beds || 1).toString());
            const totalRooms = (roomType.male_rooms || 0) + (roomType.female_rooms || 0) + (roomType.mixed_rooms || 0);
            formData.append(`room_types[${index}][available_rooms]`, (totalRooms || 1).toString());
            formData.append(`room_types[${index}][semester_type]`, 'both');
        });

        formData.append('_method', 'put'); // Important for Inertia PUT request

        router.post(`/admin/hostels/${hostel.id}`, formData, {
            forceFormData: true,
            onProgress: (progress) => {
                setUploadProgress(Math.round((progress.loaded / progress.total) * 100));
            },
            onStart: () => {
                setIsUploading(true);
                setUploadProgress(0);
            },
            onSuccess: () => {
                setIsUploading(false);
                setUploadProgress(100);
                router.visit('/admin/hostels');
            },
            onError: (errors: any) => {
                setIsUploading(false);
                setUploadProgress(0);
            },
            onFinish: () => {
                setIsUploading(false);
            }
        });
    };

    return (
        <AppLayout>
            <Head title={`Edit Hostel: ${hostel.name}`} />
            
            {/* Room Distribution Alert Dialog */}
            <AlertDialog open={showRoomDistributionDialog} onOpenChange={setShowRoomDistributionDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                            <AlertCircle className="h-5 w-5 text-blue-600" />
                            Room Distribution Information
                        </AlertDialogTitle>
                        <AlertDialogDescription className="space-y-3">
                            <p>
                                <strong>Important:</strong> When editing room types, please note the following about room distribution:
                            </p>
                            <ul className="list-disc pl-6 space-y-2">
                                <li>
                                    <strong>Existing rooms</strong> are currently displayed in the "Mixed Rooms" field
                                </li>
                                <li>
                                    You can <strong>redistribute these rooms</strong> across Male, Female, and Mixed categories as needed
                                </li>
                                <li>
                                    The <strong>total number</strong> of Male + Female + Mixed rooms will determine the available rooms for booking
                                </li>
                                <li>
                                    This gives you flexibility to manage gender-specific accommodations for your hostel
                                </li>
                            </ul>
                            <p className="text-sm text-gray-600">
                                For example: If you have 6 mixed rooms, you could change it to 2 male rooms, 2 female rooms, and 2 mixed rooms.
                            </p>
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={() => setShowRoomDistributionDialog(false)}>
                            Got it, proceed with editing
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            
            <div className="flex h-full flex-1 flex-col gap-4 md:gap-8 p-4 md:p-6 bg-white">
                <div className="flex items-center gap-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.visit('/admin/hostels')}
                        className="rounded-full"
                    >
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Edit Hostel</h1>
                        <p className="mt-1 text-sm md:text-base text-gray-600">Update details for {hostel.name}</p>
                    </div>
                </div>

                <div className="w-full">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Basic Information */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="md:col-span-2">
                                    <Label htmlFor="name">Hostel Name *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={e => setData('name', e.target.value)}
                                        className="mt-1"
                                        placeholder="Enter hostel name"
                                        required
                                    />
                                    {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                                </div>
                                
                                <div className="md:col-span-2">
                                    <Label htmlFor="description">Description *</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={e => setData('description', e.target.value)}
                                        className="mt-1"
                                        placeholder="Describe the hostel, its features, and what makes it special..."
                                        rows={4}
                                        required
                                    />
                                    {errors.description && <p className="text-sm text-red-600 mt-1">{errors.description}</p>}
                                </div>

                                <div className="md:col-span-2">
                                    <Label htmlFor="owner_id">Hostel Owner *</Label>
                                    <select
                                        id="owner_id"
                                        value={data.owner_id}
                                        onChange={e => setData('owner_id', e.target.value)}
                                        className="w-full mt-1 rounded-md border border-gray-300 px-3 py-2"
                                        required
                                    >
                                        <option value="">Select a hostel admin</option>
                                        {hostel_admins.map(admin => (
                                            <option key={admin.id} value={admin.id}>
                                                {admin.name} ({admin.email})
                                            </option>
                                        ))}
                                    </select>
                                    {errors.owner_id && <p className="text-sm text-red-600 mt-1">{errors.owner_id}</p>}
                                </div>
                            </div>
                        </div>

                        {/* Location */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Location</h2>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="md:col-span-2">
                                    <Label htmlFor="address">Address *</Label>
                                    <Input
                                        id="address"
                                        value={data.address}
                                        onChange={e => setData('address', e.target.value)}
                                        className="mt-1"
                                        placeholder="Enter full address"
                                        required
                                    />
                                    {errors.address && <p className="text-sm text-red-600 mt-1">{errors.address}</p>}
                                </div>
                                
                                <div>
                                    <Label htmlFor="city">City *</Label>
                                    <Input
                                        id="city"
                                        value={data.city}
                                        onChange={e => setData('city', e.target.value)}
                                        className="mt-1"
                                        placeholder="Enter city"
                                        required
                                    />
                                    {errors.city && <p className="text-sm text-red-600 mt-1">{errors.city}</p>}
                                </div>
                                
                                <div>
                                    <Label htmlFor="state">State/Region *</Label>
                                    <Select value={data.state} onValueChange={(value) => setData('state', value)} required>
                                        <SelectTrigger className="mt-1 w-full">
                                            <SelectValue placeholder="Select Region" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {ghanaianRegions.map((region) => (
                                                <SelectItem key={region} value={region}>
                                                    {region}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.state && <p className="text-sm text-red-600 mt-1">{errors.state}</p>}
                                </div>
                                
                                <div>
                                    <Label htmlFor="country">Country *</Label>
                                    <Input
                                        id="country"
                                        value={data.country}
                                        onChange={e => setData('country', e.target.value)}
                                        className="mt-1"
                                        required
                                    />
                                    {errors.country && <p className="text-sm text-red-600 mt-1">{errors.country}</p>}
                                </div>

                                <div>
                                    <Label htmlFor="postal_code">Postal Code</Label>
                                    <Input
                                        id="postal_code"
                                        value={data.postal_code}
                                        onChange={(e) => setData('postal_code', e.target.value)}
                                        placeholder="00233"
                                    />
                                    {errors.postal_code && (
                                        <p className="text-sm text-red-600 mt-1">{errors.postal_code}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="latitude">Latitude</Label>
                                    <Input
                                        id="latitude"
                                        value={data.latitude}
                                        onChange={(e) => setData('latitude', e.target.value)}
                                        placeholder="5.6037"
                                    />
                                    {errors.latitude && (
                                        <p className="text-sm text-red-600 mt-1">{errors.latitude}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="longitude">Longitude</Label>
                                    <Input
                                        id="longitude"
                                        value={data.longitude}
                                        onChange={(e) => setData('longitude', e.target.value)}
                                        placeholder="-0.1870"
                                    />
                                    {errors.longitude && (
                                        <p className="text-sm text-red-600 mt-1">{errors.longitude}</p>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Operational Details */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Operational Details</h2>
                            <div>
                                <Label htmlFor="house_rules">Hostel Rules</Label>
                                <Textarea
                                    id="house_rules"
                                    value={data.house_rules}
                                    onChange={(e) => setData('house_rules', e.target.value)}
                                    placeholder="e.g., Quiet hours from 10 PM to 6 AM. No opposite gender visitors after 9 PM..."
                                    rows={3}
                                />
                                {errors.house_rules && (
                                    <p className="text-sm text-red-600 mt-1">{errors.house_rules}</p>
                                )}
                            </div>

                            <div className="mt-4">
                                <Label htmlFor="cancellation_policy">Cancellation Policy</Label>
                                <Textarea
                                    id="cancellation_policy"
                                    value={data.cancellation_policy}
                                    onChange={(e) => setData('cancellation_policy', e.target.value)}
                                    placeholder="e.g., Cancellation allowed up to 2 weeks before semester start with full refund..."
                                    rows={3}
                                />
                                {errors.cancellation_policy && (
                                    <p className="text-sm text-red-600 mt-1">{errors.cancellation_policy}</p>
                                )}
                            </div>
                        </div>

                        {/* Booking & Payment Settings (Feature 1 & 3) */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Booking & Payment Settings</h2>
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="payment_period">Payment Period *</Label>
                                    <Select
                                        value={data.payment_period}
                                        onValueChange={(value: 'month' | 'semester' | 'year') => setData('payment_period', value)}
                                        required
                                    >
                                        <SelectTrigger className="mt-1 w-full">
                                            <SelectValue placeholder="Select Payment Period" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="month">Per Month</SelectItem>
                                            <SelectItem value="semester">Per Semester</SelectItem>
                                            <SelectItem value="year">Per Academic Year</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.payment_period && (
                                        <p className="text-sm text-red-600 mt-1">{errors.payment_period}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="hostel_type">Hostel Type *</Label>
                                    <Select
                                        value={data.hostel_type}
                                        onValueChange={(value: 'university' | 'homestel' | 'other') => setData('hostel_type', value)}
                                        required
                                    >
                                        <SelectTrigger className="mt-1 w-full">
                                            <SelectValue placeholder="Select Hostel Type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="university">Hostel</SelectItem>
                                            <SelectItem value="homestel">Homestel</SelectItem>
                                            <SelectItem value="other">Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.hostel_type && (
                                        <p className="text-sm text-red-600 mt-1">{errors.hostel_type}</p>
                                    )}
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="allow_deposit"
                                        checked={data.allow_deposit}
                                        onCheckedChange={(checked) => setData('allow_deposit', checked as boolean)}
                                    />
                                    <Label htmlFor="allow_deposit" className="text-sm font-medium leading-none">
                                        Allow part-payment (deposit)
                                    </Label>
                                    {errors.allow_deposit && <p className="text-sm text-red-600 mt-1">{errors.allow_deposit}</p>}
                                </div>

                                {/* Pricing Section */}
                                <div className="border-t pt-4">
                                    <h3 className="text-md font-medium text-gray-900 mb-3">Pricing Information</h3>
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="base_price">Base Price per {getPaymentPeriodText()} (GHS) *</Label>
                                            <Input
                                                id="base_price"
                                                type="number"
                                                value={data.base_price || ''}
                                                onChange={(e) => setData('base_price', e.target.value)}
                                                placeholder={`e.g., ${data.payment_period === 'month' ? '500' : data.payment_period === 'semester' ? '2000' : '4000'}`}
                                                required
                                                min={0}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Starting price for the most basic room type per {getPaymentPeriodText().toLowerCase()}</p>
                                            {errors.base_price && <p className="text-sm text-red-600 mt-1">{errors.base_price}</p>}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="service_fee">Service Fee (GHS)</Label>
                                            <Input
                                                id="service_fee"
                                                type="number"
                                                value={data.service_fee || ''}
                                                onChange={(e) => setData('service_fee', e.target.value)}
                                                placeholder="e.g., 50"
                                                min={0}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">One-time service fee per booking</p>
                                            {errors.service_fee && <p className="text-sm text-red-600 mt-1">{errors.service_fee}</p>}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="deposit_percentage">Deposit Percentage (%)</Label>
                                            <Input
                                                id="deposit_percentage"
                                                type="number"
                                                value={data.deposit_percentage || ''}
                                                onChange={(e) => setData('deposit_percentage', e.target.value)}
                                                placeholder="e.g., 30"
                                                min={0}
                                                max={100}
                                                disabled={!data.allow_deposit}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Percentage required as deposit when partial payment is allowed</p>
                                            {errors.deposit_percentage && <p className="text-sm text-red-600 mt-1">{errors.deposit_percentage}</p>}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="cancellation_fee">Cancellation Fee (GHS)</Label>
                                            <Input
                                                id="cancellation_fee"
                                                type="number"
                                                value={data.cancellation_fee || ''}
                                                onChange={(e) => setData('cancellation_fee', e.target.value)}
                                                placeholder="e.g., 100"
                                                min={0}
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Fee charged for booking cancellations</p>
                                            {errors.cancellation_fee && <p className="text-sm text-red-600 mt-1">{errors.cancellation_fee}</p>}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Hostel Amenities */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Hostel Amenities</h2>
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                {amenitiesList.map((amenity) => (
                                    <label
                                        key={amenity}
                                        className="flex items-center space-x-2 cursor-pointer"
                                    >
                                        <input
                                            type="checkbox"
                                            checked={selectedAmenities.includes(amenity)}
                                            onChange={() => handleAmenityToggle(amenity)}
                                            className="rounded border-gray-300"
                                        />
                                        <span className="text-sm capitalize">
                                            {amenity.replace('_', ' ')}
                                        </span>
                                    </label>
                                ))}
                            </div>
                            {errors.amenities && <p className="text-sm text-red-600 mt-1">{errors.amenities}</p>}
                        </div>

                        {/* YouTube Video Links (Feature 6) */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">YouTube Video Links</h2>
                            <div className="space-y-4">
                                {youtubeLinks.map((link, index) => (
                                    <div key={index} className="flex items-center gap-2">
                                        <Input
                                            type="url"
                                            value={link}
                                            onChange={(e) => handleYoutubeLinkChange(index, e.target.value)}
                                            placeholder="https://www.youtube.com/watch?v=yourvideo"
                                        />
                                        {youtubeLinks.length > 1 && (
                                            <Button type="button" variant="destructive" onClick={() => removeYoutubeLink(index)}>
                                                <X className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button type="button" variant="outline" onClick={addYoutubeLink}>
                                    Add Another Video Link
                                </Button>
                                {errors.youtube_links && (
                                    <p className="text-sm text-red-600 mt-1">{errors.youtube_links}</p>
                                )}
                            </div>
                        </div>

                        {/* Room Types (Feature 2) */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Room Types</h2>
                            
                            {/* Information Alert */}
                            <Alert className="mb-6 border-blue-200 bg-blue-50">
                                <AlertCircle className="h-4 w-4 text-blue-600" />
                                <AlertDescription className="text-blue-800">
                                    <strong>Room Distribution:</strong> The existing rooms are currently shown in the "Mixed Rooms" field. 
                                    You can redistribute them across Male, Female, and Mixed rooms as needed. 
                                    The total of all three fields will determine the available rooms for this room type.
                                </AlertDescription>
                            </Alert>
                            
                            <div className="space-y-6">
                                {roomTypes.map((roomType, index) => (
                                    <div key={roomType.id} className="border p-4 rounded-lg relative">
                                        <Button
                                            type="button"
                                            variant="destructive"
                                            size="icon"
                                            className="absolute top-2 right-2 rounded-full h-6 w-6 z-10"
                                            onClick={() => handleRemoveRoomType(roomType.id)}
                                        >
                                            <X className="h-3 w-3" />
                                        </Button>
                                        <h4 className="font-semibold mb-4">Room Type {index + 1}</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <div>
                                                <Label htmlFor={`room_type_${index}_name`}>Room Type Name *</Label>
                                                <Input
                                                    id={`room_type_${index}_name`}
                                                    value={roomType.name}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'name', e.target.value)}
                                                    placeholder="e.g., 2 in 1"
                                                    required
                                                />
                                                {errors.room_types && <p className="text-sm text-red-600 mt-1">Please fill in all required fields</p>}
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_capacity`}>Capacity (persons) *</Label>
                                                <Input
                                                    id={`room_type_${index}_capacity`}
                                                    type="number"
                                                    value={roomType.capacity || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'capacity', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    required
                                                    min={1}
                                                />

                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_beds`}>Number of Beds *</Label>
                                                <Input
                                                    id={`room_type_${index}_beds`}
                                                    type="number"
                                                    value={roomType.number_of_beds || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'number_of_beds', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    required
                                                    min={1}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_male_rooms`}>Number of Male Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_male_rooms`}
                                                    type="number"
                                                    value={roomType.male_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'male_rooms', e.target.value)}
                                                    placeholder="e.g., 5"
                                                    min={0}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_female_rooms`}>Number of Female Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_female_rooms`}
                                                    type="number"
                                                    value={roomType.female_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'female_rooms', e.target.value)}
                                                    placeholder="e.g., 5"
                                                    min={0}
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor={`room_type_${index}_mixed_rooms`}>Number of Mixed Rooms</Label>
                                                <Input
                                                    id={`room_type_${index}_mixed_rooms`}
                                                    type="number"
                                                    value={roomType.mixed_rooms || ''}
                                                    onChange={(e) => handleRoomTypeChange(roomType.id, 'mixed_rooms', e.target.value)}
                                                    placeholder="e.g., 2"
                                                    min={0}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                <Button type="button" onClick={handleAddRoomType} variant="outline" className="w-full">
                                    Add Another Room Type
                                </Button>
                            </div>
                        </div>

                        {/* Hostel Photos */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Hostel Photos</h2>
                            <div
                                className="border-2 border-dashed rounded-lg p-6 text-center transition-colors"
                                onDragOver={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    e.dataTransfer.dropEffect = 'copy';
                                    setIsDragOver(true);
                                }}
                                onDragLeave={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setIsDragOver(false);
                                }}
                                onDrop={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setIsDragOver(false);
                                    const files = e.dataTransfer.files;
                                    if (files) {
                                        processFiles(files);
                                    }
                                }}
                            >
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    id="photos"
                                    multiple
                                    accept="image/*"
                                    onChange={handlePhotoSelect}
                                    className="hidden"
                                />
                                <label htmlFor="photos" className="cursor-pointer">
                                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                    <p className="mt-2 text-sm text-gray-600">Drag & drop photos here, or click to browse</p>
                                    <p className="text-xs text-gray-500">(Max 10 photos)</p>
                                </label>
                            </div>
                            {uploadErrors.length > 0 && (
                                <Alert variant="destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertDescription>
                                        <ul className="list-disc pl-5">
                                            {uploadErrors.map((error, index) => (
                                                <li key={index}>{error}</li>
                                            ))}
                                        </ul>
                                    </AlertDescription>
                                </Alert>
                            )}
                            {photoPreviews.length > 0 && (
                                <div className="mt-4 grid grid-cols-2 gap-4">
                                    {photoPreviews.map((photo) => (
                                        <div key={photo.id} className="relative">
                                            <img
                                                src={photo.url}
                                                alt={`Photo ${photo.id}`}
                                                className="h-32 w-full object-cover rounded-lg"
                                            />
                                            <Button
                                                type="button"
                                                onClick={() => handleRemovePhoto(photo.id, photo.isNew)}
                                                variant="destructive"
                                                size="icon"
                                                className="absolute top-1 right-1 rounded-full h-6 w-6"
                                            >
                                                <X className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        {/* Super Admin Specific Settings */}
                        <div className="p-6 border rounded-lg bg-white">
                            <h2 className="text-lg font-semibold text-gray-900 mb-4">Admin Settings</h2>
                            <div className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="is_active"
                                        checked={data.is_active}
                                        onCheckedChange={(checked) => setData('is_active', checked as boolean)}
                                    />
                                    <Label htmlFor="is_active" className="text-sm font-medium leading-none">
                                        Hostel is Active (visible to users)
                                    </Label>
                                    {errors.is_active && <p className="text-sm text-red-600 mt-1">{errors.is_active}</p>}
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="is_verified"
                                        checked={data.is_verified}
                                        onCheckedChange={(checked) => setData('is_verified', checked as boolean)}
                                    />
                                    <Label htmlFor="is_verified" className="text-sm font-medium leading-none">
                                        Hostel is Verified
                                    </Label>
                                    {errors.is_verified && <p className="text-sm text-red-600 mt-1">{errors.is_verified}</p>}
                                </div>
                            </div>
                        </div>

                        <div className="flex justify-end gap-4">
                            <Button type="button" variant="outline" onClick={() => router.visit('/admin/hostels')}>
                                Cancel
                            </Button>
                            <div className="flex flex-col items-end gap-2">
                                {isUploading && (
                                    <div className="w-64">
                                        <div className="flex justify-between text-xs text-gray-600 mb-1">
                                            <span>Uploading hostel updates...</span>
                                            <span>{uploadProgress}%</span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div 
                                                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                                style={{ width: `${uploadProgress}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                )}
                                <Button type="submit" disabled={processing || isUploading}>
                                    {isUploading ? 'Uploading...' : processing ? 'Updating...' : 'Update Hostel'}
                                </Button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
} 