import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm } from '@inertiajs/react';
import { 
    Settings as SettingsIcon, Database, Shield, Globe, Mail, 
    CreditCard, Activity, AlertTriangle, CheckCircle,
    Server, Monitor, Clock, Users, Building2
} from 'lucide-react';
import { useState } from 'react';

interface Props {
    settings: {
        platform_name: string;
        environment: string;
        debug_mode: boolean;
        maintenance_mode: boolean;
    };
}

export default function Settings({ settings }: Props) {
    const [activeTab, setActiveTab] = useState('general');

    const { data, setData, post, processing } = useForm({
        platform_name: settings.platform_name,
        maintenance_mode: settings.maintenance_mode,
        backup_frequency: 'daily',
        max_upload_size: '10',
        session_timeout: '120',
    });

    const handleSaveSettings = (e: React.FormEvent) => {
        e.preventDefault();
        post('/admin/settings', {
            preserveState: true,
        });
    };

    const getEnvironmentBadge = (env: string) => {
        const config = {
            production: { color: 'bg-green-100 text-green-800', label: 'Production' },
            staging: { color: 'bg-yellow-100 text-yellow-800', label: 'Staging' },
            local: { color: 'bg-blue-100 text-blue-800', label: 'Development' },
        };
        const envConfig = config[env as keyof typeof config] || config.local;
        return <Badge className={envConfig.color}>{envConfig.label}</Badge>;
    };

    const tabs = [
        { id: 'general', label: 'General', icon: SettingsIcon },
        { id: 'security', label: 'Security', icon: Shield },
        { id: 'payments', label: 'Payments', icon: CreditCard },
        { id: 'system', label: 'System', icon: Server },
        { id: 'maintenance', label: 'Maintenance', icon: AlertTriangle },
    ];

    return (
        <AppLayout>
            <Head title="System Settings" />
            
            <div className="flex h-full flex-1 flex-col gap-8 p-6 bg-white">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">System Settings</h1>
                        <p className="mt-2 text-gray-600">Configure platform settings and system preferences</p>
                    </div>
                </div>

                {/* System Status */}
                <div className="grid gap-6 md:grid-cols-4">
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <Activity className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">System Status</p>
                                <p className="text-lg font-bold text-green-600">Healthy</p>
                            </div>
                        </div>
                    </div>
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <Globe className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Environment</p>
                                {getEnvironmentBadge(settings.environment)}
                            </div>
                        </div>
                    </div>
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <Monitor className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Debug Mode</p>
                                <Badge className={settings.debug_mode ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}>
                                    {settings.debug_mode ? 'Enabled' : 'Disabled'}
                                </Badge>
                            </div>
                        </div>
                    </div>
                    <div className="p-6 border rounded-lg bg-white">
                        <div className="flex items-center">
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <AlertTriangle className="h-6 w-6 text-orange-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Maintenance</p>
                                <Badge className={settings.maintenance_mode ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}>
                                    {settings.maintenance_mode ? 'Active' : 'Normal'}
                                </Badge>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Tabs */}
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                        {tabs.map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                                        activeTab === tab.id
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    <Icon className={`mr-2 h-4 w-4 ${
                                        activeTab === tab.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                                    }`} />
                                    {tab.label}
                                </button>
                            );
                        })}
                    </nav>
                </div>

                {/* Tab Content */}
                <div className="mt-6">
                    {activeTab === 'general' && (
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Platform Information</h3>
                                <form onSubmit={handleSaveSettings} className="space-y-4">
                                    <div>
                                        <Label htmlFor="platform_name">Platform Name</Label>
                                        <Input
                                            id="platform_name"
                                            value={data.platform_name}
                                            onChange={e => setData('platform_name', e.target.value)}
                                        />
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="maintenance_mode"
                                            checked={data.maintenance_mode}
                                            onChange={e => setData('maintenance_mode', e.target.checked)}
                                            className="rounded"
                                        />
                                        <Label htmlFor="maintenance_mode">Enable Maintenance Mode</Label>
                                    </div>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Saving...' : 'Save Changes'}
                                    </Button>
                                </form>
                            </div>
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
                                <div className="space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Laravel Version</span>
                                        <span className="text-sm font-medium text-gray-900">11.x</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">PHP Version</span>
                                        <span className="text-sm font-medium text-gray-900">8.2+</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Database</span>
                                        <span className="text-sm font-medium text-gray-900">MySQL</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Cache Driver</span>
                                        <span className="text-sm font-medium text-gray-900">Redis</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'security' && (
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Settings</h3>
                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="session_timeout">Session Timeout (minutes)</Label>
                                        <Input
                                            id="session_timeout"
                                            type="number"
                                            value={data.session_timeout}
                                            onChange={e => setData('session_timeout', e.target.value)}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="max_upload_size">Max Upload Size (MB)</Label>
                                        <Input
                                            id="max_upload_size"
                                            type="number"
                                            value={data.max_upload_size}
                                            onChange={e => setData('max_upload_size', e.target.value)}
                                        />
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <input type="checkbox" id="two_factor" className="rounded" />
                                        <Label htmlFor="two_factor">Require Two-Factor Authentication</Label>
                                    </div>
                                </div>
                            </div>
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Status</h3>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">SSL Certificate</span>
                                        <Badge className="bg-green-100 text-green-800">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            Valid
                                        </Badge>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Firewall</span>
                                        <Badge className="bg-green-100 text-green-800">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            Active
                                        </Badge>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Rate Limiting</span>
                                        <Badge className="bg-green-100 text-green-800">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            Enabled
                                        </Badge>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'payments' && (
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Gateway</h3>
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium text-gray-900">Paystack Integration</span>
                                        <Badge className="bg-green-100 text-green-800">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            Connected
                                        </Badge>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Environment</span>
                                        <span className="text-sm font-medium text-gray-900">Live</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Currency</span>
                                        <span className="text-sm font-medium text-gray-900">GHS (Ghana Cedis)</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Transaction Fee</span>
                                        <span className="text-sm font-medium text-gray-900">1.95% + GH₵0.30</span>
                                    </div>
                                </div>
                            </div>
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Settings</h3>
                                <div className="space-y-4">
                                    <div className="flex items-center space-x-2">
                                        <input type="checkbox" id="auto_capture" defaultChecked className="rounded" />
                                        <Label htmlFor="auto_capture">Auto-capture payments</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <input type="checkbox" id="email_receipts" defaultChecked className="rounded" />
                                        <Label htmlFor="email_receipts">Send email receipts</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <input type="checkbox" id="refund_notifications" defaultChecked className="rounded" />
                                        <Label htmlFor="refund_notifications">Refund notifications</Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'system' && (
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">System Performance</h3>
                                <div className="space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">CPU Usage</span>
                                        <span className="text-sm font-medium text-green-600">12%</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Memory Usage</span>
                                        <span className="text-sm font-medium text-yellow-600">67%</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Disk Usage</span>
                                        <span className="text-sm font-medium text-green-600">34%</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Database Size</span>
                                        <span className="text-sm font-medium text-gray-900">127 MB</span>
                                    </div>
                                </div>
                            </div>
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Background Jobs</h3>
                                <div className="space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Queue Status</span>
                                        <Badge className="bg-green-100 text-green-800">Running</Badge>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Jobs Processed</span>
                                        <span className="text-sm font-medium text-gray-900">1,247</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Failed Jobs</span>
                                        <span className="text-sm font-medium text-red-600">3</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Average Processing Time</span>
                                        <span className="text-sm font-medium text-gray-900">0.8s</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'maintenance' && (
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Backup & Maintenance</h3>
                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="backup_frequency">Backup Frequency</Label>
                                        <select
                                            id="backup_frequency"
                                            value={data.backup_frequency}
                                            onChange={e => setData('backup_frequency', e.target.value)}
                                            className="w-full rounded-md border border-gray-300 px-3 py-2"
                                        >
                                            <option value="hourly">Hourly</option>
                                            <option value="daily">Daily</option>
                                            <option value="weekly">Weekly</option>
                                        </select>
                                    </div>
                                    <div className="space-y-2">
                                        <Button variant="outline" className="w-full">
                                            <Database className="mr-2 h-4 w-4" />
                                            Create Backup Now
                                        </Button>
                                        <Button variant="outline" className="w-full">
                                            <Activity className="mr-2 h-4 w-4" />
                                            Clear Cache
                                        </Button>
                                        <Button variant="outline" className="w-full">
                                            <Clock className="mr-2 h-4 w-4" />
                                            Clear Logs
                                        </Button>
                                    </div>
                                </div>
                            </div>
                            <div className="p-6 border rounded-lg bg-white">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Last Maintenance Activities</h3>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Last Backup</span>
                                        <span className="text-sm font-medium text-gray-900">2 hours ago</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Cache Cleared</span>
                                        <span className="text-sm font-medium text-gray-900">1 day ago</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Log Rotation</span>
                                        <span className="text-sm font-medium text-gray-900">3 days ago</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">System Update</span>
                                        <span className="text-sm font-medium text-gray-900">1 week ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
} 