import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    Plus, TrendingUp, Users, Building2, Calendar, 
    CreditCard, CheckCircle, Clock, AlertCircle, 
    MapPin, Star, Eye, BarChart3, DollarSign,
    Activity, UserCheck, BedDouble, Search, Bed
} from 'lucide-react';
import { useState, useEffect } from 'react';

interface DashboardStats {
    total_bookings: number;
    confirmed_bookings: number;
    pending_bookings: number;
    cancelled_bookings: number;
    total_revenue: number;
    this_month_bookings: number;
}

interface RecentBooking {
    id: string;
    booking_reference: string;
    hostel_name: string;
    room_type_name?: string;
    guest_name?: string;
    check_in_date?: string;
    academic_year?: string;
    semester?: string;
    status: string;
    total_amount: number;
    payment_status: string;
    created_at?: string;
}

interface Props {
    recent_bookings?: RecentBooking[];
    popular_destinations?: Array<{ city: string; count: number; }>;
    stats?: {
        platform_overview: {
            total_users: number;
            total_hostels: number;
            total_bookings: number;
            total_revenue: number;
            active_hostels: number;
            verified_hostels: number;
        };
        recent_activity: {
            new_users: number;
            new_hostels: number;
            new_bookings: number;
            revenue_period: number;
        };
        booking_stats: {
            pending: number;
            confirmed: number;
            checked_in: number;
            completed: number;
            cancelled: number;
        };
        payment_stats: {
            paid: number;
            pending: number;
            failed: number;
        };
    };
}

const statusConfig = {
    pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
    confirmed: { label: 'Confirmed', color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
    checked_in: { label: 'Checked In', color: 'bg-green-100 text-green-800', icon: UserCheck },
    checked_out: { label: 'Completed', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
    cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: AlertCircle },
};

export default function Dashboard({ recent_bookings = [], popular_destinations = [], stats: superAdminStats }: Props) {
    const { auth } = usePage().props as any;
    const [stats, setStats] = useState<DashboardStats | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Only fetch stats for non-super-admin users
        if (auth.user.role !== 'super_admin') {
            fetchStats();
        } else {
            setLoading(false);
        }
    }, [auth.user.role]);

    const fetchStats = async () => {
        try {
            const response = await fetch('/api/bookings/analytics');
            const data = await response.json();
            setStats(data);
        } catch (error) {
            // Failed to fetch stats
        } finally {
            setLoading(false);
        }
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (date: string | undefined) => {
        if (!date) return '';
        return new Date(date).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
        });
    };

    const getStatusBadge = (status: string) => {
        const config = statusConfig[status as keyof typeof statusConfig];
        if (!config) return null;
        
        const Icon = config.icon;
        return (
            <Badge className={`${config.color} gap-1`}>
                <Icon className="h-3 w-3" />
                {config.label}
            </Badge>
        );
    };

    const getGreeting = () => {
        const hour = new Date().getHours();
        if (hour < 12) return 'Good morning';
        if (hour < 17) return 'Good afternoon';
        return 'Good evening';
    };

    const getRoleSpecificTitle = () => {
        switch (auth.user.role) {
            case 'student':
                return 'Your Booking Dashboard';
            case 'guest':
                return 'Your Travel Dashboard';
            case 'hostel_admin':
                return 'Hostel Management Dashboard';
            case 'super_admin':
                return 'Platform Administration';
            default:
                return 'Dashboard';
        }
    };

    // Student/Guest Dashboard
    if (auth.user.role === 'student' || auth.user.role === 'guest') {
        return (
            <AppLayout>
                <Head title="Dashboard" />
                
                <div className="flex h-full flex-1 flex-col gap-6 p-4">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                                {getGreeting()}, {auth.user.name}!
                            </h1>
                            <p className="mt-2 text-gray-600 dark:text-gray-400">
                                {getRoleSpecificTitle()}
                            </p>
                        </div>
                        <Button onClick={() => router.visit('/search')}>
                            <Search className="mr-2 h-4 w-4" />
                            Find Hostel
                        </Button>
                    </div>

                    {/* Quick Stats */}
                    {stats && (
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center">
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <Calendar className="h-6 w-6 text-blue-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                                            <p className="text-2xl font-bold text-gray-900">{stats.total_bookings}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center">
                                        <div className="p-2 bg-green-100 rounded-lg">
                                            <CheckCircle className="h-6 w-6 text-green-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Confirmed</p>
                                            <p className="text-2xl font-bold text-gray-900">{stats.confirmed_bookings}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center">
                                        <div className="p-2 bg-yellow-100 rounded-lg">
                                            <Clock className="h-6 w-6 text-yellow-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Pending</p>
                                            <p className="text-2xl font-bold text-gray-900">{stats.pending_bookings}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center">
                                        <div className="p-2 bg-purple-100 rounded-lg">
                                            <DollarSign className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Total Spent</p>
                                            <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.total_revenue)}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    )}

                    <div className="grid gap-6 lg:grid-cols-3">
                        {/* Recent Bookings */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between">
                                    <div>
                                        <CardTitle>Recent Bookings</CardTitle>
                                        <CardDescription>Your latest reservations</CardDescription>
                                    </div>
                                    <Button variant="outline" onClick={() => router.visit('/bookings')}>
                                        View All
                                    </Button>
                                </CardHeader>
                                <CardContent>
                                    {recent_bookings.length > 0 ? (
                                        <div className="space-y-4">
                                            {recent_bookings.slice(0, 5).map((booking) => (
                                                <div
                                                    key={booking.id}
                                                    className="flex items-center justify-between p-4 rounded-lg border cursor-pointer hover:bg-gray-50"
                                                    onClick={() => router.visit(`/bookings/${booking.id}`)}
                                                >
                                                    <div className="flex items-center space-x-4">
                                                        <Building2 className="h-8 w-8 text-gray-400" />
                                                        <div>
                                                            <p className="font-medium">{booking.hostel_name}</p>
                                                                                                        <p className="text-sm text-gray-600">
                                                {booking.booking_reference}
                                                {booking.room_type_name && ` • ${booking.room_type_name}`}
                                            </p>
                                            {booking.academic_year && booking.semester && (
                                                <p className="text-xs text-gray-500">
                                                    {booking.academic_year} • {booking.semester} semester
                                                </p>
                                            )}
                                            {booking.check_in_date && (
                                                <p className="text-xs text-gray-500">
                                                    Check-in: {formatDate(booking.check_in_date)}
                                                </p>
                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center space-x-3">
                                                        <span className="font-medium">{formatCurrency(booking.total_amount)}</span>
                                                        {getStatusBadge(booking.status)}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                                            <h3 className="mt-4 text-lg font-medium text-gray-900">No bookings yet</h3>
                                            <p className="mt-2 text-gray-600">Start exploring hostels and make your first booking!</p>
                                            <Button className="mt-4" onClick={() => router.visit('/search')}>
                                                <Search className="mr-2 h-4 w-4" />
                                                Explore Hostels
                                            </Button>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Popular Destinations */}
                        <div>
                            <Card>
                                <CardHeader>
                                    <CardTitle>Popular Destinations</CardTitle>
                                    <CardDescription>Trending places to visit</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {popular_destinations.slice(0, 5).map((destination, index) => (
                                            <div key={index} className="flex items-center justify-between">
                                                <div className="flex items-center space-x-2">
                                                    <MapPin className="h-4 w-4 text-gray-400" />
                                                    <span className="text-sm font-medium">{destination.city}</span>
                                                </div>
                                                <span className="text-sm text-gray-600">{destination.count} hostels</span>
                                            </div>
                                        ))}
                                    </div>
                                    <Button
                                        variant="outline"
                                        className="w-full mt-4"
                                        onClick={() => router.visit('/search')}
                                    >
                                        Explore All Destinations
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    // Hostel Admin Dashboard
    if (auth.user.role === 'hostel_admin') {
        return (
            <AppLayout>
                <Head title="Dashboard" />
                
                <div className="flex h-full flex-1 flex-col gap-6 p-4">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                                {getGreeting()}, {auth.user.name}!
                            </h1>
                            <p className="mt-2 text-gray-600 dark:text-gray-400">
                                {getRoleSpecificTitle()}
                            </p>
                        </div>
                        <div className="flex gap-2">
                            <Button variant="outline" onClick={() => router.visit('/hostels')}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Hostels
                            </Button>
                            <Button onClick={() => router.visit('/hostels/create')}>
                                <Plus className="mr-2 h-4 w-4" />
                                Add Hostel
                            </Button>
                        </div>
                    </div>

                    {/* Key Metrics */}
                    {stats && (
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center">
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <Calendar className="h-6 w-6 text-blue-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                                            <p className="text-2xl font-bold text-gray-900">{stats.total_bookings}</p>
                                            <p className="text-xs text-gray-600">{stats.this_month_bookings} this month</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center">
                                        <div className="p-2 bg-green-100 rounded-lg">
                                            <DollarSign className="h-6 w-6 text-green-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                                            <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.total_revenue)}</p>
                                            <p className="text-xs text-green-600">↗ Revenue growth</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center">
                                        <div className="p-2 bg-yellow-100 rounded-lg">
                                            <Clock className="h-6 w-6 text-yellow-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Pending Bookings</p>
                                            <p className="text-2xl font-bold text-gray-900">{stats.pending_bookings}</p>
                                            <p className="text-xs text-gray-600">Need attention</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center">
                                        <div className="p-2 bg-purple-100 rounded-lg">
                                            <Activity className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Occupancy Rate</p>
                                            <p className="text-2xl font-bold text-gray-900">
                                                {stats.total_bookings > 0 ? Math.round((stats.confirmed_bookings / stats.total_bookings) * 100) : 0}%
                                            </p>
                                            <Progress 
                                                value={stats.total_bookings > 0 ? (stats.confirmed_bookings / stats.total_bookings) * 100 : 0} 
                                                className="h-2 mt-2" 
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    )}

                    <div className="grid gap-6 lg:grid-cols-3">
                        {/* Recent Bookings */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between">
                                    <div>
                                        <CardTitle>Recent Bookings</CardTitle>
                                        <CardDescription>Latest reservations for your hostels</CardDescription>
                                    </div>
                                    <Button variant="outline" onClick={() => router.visit('/bookings')}>
                                        Manage All
                                    </Button>
                                </CardHeader>
                                <CardContent>
                                    {recent_bookings.length > 0 ? (
                                        <div className="space-y-4">
                                            {recent_bookings.slice(0, 5).map((booking) => (
                                                <div
                                                    key={booking.id}
                                                    className="flex items-center justify-between p-4 rounded-lg border cursor-pointer hover:bg-gray-50"
                                                    onClick={() => router.visit(`/bookings/${booking.id}`)}
                                                >
                                                    <div className="flex items-center space-x-4">
                                                        <Users className="h-8 w-8 text-gray-400" />
                                                        <div>
                                                            <p className="font-medium">{booking.guest_name}</p>
                                                                                                        <p className="text-sm text-gray-600">
                                                {booking.booking_reference} • {booking.hostel_name}
                                            </p>
                                            {booking.check_in_date && (
                                                <p className="text-xs text-gray-500">
                                                    Check-in: {formatDate(booking.check_in_date)}
                                                </p>
                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center space-x-3">
                                                        <div className="text-right">
                                                            <div className="font-medium">{formatCurrency(booking.total_amount)}</div>
                                                            <div className="text-xs text-gray-600">{booking.payment_status}</div>
                                                        </div>
                                                        {getStatusBadge(booking.status)}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                                            <h3 className="mt-4 text-lg font-medium text-gray-900">No recent bookings</h3>
                                            <p className="mt-2 text-gray-600">Bookings will appear here once guests start making reservations.</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Quick Actions */}
                        <div>
                            <Card>
                                <CardHeader>
                                    <CardTitle>Quick Actions</CardTitle>
                                    <CardDescription>Manage your hostels</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <Button
                                            variant="outline"
                                            className="w-full justify-start"
                                            onClick={() => router.visit('/hostels/create')}
                                        >
                                            <Plus className="mr-2 h-4 w-4" />
                                            Add New Hostel
                                        </Button>
                                        <Button
                                            variant="outline"
                                            className="w-full justify-start"
                                            onClick={() => router.visit('/hostels')}
                                        >
                                            <Building2 className="mr-2 h-4 w-4" />
                                            Manage Hostels
                                        </Button>
                                        <Button
                                            variant="outline"
                                            className="w-full justify-start"
                                            onClick={() => router.visit('/bookings')}
                                        >
                                            <Calendar className="mr-2 h-4 w-4" />
                                            View All Bookings
                                        </Button>
                                        <Button
                                            variant="outline"
                                            className="w-full justify-start"
                                            onClick={() => router.visit('/bookings?status=pending')}
                                        >
                                            <Clock className="mr-2 h-4 w-4" />
                                            Pending Confirmations
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    // Super Admin Dashboard
    return (
        <AppLayout>
            <Head title="Admin Dashboard" />
            
            <div className="flex h-full flex-1 flex-col gap-4 sm:gap-6 lg:gap-8 p-4 sm:p-6 bg-white">
                {/* Header */}
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div>
                        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                            {getGreeting()}, {auth.user.name}!
                        </h1>
                        <p className="mt-1 sm:mt-2 text-sm sm:text-base text-gray-600">
                            Platform Administration Dashboard
                        </p>
                    </div>
                    <Button onClick={() => router.visit('/admin/analytics')} className="w-full sm:w-auto">
                        <BarChart3 className="mr-2 h-4 w-4" />
                        View Analytics
                    </Button>
                </div>

                {/* Platform Stats */}
                {superAdminStats && (
                    <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                        <div className="p-4 sm:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                                    <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                                </div>
                                <div className="ml-3 sm:ml-4 min-w-0">
                                    <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">Total Bookings</p>
                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{superAdminStats.platform_overview.total_bookings}</p>
                                    <p className="text-xs text-gray-600">Platform wide</p>
                                </div>
                            </div>
                        </div>

                        <div className="p-4 sm:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                                    <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                                </div>
                                <div className="ml-3 sm:ml-4 min-w-0">
                                    <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">Total Revenue</p>
                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{formatCurrency(superAdminStats.platform_overview.total_revenue)}</p>
                                    <p className="text-xs text-gray-600">All transactions</p>
                                </div>
                            </div>
                        </div>

                        <div className="p-4 sm:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-purple-100 rounded-lg flex-shrink-0">
                                    <Building2 className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
                                </div>
                                <div className="ml-3 sm:ml-4 min-w-0">
                                    <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">Active Hostels</p>
                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{superAdminStats.platform_overview.active_hostels}</p>
                                    <p className="text-xs text-gray-600">Platform wide</p>
                                </div>
                            </div>
                        </div>

                        <div className="p-4 sm:p-6 border rounded-lg bg-white">
                            <div className="flex items-center">
                                <div className="p-2 bg-orange-100 rounded-lg flex-shrink-0">
                                    <Users className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
                                </div>
                                <div className="ml-3 sm:ml-4 min-w-0">
                                    <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">Total Users</p>
                                    <p className="text-xl sm:text-2xl font-bold text-gray-900">{superAdminStats.platform_overview.total_users}</p>
                                    <p className="text-xs text-gray-600">All user types</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Admin Management Sections */}
                <div className="grid gap-4 md:gap-8 lg:grid-cols-2">
                    {/* System Overview */}
                    <div className="p-4 md:p-6 border rounded-lg bg-white">
                        <div className="mb-3 md:mb-4">
                            <h3 className="text-base md:text-lg font-semibold text-gray-900">System Overview</h3>
                            <p className="text-xs md:text-sm text-gray-600">Platform statistics and health</p>
                        </div>
                        {superAdminStats && (
                            <div className="space-y-3 md:space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-xs md:text-sm font-medium">Pending Hostel Approvals</span>
                                    <Badge variant="outline">{superAdminStats.platform_overview.total_hostels - superAdminStats.platform_overview.verified_hostels}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-xs md:text-sm font-medium">Failed Payments</span>
                                    <Badge variant="outline">{superAdminStats.payment_stats.failed}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-xs md:text-sm font-medium">Pending Bookings</span>
                                    <Badge variant="outline">{superAdminStats.booking_stats.pending}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-xs md:text-sm font-medium">Payment Gateway</span>
                                    <Badge className="bg-green-100 text-green-800">Connected</Badge>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Super Admin Actions */}
                    <div className="p-4 md:p-6 border rounded-lg bg-white">
                        <div className="mb-3 md:mb-4">
                            <h3 className="text-base md:text-lg font-semibold text-gray-900">Platform Management</h3>
                            <p className="text-xs md:text-sm text-gray-600">Administrative functions</p>
                        </div>
                        <div className="space-y-2 md:space-y-3">
                            <Button
                                variant="outline"
                                className="w-full justify-start text-sm"
                                onClick={() => router.visit('/bookings')}
                            >
                                <Calendar className="mr-2 h-4 w-4" />
                                Manage All Bookings
                            </Button>
                            <Button
                                variant="outline"
                                className="w-full justify-start text-sm"
                                onClick={() => router.visit('/admin/hostels')}
                            >
                                <Building2 className="mr-2 h-4 w-4" />
                                Manage All Hostels
                            </Button>
                            <Button
                                variant="outline"
                                className="w-full justify-start text-sm"
                                onClick={() => router.visit('/admin/users')}
                            >
                                <Users className="mr-2 h-4 w-4" />
                                Manage Users & Permissions
                            </Button>
                            <Button
                                variant="outline"
                                className="w-full justify-start text-sm"
                                onClick={() => router.visit('/admin/analytics')}
                            >
                                <BarChart3 className="mr-2 h-4 w-4" />
                                Analytics & Reports
                            </Button>
                            <Button
                                variant="outline"
                                className="w-full justify-start text-sm"
                                onClick={() => router.visit('/admin/settings')}
                            >
                                <Activity className="mr-2 h-4 w-4" />
                                System Configuration
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Platform Activity */}
                <div className="p-4 md:p-6 border rounded-lg bg-white">
                    <div className="mb-3 md:mb-4">
                        <h3 className="text-base md:text-lg font-semibold text-gray-900">Recent Platform Activity</h3>
                        <p className="text-xs md:text-sm text-gray-600">Latest system events and activities</p>
                    </div>
                    <div className="space-y-2 md:space-y-3">
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-2 md:space-x-3 min-w-0">
                                <CheckCircle className="h-4 w-4 md:h-5 md:w-5 text-green-600 flex-shrink-0" />
                                <span className="text-xs md:text-sm truncate">System backup completed successfully</span>
                            </div>
                            <span className="text-xs text-gray-500 flex-shrink-0 ml-2">2 hours ago</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-2 md:space-x-3 min-w-0">
                                <Users className="h-4 w-4 md:h-5 md:w-5 text-blue-600 flex-shrink-0" />
                                <span className="text-xs md:text-sm truncate">New hostel administrator registered</span>
                            </div>
                            <span className="text-xs text-gray-500 flex-shrink-0 ml-2">5 hours ago</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-2 md:space-x-3 min-w-0">
                                <Building2 className="h-4 w-4 md:h-5 md:w-5 text-purple-600 flex-shrink-0" />
                                <span className="text-xs md:text-sm truncate">New hostel "KNUST Unity Hall" added to platform</span>
                            </div>
                            <span className="text-xs text-gray-500 flex-shrink-0 ml-2">1 day ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
