import { type BreadcrumbItem, type SharedData } from '@/types';
import { Transition } from '@headlessui/react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { FormEventHandler } from 'react';

import DeleteUser from '@/components/delete-user';
import HeadingSmall from '@/components/heading-small';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';

interface ProfileForm {
    name: string;
    email: string;
    phone?: string;
    date_of_birth?: string;
    gender?: string;
    bio?: string;
    university?: string;
    student_id?: string;
    program?: string;
    year_of_study?: string;
    emergency_contact_name?: string;
    emergency_contact_phone?: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Settings', href: '/settings' },
    { title: 'Profile', href: '/settings/profile' },
];

const ghanaianUniversities = [
    'University of Ghana',
    'Kwame Nkrumah University of Science and Technology',
    'University of Cape Coast',
    'University for Development Studies',
    'University of Education, Winneba',
    'Ghana Institute of Management and Public Administration',
    'Ashesi University',
    'Central University',
    'Valley View University',
    'Presbyterian University',
    'Methodist University',
    'Wisconsin International University College',
    'Academic City University College',
    'Lancaster University Ghana',
    'University of Mines and Technology',
    'Ho Technical University',
    'Bolgatanga Technical University',
    'Tamale Technical University',
    'Other'
];

const studyPrograms = [
    'Computer Science',
    'Information Technology',
    'Business Administration',
    'Engineering',
    'Medicine',
    'Nursing',
    'Education',
    'Agriculture',
    'Law',
    'Economics',
    'Psychology',
    'Social Sciences',
    'Natural Sciences',
    'Arts and Humanities',
    'Architecture',
    'Pharmacy',
    'Dentistry',
    'Other'
];

export default function Profile({ mustVerifyEmail, status }: { mustVerifyEmail: boolean; status?: string }) {
    const { auth } = usePage<SharedData>().props;
    const isStudent = auth.user.role === 'student';

    const { data, setData, patch, errors, processing, recentlySuccessful } = useForm<Required<ProfileForm>>({
        name: auth.user.name || '',
        email: auth.user.email || '',
        phone: auth.user.phone || '',
        date_of_birth: auth.user.date_of_birth || '',
        gender: auth.user.gender || '',
        bio: auth.user.bio || '',
        university: auth.user.university || '',
        student_id: auth.user.student_id || '',
        program: auth.user.program || '',
        year_of_study: auth.user.year_of_study || '',
        emergency_contact_name: auth.user.emergency_contact_name || '',
        emergency_contact_phone: auth.user.emergency_contact_phone || '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        patch(route('profile.update'), {
            preserveScroll: true,
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Profile settings" />

            <SettingsLayout>
                <div className="space-y-6">
                    <HeadingSmall title="Profile information" description="Update your personal information and academic details" />

                    <form onSubmit={submit} className="space-y-6">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Basic Information</CardTitle>
                                <CardDescription>Your personal details</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 sm:grid-cols-2">
                                    <div>
                                        <Label htmlFor="name">Full Name</Label>
                                        <Input
                                            id="name"
                                            className="mt-1 block w-full"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            required
                                            autoComplete="name"
                                            placeholder="Full name"
                                        />
                                        <InputError className="mt-2" message={errors.name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="email">Email address</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            className="mt-1 block w-full"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            required
                                            autoComplete="username"
                                            placeholder="Email address"
                                        />
                                        <InputError className="mt-2" message={errors.email} />
                                    </div>

                                    <div>
                                        <Label htmlFor="phone">Phone Number</Label>
                                        <Input
                                            id="phone"
                                            type="tel"
                                            className="mt-1 block w-full"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="+233 XX XXX XXXX"
                                        />
                                        <InputError className="mt-2" message={errors.phone} />
                                    </div>

                                    <div>
                                        <Label htmlFor="date_of_birth">Date of Birth</Label>
                                        <Input
                                            id="date_of_birth"
                                            type="date"
                                            className="mt-1 block w-full"
                                            value={data.date_of_birth}
                                            onChange={(e) => setData('date_of_birth', e.target.value)}
                                        />
                                        <InputError className="mt-2" message={errors.date_of_birth} />
                                    </div>
                                </div>

                                <div className="grid gap-4 sm:grid-cols-2">
                                    <div>
                                        <Label htmlFor="gender">Gender</Label>
                                        <Select value={data.gender} onValueChange={(value) => setData('gender', value)}>
                                            <SelectTrigger className="mt-1">
                                                <SelectValue placeholder="Select your gender" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="male">Male</SelectItem>
                                                <SelectItem value="female">Female</SelectItem>
                                                <SelectItem value="other">Other</SelectItem>
                                                <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <InputError className="mt-2" message={errors.gender} />
                                    </div>

                                    <div>
                                        <Label htmlFor="bio">Bio</Label>
                                        <Input
                                            id="bio"
                                            className="mt-1 block w-full"
                                            value={data.bio}
                                            onChange={(e) => setData('bio', e.target.value)}
                                            placeholder="Tell us a bit about yourself"
                                        />
                                        <InputError className="mt-2" message={errors.bio} />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Student Information */}
                        {isStudent && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Academic Information</CardTitle>
                                    <CardDescription>Your university and study details</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="university">University</Label>
                                        <Select value={data.university} onValueChange={(value) => setData('university', value)}>
                                            <SelectTrigger className="mt-1">
                                                <SelectValue placeholder="Select your university" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {ghanaianUniversities.map((uni) => (
                                                    <SelectItem key={uni} value={uni}>
                                                        {uni}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <InputError className="mt-2" message={errors.university} />
                                    </div>

                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div>
                                            <Label htmlFor="student_id">Student ID</Label>
                                            <Input
                                                id="student_id"
                                                className="mt-1 block w-full"
                                                value={data.student_id}
                                                onChange={(e) => setData('student_id', e.target.value)}
                                                placeholder="20210001"
                                            />
                                            <InputError className="mt-2" message={errors.student_id} />
                                        </div>

                                        <div>
                                            <Label htmlFor="year_of_study">Year of Study</Label>
                                            <Select value={data.year_of_study} onValueChange={(value) => setData('year_of_study', value)}>
                                                <SelectTrigger className="mt-1">
                                                    <SelectValue placeholder="Select year" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="1">1st Year</SelectItem>
                                                    <SelectItem value="2">2nd Year</SelectItem>
                                                    <SelectItem value="3">3rd Year</SelectItem>
                                                    <SelectItem value="4">4th Year</SelectItem>
                                                    <SelectItem value="5">5th Year</SelectItem>
                                                    <SelectItem value="6">6th Year</SelectItem>
                                                    <SelectItem value="graduate">Graduate Student</SelectItem>
                                                    <SelectItem value="other">Other</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <InputError className="mt-2" message={errors.year_of_study} />
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="program">Program of Study</Label>
                                        <Select value={data.program} onValueChange={(value) => setData('program', value)}>
                                            <SelectTrigger className="mt-1">
                                                <SelectValue placeholder="Select your program" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {studyPrograms.map((program) => (
                                                    <SelectItem key={program} value={program}>
                                                        {program}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <InputError className="mt-2" message={errors.program} />
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Emergency Contact */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Emergency Contact</CardTitle>
                                <CardDescription>Person to contact in case of emergency</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 sm:grid-cols-2">
                                    <div>
                                        <Label htmlFor="emergency_contact_name">Contact Name</Label>
                                        <Input
                                            id="emergency_contact_name"
                                            className="mt-1 block w-full"
                                            value={data.emergency_contact_name}
                                            onChange={(e) => setData('emergency_contact_name', e.target.value)}
                                            placeholder="Parent/Guardian Name"
                                        />
                                        <InputError className="mt-2" message={errors.emergency_contact_name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="emergency_contact_phone">Contact Phone</Label>
                                        <Input
                                            id="emergency_contact_phone"
                                            type="tel"
                                            className="mt-1 block w-full"
                                            value={data.emergency_contact_phone}
                                            onChange={(e) => setData('emergency_contact_phone', e.target.value)}
                                            placeholder="+233 XX XXX XXXX"
                                        />
                                        <InputError className="mt-2" message={errors.emergency_contact_phone} />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <div className="flex items-center gap-4">
                            <Button type="submit" disabled={processing}>
                                Save Changes
                            </Button>

                            {recentlySuccessful && (
                                <p className="text-sm text-green-600">Saved.</p>
                            )}
                        </div>
                    </form>
                </div>

                <DeleteUser />
            </SettingsLayout>
        </AppLayout>
    );
}
