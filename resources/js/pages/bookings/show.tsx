import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    MapPin, Phone, Mail, Star, Building2, 
    Calendar, User, DollarSign, Bed, 
    Edit, X, CheckCircle, Clock, 
    CreditCard, GraduationCap
} from 'lucide-react';

interface User {
    id: string;
    name: string;
    email: string;
    phone?: string;
    role: string;
}

interface Hostel {
    id: string;
    name: string;
    description: string;
    address: string;
    city: string;
    state: string;
    phone: string;
    email: string;
    photos: string[];
    amenities: string[];
    owner: User;
    average_rating?: number;
    total_reviews?: number;
    payment_period: string;
}

interface RoomType {
    id: string;
    name: string;
    category: string;
    description: string;
    capacity: number;
    amenities?: string[];
}

interface Room {
    id: string;
    room_number: string;
    status: string;
}

interface Booking {
    id: string;
    booking_reference: string;
    user: User;
    hostel: Hostel;
    room_type: RoomType;
    room?: Room;
    semester: string;
    academic_year: string;
    students: number;
    price_per_semester: number;
    subtotal: number;
    charges: number;
    service_fee: number;
    total_amount: number;
    amount_paid: number;
    amount_due: number;
    payment_status: 'pending' | 'partially_paid' | 'paid' | 'failed' | 'refunded';
    status: 'pending' | 'confirmed' | 'moved_in' | 'active' | 'moved_out' | 'cancelled';
    payment_type: 'full' | 'deposit';
    payment_reference?: string;
    special_requests?: string;
    cancellation_reason?: string;
    created_at: string;
    confirmed_at?: string;
    checked_in_at?: string;
    checked_out_at?: string;
    cancelled_at?: string;
}

interface Props {
    booking: Booking;
    can_edit: boolean;
    can_cancel: boolean;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Bookings', href: '/bookings' },
    { title: 'Booking Details', href: '#' },
];

export default function BookingShow({ booking, can_edit, can_cancel }: Props) {
    const { auth } = usePage().props as any;
    const isOwner = auth.user.role === 'hostel_admin';
    const isAdmin = auth.user.role === 'admin';
    const isGuest = booking.user.id === auth.user.id;

    // Helper function to get payment period text
    const getPaymentPeriodText = () => {
        switch(booking.hostel.payment_period) {
            case 'month': return 'monthly';
            case 'semester': return 'semester';
            case 'year': return 'academic year';
            default: return 'period';
        }
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 0,
        }).format(price);
    };

    const formatDate = (date: string) => {
        return new Date(date).toLocaleDateString('en-GB', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatDateTime = (date: string) => {
        return new Date(date).toLocaleString('en-GB', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getSemesterLabel = (semester: string) => {
        // For monthly payment hostels, show different labels
        if (booking.hostel?.payment_period === 'month') {
            switch (semester) {
                case 'first': return 'September - December (4 months)';
                case 'second': return 'January - May (5 months)';
                case 'full_year': return 'Full Academic Year (9 months)';
                default: return semester;
            }
        }
        
        // For semester/year payment hostels, show traditional labels
        switch (semester) {
            case 'first': return 'First Semester';
            case 'second': return 'Second Semester';
            case 'full_year': return 'Full Academic Year';
            default: return semester;
        }
    };

    const getStatusBadge = (status: string) => {
        const statusStyles = {
            pending: 'bg-yellow-50 text-yellow-700 border-yellow-200',
            confirmed: 'bg-blue-50 text-blue-700 border-blue-200',
            moved_in: 'bg-green-50 text-green-700 border-green-200',
            active: 'bg-emerald-50 text-emerald-700 border-emerald-200',
            moved_out: 'bg-gray-50 text-gray-700 border-gray-200',
            cancelled: 'bg-red-50 text-red-700 border-red-200',
        };
        
        const style = statusStyles[status as keyof typeof statusStyles] || 'bg-gray-50 text-gray-700 border-gray-200';
        const label = status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
        
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${style}`}>
                {label}
            </span>
        );
    };

    const handleAction = async (action: string, data?: any) => {
        try {
            if (action === 'cancel') {
                router.post(`/bookings/${booking.id}/cancel`, data);
            } else if (action === 'confirm') {
                router.post(`/bookings/${booking.id}/confirm`);
            } else if (action === 'check-in') {
                router.post(`/bookings/${booking.id}/check-in`);
            } else if (action === 'check-out') {
                router.post(`/bookings/${booking.id}/check-out`);
            }
        } catch (error) {
            // Action failed
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Booking ${booking.booking_reference}`} />
            
            <div className="min-h-screen bg-white">
                <div className="container mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-3xl font-bold text-foreground">
                                    Booking {booking.booking_reference}
                                </h1>
                                <p className="mt-2 text-muted-foreground">
                                    Booking details and information
                                </p>
                            </div>
                            
                            <div className="flex items-center gap-3">
                                {getStatusBadge(booking.status)}
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Booking Details */}
                            <div className="p-6 space-y-4">
                                <h2 className="text-lg font-semibold flex items-center gap-2">
                                        <Calendar className="h-5 w-5" />
                                        Booking Information
                                </h2>
                                
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <span className="text-sm text-muted-foreground">
                                                {booking.hostel?.payment_period === 'month' ? 'Booking Period' : 'Academic Period'}
                                            </span>
                                            <div className="font-medium flex items-center gap-1 mt-1">
                                                <GraduationCap className="h-4 w-4 text-primary" />
                                                {getSemesterLabel(booking.semester)} {booking.academic_year}
                                            </div>
                                            {booking.hostel?.payment_period === 'month' && (
                                                <p className="text-xs text-muted-foreground mt-1">
                                                    Monthly payment hostel
                                                </p>
                                            )}
                                        </div>
                                        
                                        <div>
                                            <span className="text-sm text-muted-foreground">Students</span>
                                            <div className="font-medium mt-1">{booking.students} {booking.students === 1 ? 'student' : 'students'}</div>
                                        </div>

                                        {booking.room && (
                                            <div>
                                                <span className="text-sm text-muted-foreground">Room Selected</span>
                                                <div className="font-medium mt-1">{booking.room.room_number || 'N/A'}</div>
                                            </div>
                                        )}

                                        <div>
                                            <span className="text-sm text-muted-foreground">Booking Date</span>
                                            <div className="font-medium mt-1">{formatDate(booking.created_at)}</div>
                                        </div>
                                        
                                        <div>
                                            <span className="text-sm text-muted-foreground">Payment Type</span>
                                            <div className="font-medium mt-1 capitalize">{booking.payment_type} Payment</div>
                                        </div>
                                    </div>
                                    
                                    {booking.special_requests && (
                                        <div className="mt-6">
                                            <span className="text-sm text-muted-foreground">Special Requests</span>
                                            <div className="mt-1 p-3 bg-gray-50 rounded-lg text-sm">
                                                {booking.special_requests}
                                            </div>
                                        </div>
                                    )}
                            </div>

                            {/* Guest Information (for owners/admins) */}
                            {(isOwner || isAdmin) && (
                                <div className="p-6 space-y-4">
                                    <h2 className="text-lg font-semibold flex items-center gap-2">
                                            <User className="h-5 w-5" />
                                            Student Information
                                    </h2>
                                    
                                        <div className="space-y-3">
                                            <div>
                                                <span className="text-sm text-muted-foreground">Name</span>
                                                <div className="font-medium">{booking.user.name}</div>
                                            </div>
                                            <div>
                                                <span className="text-sm text-muted-foreground">Email</span>
                                                <div className="font-medium">{booking.user.email}</div>
                                            </div>
                                            {booking.user.phone && (
                                                <div>
                                                    <span className="text-sm text-muted-foreground">Phone</span>
                                                    <div className="font-medium">{booking.user.phone}</div>
                                                </div>
                                            )}
                                        </div>
                                </div>
                            )}
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Payment Summary */}
                            <div className="p-6 space-y-4">
                                <h2 className="text-lg font-semibold flex items-center gap-2">
                                        <DollarSign className="h-5 w-5" />
                                        Payment Summary
                                </h2>
                                
                                    <div className="space-y-3">
                                        <div className="flex justify-between text-sm">
                                            <span>Base Price ({getPaymentPeriodText()})</span>
                                            <span>{formatPrice(booking.price_per_semester || 0)}</span>
                                        </div>
                                        
                                        <div className="flex justify-between text-sm">
                                            <span>Students × {booking.students}</span>
                                            <span>{formatPrice(booking.subtotal || 0)}</span>
                                        </div>
                                        
                                        <div className="flex justify-between text-sm">
                                            <span>Charges</span>
                                            <span>{formatPrice(booking.charges || 0)}</span>
                                        </div>
                                        
                                        <div className="flex justify-between text-sm">
                                            <span>Service fee</span>
                                            <span>{formatPrice(booking.service_fee || 0)}</span>
                                        </div>
                                        
                                        <Separator />
                                        
                                        <div className="flex justify-between font-semibold">
                                            <span>Total Amount</span>
                                            <span>{formatPrice(booking.total_amount || 0)}</span>
                                        </div>
                                        
                                        <div className="flex justify-between text-green-600">
                                            <span>Amount Paid</span>
                                            <span>{formatPrice(booking.amount_paid || 0)}</span>
                                        </div>
                                        
                                        {(booking.amount_due || 0) > 0 && (
                                            <div className="flex justify-between text-orange-600 font-medium">
                                                <span>Outstanding</span>
                                                <span>{formatPrice(booking.amount_due || 0)}</span>
                                            </div>
                                        )}
                                        
                                        {booking.payment_reference && (
                                            <div className="text-xs text-muted-foreground pt-2 border-t">
                                                Payment Ref: {booking.payment_reference}
                                            </div>
                                        )}
                                    </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="p-6 space-y-4">
                                <h2 className="text-lg font-semibold">Actions</h2>
                                
                                    <div className="space-y-3">
                                        {/* Payment Action */}
                                    {isGuest && ['pending', 'failed'].includes(booking.payment_status) && booking.status === 'pending' && (
                                        <Button
                                            onClick={() => router.visit(`/bookings/${booking.id}/payment`)}
                                            className="w-full rounded-full"
                                        >
                                            <CreditCard className="mr-2 h-4 w-4" />
                                            {booking.payment_status === 'failed' ? 'Retry Payment' : 'Complete Payment'} ({formatPrice((booking.total_amount || 0) - (booking.amount_paid || 0))})
                                        </Button>
                                    )}
                                    
                                    {/* Partial Payment Action */}
                                    {isGuest && booking.payment_status === 'partially_paid' && booking.status === 'pending' && ((booking.total_amount || 0) - (booking.amount_paid || 0)) > 0 && (
                                            <Button
                                                onClick={() => router.visit(`/bookings/${booking.id}/payment`)}
                                                className="w-full rounded-full"
                                            >
                                                <CreditCard className="mr-2 h-4 w-4" />
                                            Pay Remaining Balance ({formatPrice((booking.total_amount || 0) - (booking.amount_paid || 0))})
                                            </Button>
                                        )}
                                        
                        {/* Edit Button */}
                        {can_edit && (
                            <Button
                                variant="outline"
                                onClick={() => router.visit(`/bookings/${booking.id}/edit`)}
                                className="w-full rounded-full"
                            >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Booking
                            </Button>
                        )}
                                        
                         {/* Cancel/Delete Button */}
                        {can_cancel && (booking.status === 'pending' || booking.status === 'confirmed') && booking.payment_status !== 'paid' && (
                            <Button
                                variant="destructive"
                                onClick={() => {
                                    const reason = prompt('Please provide a reason for cancellation:');
                                    if (reason) {
                                        handleAction('cancel', { cancellation_reason: reason });
                                    }
                                }}
                                className="w-full rounded-full"
                            >
                                <X className="mr-2 h-4 w-4" />
                                Cancel Booking
                            </Button>
                        )}
                        
                        {/* Paid Booking Notice */}
                        {booking.payment_status === 'paid' && (booking.status === 'pending' || booking.status === 'confirmed') && (
                            <div className="text-center py-4 text-muted-foreground">
                                <p className="text-sm">This booking cannot be cancelled as payment has been completed.</p>
                                <p className="text-xs mt-1">Contact support for assistance with paid bookings.</p>
                            </div>
                        )}
                                        
                                        {/* Admin Actions */}
                                        {(isOwner || isAdmin) && (
                                            <>
                                                {booking.status === 'pending' && booking.payment_status === 'paid' && (
                                                    <Button
                                                        onClick={() => handleAction('confirm')}
                                                        className="w-full rounded-full"
                                                    >
                                                        <CheckCircle className="mr-2 h-4 w-4" />
                                                        Confirm Booking
                                                    </Button>
                                                )}
                                                
                                                {booking.status === 'confirmed' && (
                                                    <Button
                                                        onClick={() => handleAction('check-in')}
                                                        className="w-full rounded-full"
                                                    >
                                                        <CheckCircle className="mr-2 h-4 w-4" />
                                                        Check In Student
                                                    </Button>
                                                )}
                                                
                                                {booking.status === 'moved_in' && (
                                                    <Button
                                                        onClick={() => handleAction('check-out')}
                                                        className="w-full rounded-full"
                                                    >
                                                        <CheckCircle className="mr-2 h-4 w-4" />
                                                        Check Out Student
                                                    </Button>
                                                )}
                                            </>
                                        )}
                                        
                                        {/* No Actions Available */}
                                        {booking.status === 'cancelled' && (
                                            <div className="text-center py-4 text-muted-foreground">
                                                <p>This booking has been cancelled</p>
                                                {booking.cancellation_reason && (
                                                    <p className="text-sm mt-1">Reason: {booking.cancellation_reason}</p>
                                                )}
                                            </div>
                                        )}
                                        
                                        {booking.status === 'moved_out' && (
                                            <div className="text-center py-4 text-muted-foreground">
                                                <p>This booking has been completed</p>
                                            </div>
                                        )}
                                    </div>
                            </div>

                            {/* Hostel Information */}
                            <div className="p-6 space-y-4">
                                    {booking.hostel.photos && booking.hostel.photos.length > 0 ? (
                                        <img
                                            src={booking.hostel.photos[0]}
                                            alt={booking.hostel.name}
                                            className="aspect-[4/3] w-full rounded-lg object-cover"
                                        />
                                    ) : (
                                        <div className="aspect-[4/3] flex items-center justify-center bg-muted rounded-lg">
                                            <Building2 className="h-12 w-12 text-muted-foreground" />
                                        </div>
                                    )}
                                    
                                <div>
                                        <h3 className="text-lg font-semibold">{booking.hostel.name}</h3>
                                        <div className="mt-1 flex items-center gap-1 text-sm text-muted-foreground">
                                            <MapPin className="h-4 w-4" />
                                            <span>{booking.hostel.address}</span>
                                        </div>
                                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                            <span>{booking.hostel.city}, {booking.hostel.state}</span>
                                        </div>
                                        
                                        {booking.hostel.average_rating && booking.hostel.average_rating > 0 && (
                                            <div className="mt-2 flex items-center gap-1">
                                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                                <span className="text-sm font-medium">
                                                    {booking.hostel.average_rating.toFixed(1)}
                                                </span>
                                                <span className="text-sm text-muted-foreground">
                                                    ({booking.hostel.total_reviews || 0} reviews)
                                                </span>
                                            </div>
                                        )}

                                        <div className="mt-4 space-y-2">
                                            <div className="flex items-center gap-2 text-sm">
                                                <Phone className="h-4 w-4 text-muted-foreground" />
                                                <span>{booking.hostel.phone}</span>
                                            </div>
                                            <div className="flex items-center gap-2 text-sm">
                                                <Mail className="h-4 w-4 text-muted-foreground" />
                                                <span>{booking.hostel.email}</span>
                                            </div>
                                        </div>

                                        <Button
                                            variant="outline"
                                            className="w-full mt-4 rounded-full"
                                            onClick={() => router.visit(`/hostels/${booking.hostel.id}`)}
                                        >
                                            View Hostel Details
                                        </Button>
                                    </div>
                            </div>

                            {/* Room Information */}
                            <div className="p-6 space-y-4">
                                <h2 className="text-lg font-semibold flex items-center gap-2">
                                        <Bed className="h-5 w-5" />
                                        Room Details
                                </h2>
                                
                                    <div className="space-y-3">
                                        <div>
                                            <span className="text-sm text-muted-foreground">Room Type</span>
                                            <div className="font-medium">{booking.room_type.name}</div>
                                            <div className="text-sm text-muted-foreground">{booking.room_type.description}</div>
                                        </div>
                                        
                                        {booking.room && (
                                            <div>
                                                <span className="text-sm text-muted-foreground">Room Number</span>
                                                <div className="font-medium">{booking.room.room_number}</div>
                                            </div>
                                        )}
                                        
                                        <div>
                                            <span className="text-sm text-muted-foreground">Capacity</span>
                                            <div className="font-medium">Up to {booking.room_type.capacity} students</div>
                                        </div>

                                        {booking.room_type.amenities && booking.room_type.amenities.length > 0 && (
                                            <div>
                                                <span className="text-sm text-muted-foreground">Room Amenities</span>
                                                <div className="mt-1 flex flex-wrap gap-1">
                                                    {booking.room_type.amenities.slice(0, 3).map((amenity, index) => (
                                                        <Badge key={index} variant="outline" className="text-xs">
                                                            {amenity}
                                                        </Badge>
                                                    ))}
                                                    {booking.room_type.amenities.length > 3 && (
                                                        <Badge variant="outline" className="text-xs">
                                                            +{booking.room_type.amenities.length - 3} more
                                                        </Badge>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}