import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm, router } from '@inertiajs/react';
import { Calendar, GraduationCap, Save, ArrowLeft } from 'lucide-react';

interface User {
    id: string;
    name: string;
    email: string;
    phone?: string;
    role: string;
}

interface Hostel {
    id: string;
    name: string;
    city: string;
    state: string;
    payment_period?: string;
}

interface RoomType {
    id: string;
    name: string;
    category: string;
    description: string;
    capacity: number;
    price_per_semester: number;
}

interface Room {
    id: string;
    room_number: string;
    status: string;
}

interface Booking {
    id: string;
    booking_reference: string;
    user: User;
    hostel: Hostel;
    room_type: RoomType;
    room?: Room;
    semester: string;
    academic_year: string;
    students: number;
    special_requests?: string;
    status: string;
    payment_status: string;
}

interface Props {
    booking: Booking;
    available_rooms: Room[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Bookings', href: '/bookings' },
    { title: 'Edit Booking', href: '#' },
];

export default function BookingEdit({ booking, available_rooms }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        semester: booking.semester,
        academic_year: booking.academic_year,
        students: booking.students,
        room_id: booking.room?.id || '',
        special_requests: booking.special_requests || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/bookings/${booking.id}`, {
            onSuccess: () => {
                // Will redirect to show page via controller
            }
        });
    };

    const getSemesterLabel = (semester: string) => {
        // For monthly payment hostels, show different labels
        if (booking.hostel?.payment_period === 'month') {
            switch (semester) {
                case 'first': return 'September - December (4 months)';
                case 'second': return 'January - May (5 months)';
                case 'full_year': return 'Full Academic Year (9 months)';
                default: return semester;
            }
        }
        
        // For semester/year payment hostels, show traditional labels
        switch (semester) {
            case 'first': return 'First Semester';
            case 'second': return 'Second Semester';
            case 'full_year': return 'Full Academic Year';
            default: return semester;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Booking ${booking.booking_reference}`} />
            
            <div className="min-h-screen bg-white">
                <div className="container mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center gap-4">
                            <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => router.visit(`/bookings/${booking.id}`)}
                                className="rounded-full"
                            >
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                            <div>
                                <h1 className="text-3xl font-bold text-foreground">
                                    Edit Booking {booking.booking_reference}
                                </h1>
                                <p className="mt-2 text-muted-foreground">
                                    Update your booking details
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Edit Form */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Calendar className="h-5 w-5" />
                                        Booking Details
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            {/* Semester */}
                                            <div>
                                                <Label htmlFor="semester">
                                                    {booking.hostel?.payment_period === 'month' ? 'Period' : 'Semester'}
                                                </Label>
                                                <Select value={data.semester} onValueChange={(value) => setData('semester', value)}>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder={`Select ${booking.hostel?.payment_period === 'month' ? 'period' : 'semester'}`} />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="first">
                                                            {booking.hostel?.payment_period === 'month' 
                                                                ? 'September - December (4 months)' 
                                                                : 'First Semester'
                                                            }
                                                        </SelectItem>
                                                        <SelectItem value="second">
                                                            {booking.hostel?.payment_period === 'month' 
                                                                ? 'January - May (5 months)' 
                                                                : 'Second Semester'
                                                            }
                                                        </SelectItem>
                                                        <SelectItem value="full_year">
                                                            {booking.hostel?.payment_period === 'month' 
                                                                ? 'Full Academic Year (9 months)' 
                                                                : 'Full Academic Year'
                                                            }
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors.semester && <div className="text-red-600 text-sm mt-1">{errors.semester}</div>}
                                            </div>

                                            {/* Academic Year */}
                                            <div>
                                                <Label htmlFor="academic_year">Academic Year</Label>
                                                <Input
                                                    id="academic_year"
                                                    type="text"
                                                    value={data.academic_year}
                                                    onChange={(e) => setData('academic_year', e.target.value)}
                                                    placeholder="e.g., 2024/2025"
                                                />
                                                {errors.academic_year && <div className="text-red-600 text-sm mt-1">{errors.academic_year}</div>}
                                            </div>

                                            {/* Number of Students */}
                                            <div>
                                                <Label htmlFor="students">Number of Students</Label>
                                                <Input
                                                    id="students"
                                                    type="number"
                                                    min="1"
                                                    max={booking.room_type.capacity}
                                                    value={data.students}
                                                    onChange={(e) => setData('students', parseInt(e.target.value))}
                                                />
                                                <div className="text-sm text-muted-foreground mt-1">
                                                    Maximum {booking.room_type.capacity} students for this room type
                                                </div>
                                                {errors.students && <div className="text-red-600 text-sm mt-1">{errors.students}</div>}
                                            </div>

                                            {/* Room Assignment */}
                                            <div>
                                                <Label htmlFor="room_id">Room Assignment</Label>
                                                <Select value={data.room_id} onValueChange={(value) => setData('room_id', value)}>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select a room" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="">No specific room preference</SelectItem>
                                                        {available_rooms.map((room) => (
                                                            <SelectItem key={room.id} value={room.id}>
                                                                Room {room.room_number}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {errors.room_id && <div className="text-red-600 text-sm mt-1">{errors.room_id}</div>}
                                            </div>
                                        </div>

                                        {/* Special Requests */}
                                        <div>
                                            <Label htmlFor="special_requests">Special Requests</Label>
                                            <Textarea
                                                id="special_requests"
                                                value={data.special_requests}
                                                onChange={(e) => setData('special_requests', e.target.value)}
                                                placeholder="Any special requirements or requests..."
                                                rows={4}
                                            />
                                            {errors.special_requests && <div className="text-red-600 text-sm mt-1">{errors.special_requests}</div>}
                                        </div>

                                        {/* Submit Button */}
                                        <div className="flex gap-3">
                                            <Button
                                                type="submit"
                                                disabled={processing}
                                                className="rounded-full"
                                            >
                                                <Save className="mr-2 h-4 w-4" />
                                                {processing ? 'Saving...' : 'Save Changes'}
                                            </Button>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={() => router.visit(`/bookings/${booking.id}`)}
                                                className="rounded-full"
                                            >
                                                Cancel
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar Info */}
                        <div className="space-y-6">
                            {/* Booking Info */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Booking Information</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div>
                                            <span className="text-sm text-muted-foreground">Reference</span>
                                            <div className="font-medium">{booking.booking_reference}</div>
                                        </div>
                                        <div>
                                            <span className="text-sm text-muted-foreground">Status</span>
                                            <div className="font-medium capitalize">{booking.status.replace('_', ' ')}</div>
                                        </div>
                                        <div>
                                            <span className="text-sm text-muted-foreground">Payment Status</span>
                                            <div className="font-medium capitalize">{booking.payment_status.replace('_', ' ')}</div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Hostel Info */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Hostel Details</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div>
                                            <span className="text-sm text-muted-foreground">Name</span>
                                            <div className="font-medium">{booking.hostel.name}</div>
                                        </div>
                                        <div>
                                            <span className="text-sm text-muted-foreground">Location</span>
                                            <div className="font-medium">{booking.hostel.city}, {booking.hostel.state}</div>
                                        </div>
                                        <div>
                                            <span className="text-sm text-muted-foreground">Room Type</span>
                                            <div className="font-medium">{booking.room_type.name}</div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Important Notes */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Important Notes</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <ul className="text-sm text-muted-foreground space-y-2">
                                        <li>• Changes to semester or academic year may affect pricing</li>
                                        <li>• Room assignments are subject to availability</li>
                                        <li>• Confirmed bookings may require approval for changes</li>
                                        <li>• Contact support if you need help with your booking</li>
                                    </ul>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
} 