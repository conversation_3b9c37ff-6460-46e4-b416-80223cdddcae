import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import OptimizedImage from '@/components/OptimizedImage';
import { Head, useForm, router, usePage } from '@inertiajs/react';
import {
    Calendar, Users, MapPin, Star, Building2, Bed,
    Wifi, Car, Coffee, ShoppingBag, Utensils, Tv,
    CreditCard, CheckCircle, AlertCircle, Info, GraduationCap,
    ArrowLeft, Check, ArrowUp, ArrowDown, ArrowLeft as ArrowLeftIcon, ArrowRight, Home
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { Textarea } from '@/components/ui/textarea';
import axios from 'axios';
import { type BreadcrumbItem } from '@/types';

interface Room {
    id: string;
    room_number: string;
    floor: number;
    is_available: boolean;
    gender_allocated: 'male' | 'female' | 'mixed';
}
interface RoomType {
    id: string;
    name: string;
    category: string;
    description: string;
    capacity: number;
    total_beds: number;
    available_beds: number;
    amenities: string[];
    male_rooms: number;
    female_rooms: number;
    mixed_rooms: number;
    gender_type: string;
    available_rooms: number;
}

interface Hostel {
    id: string;
    name: string;
    description: string;
    address: string;
    city: string;
    state: string;
    photos: string[];
    amenities: string[];
    average_rating: number;
    total_reviews: number;
    university_distance: number;
    activeRoomTypes: RoomType[];
    is_per_semester: boolean;
    allow_deposit: boolean;
    payment_period: string;
    base_price: number;
    service_fee: number;
    deposit_percentage: number;
    cancellation_fee: number;
}

interface BookingData {
    hostel_id?: string;
    room_type_id?: string;
    academic_year?: string;
    semester?: string;
    students?: number;
}

interface AcademicYear {
    id: number;
    name: string;
    display_name: string;
    is_active: boolean;
    is_current: boolean;
}

interface Props {
    hostel?: Hostel;
    room_type?: RoomType;
    booking_data?: BookingData;
    academicYears?: AcademicYear[];
}

export default function BookingCreate({ hostel, room_type, booking_data, academicYears }: Props) {
    const { auth } = usePage().props as any;
    const user = auth?.user;
    const [selectedRoomType, setSelectedRoomType] = useState<RoomType | null>(room_type || null);
    const [pricing, setPricing] = useState({
        subtotal: 0,
        charges: 0,
        serviceFee: 0,
        total: 0,
        amountDueNow: 0,
        amountDueLater: 0,
    });

    const { data, setData, post, transform, processing, errors } = useForm({
        hostel_id: booking_data?.hostel_id || hostel?.id || '',
        room_type_id: booking_data?.room_type_id || room_type?.id || '',
        academic_year: booking_data?.academic_year || getCurrentAcademicYear(),
        semester: booking_data?.semester || getCurrentSemester(),
        students: booking_data?.students || 1,
        payment_type: 'full' as 'full' | 'deposit',
        special_requests: '',
    });

    transform((data) => ({
        ...data,
        semester: data.semester === 'both' ? 'full_year' : data.semester,
    }));

    // Calculate pricing when room type, semester, or payment type changes
    useEffect(() => {
        if (selectedRoomType && hostel) {
            if (!hostel.base_price) {
                setPricing({ 
                    subtotal: 0, 
                    charges: 0, 
                    serviceFee: 0, 
                    total: 0, 
                    amountDueNow: 0, 
                    amountDueLater: 0 
                });
                return;
            }

            const basePrice = hostel.base_price;
            
            // Apply pricing based on payment period and semester selection
            let subtotal = 0;
            if (hostel.payment_period === 'month') {
                // For monthly hostels, calculate based on semester length
                const monthsInSemester = data.semester === 'both' ? 9 : 4.5;
                subtotal = basePrice * monthsInSemester * data.students;
            } else if (hostel.payment_period === 'semester') {
                // Semester-based pricing
                const semesterMultiplier = data.semester === 'both' ? 1.8 : 1; // Slight discount for full year
                subtotal = basePrice * semesterMultiplier * data.students;
            } else { // year
                // Academic year pricing - no additional multiplier needed
                subtotal = basePrice * data.students;
            }
            
            const charges = subtotal * 0.03; // 3% charges
            const serviceFee = hostel.service_fee || 0;
            const total = subtotal + charges + serviceFee;
            
            // Calculate amounts based on payment type
            let amountDueNow = total;
            let amountDueLater = 0;
            
            if (data.payment_type === 'deposit' && hostel.allow_deposit) {
                const depositPercentage = hostel.deposit_percentage || 30;
                amountDueNow = total * (depositPercentage / 100);
                amountDueLater = total - amountDueNow;
            }
            
            setPricing({ 
                subtotal, 
                charges, 
                serviceFee, 
                total, 
                amountDueNow, 
                amountDueLater 
            });
        } else {
            setPricing({ 
                subtotal: 0, 
                charges: 0, 
                serviceFee: 0, 
                total: 0, 
                amountDueNow: 0, 
                amountDueLater: 0 
            });
        }
    }, [selectedRoomType, data.semester, data.payment_type, data.students, hostel]);

    // Update selected room type when form data changes
    useEffect(() => {
        if (data.room_type_id && hostel?.activeRoomTypes) {
            const roomType = hostel.activeRoomTypes.find(rt => rt.id === data.room_type_id);
            setSelectedRoomType(roomType || null);
        }
    }, [data.room_type_id, hostel]);

    // Auto-set semester based on hostel's payment period
    useEffect(() => {
        if (hostel?.payment_period === 'year' && data.semester !== 'both') {
            setData('semester', 'both');
        } else if (hostel?.payment_period === 'month') {
            // For monthly hostels, default to current semester or first if not set
            if (!data.semester || data.semester === 'both') {
                setData('semester', getCurrentSemester());
            }
        }
    }, [hostel?.payment_period, data.semester]);

    // Ensure students is always set to 1 (one booking per student)
    useEffect(() => {
        if (data.students !== 1) {
            setData('students', 1);
        }
    }, [data.students, setData]);

    function getCurrentAcademicYear() {
        const now = new Date();
        const year = now.getFullYear();
        
        // Academic year runs from September to August
        if (now.getMonth() >= 8) {
            return `${year}/${year + 1}`;
        } else {
            return `${year - 1}/${year}`;
        }
    }

    function getCurrentSemester() {
        const now = new Date();
        const month = now.getMonth() + 1;
        
        // First semester: September - December
        if (month >= 9 || month <= 12) {
            return 'first';
        }
        // Second semester: January - May
        else if (month >= 1 && month <= 5) {
            return 'second';
        }
        // Vacation period: June - August
        return 'first';
    }

    function getSemesterLabel(semester: string) {
        if (!hostel) return semester;
        
        // For monthly payment hostels, show different labels
        if (hostel.payment_period === 'month') {
            switch (semester) {
                case 'first': return 'September - December (4 months)';
                case 'second': return 'January - May (5 months)';
                case 'both': return 'Full Academic Year (9 months)';
                default: return semester;
            }
        }
        
        // For semester/year payment hostels, show traditional labels
        switch (semester) {
            case 'first': return 'First Semester';
            case 'second': return 'Second Semester';
            case 'both': return 'Full Academic Year';
            default: return semester;
        }
    }

    function formatPrice(price: number) {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 0,
        }).format(price);
    }

    function submit(e: React.FormEvent) {
        e.preventDefault();

        // Validate required fields
        if (!data.hostel_id) {
            return;
        }
        
        if (!data.room_type_id) {
            return;
        }
        
        // Use route helper with fallback
        const postUrl = typeof route !== 'undefined' ? route('bookings.store') : '/bookings';
        
        post(postUrl, {
            onSuccess: (page) => {
                // Form submission successful - user will be redirected
            },
        });
    }

    const getAmenityIcon = (amenity: string) => {
        const amenityData = commonAmenities.find(a => a.key === amenity || a.label.toLowerCase() === amenity.toLowerCase());
        return amenityData?.icon || Coffee;
    };

    const commonAmenities = [
        { key: 'wifi', label: 'WiFi', icon: Wifi },
        { key: 'parking', label: 'Parking', icon: Car },
        { key: 'breakfast', label: 'Breakfast', icon: Coffee },
        { key: 'air_conditioning', label: 'AC', icon: Coffee },
        { key: 'kitchen', label: 'Kitchen', icon: Utensils },
        { key: 'laundry', label: 'Laundry', icon: ShoppingBag },
        { key: 'tv', label: 'TV', icon: Tv },
        { key: 'study_room', label: 'Study Room', icon: Coffee },
        { key: 'security', label: 'Security', icon: Coffee },
        { key: 'water_supply', label: 'Water Supply', icon: Coffee },
    ];

    // Breadcrumbs for navigation context
    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Bookings', href: '/bookings' },
        { title: hostel?.name || 'Booking', href: '#' },
        { title: 'Create', href: '#' },
    ];

    // Helper functions for pricing display
    const getPaymentPeriodText = () => {
        if (!hostel) return 'period';
        switch(hostel.payment_period) {
            case 'month': return 'month';
            case 'semester': return 'semester';
            case 'year': return 'academic year';
            default: return 'period';
        }
    };

    const getFormattedBasePrice = () => {
        if (!hostel?.base_price) return 'Price not set';
        return `GH₵${hostel.base_price.toLocaleString()} per ${getPaymentPeriodText()}`;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Book Student Accommodation" />
            
            <div className="bg-white">
                <div className="container mx-auto px-4 py-8">
                {/* Back to search button */}
                <div className="mb-8">
                    <Button
                        type="button"
                        variant="ghost"
                        onClick={() => router.visit('/search')}
                        className="mb-4 text-muted-foreground hover:text-foreground"
                    >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Search
                    </Button>
                </div>

                {/* Hostel Selection Warning */}
                {!hostel && (
                    <div className="mb-8 rounded-lg border border-orange-200 bg-orange-50 p-4 text-center">
                        <Info className="mx-auto mb-2 h-6 w-6 text-orange-600" />
                        <p className="text-orange-800">Please select a hostel from the search page to continue with your booking.</p>
                    </div>
                )}

                {/* Hostel Summary */}
                {hostel && (
                    <div className="mb-12 rounded-xl p-8">
                        <div className="flex flex-col gap-6 md:flex-row md:items-start">
                            <div className="h-32 w-full flex-shrink-0 overflow-hidden rounded-xl md:h-28 md:w-28">
                                {hostel?.photos && hostel.photos.length > 0 ? (
                                    <OptimizedImage
                                        photo={hostel.photos[0]}
                                        alt={hostel.name}
                                        size="thumbnail"
                                        className="h-full w-full object-cover"
                                    />
                                ) : (
                                    <div className="flex h-full w-full items-center justify-center bg-gray-100">
                                        <Building2 className="h-10 w-10 text-gray-400" />
                                    </div>
                                )}
                            </div>
                            <div className="flex-1 space-y-3">
                                <h3 className="text-2xl font-bold text-gray-900">{hostel.name}</h3>
                                <div className="flex flex-wrap items-center gap-x-6 gap-y-2">
                                    <div className="flex items-center gap-2">
                                        <MapPin className="h-5 w-5 text-primary" />
                                        <span className="font-medium">{hostel.city}, {hostel.state}</span>
                                    </div>
                                </div>
                                {hostel.description && (
                                    <p className="text-gray-600 leading-relaxed">{hostel.description}</p>
                                )}
                            </div>
                        </div>
                    </div>
                )}

                <form className="space-y-12" onSubmit={submit}>
                    {/* Prominent Error Display for Critical Validation Issues */}
                    {(errors.semester || errors.students) && (
                        <div className="mb-8 rounded-lg border-2 border-red-200 bg-red-50 p-6">
                            <div className="flex items-start gap-3">
                                <AlertCircle className="h-6 w-6 text-red-600 flex-shrink-0 mt-0.5" />
                                <div className="space-y-3">
                                    {errors.semester && (
                                        <div>
                                            <h3 className="text-lg font-semibold text-red-800 mb-2">Booking Conflict</h3>
                                            <p className="text-red-700">{errors.semester}</p>
                                            <p className="text-red-600 text-sm mt-2">
                                                Please check your existing bookings or select a different semester/academic year.
                                            </p>
                                        </div>
                                    )}
                                    {errors.students && (
                                        <div>
                                            <h3 className="text-lg font-semibold text-red-800 mb-2">Room Capacity Exceeded</h3>
                                            <p className="text-red-700">{errors.students}</p>
                                            <p className="text-red-600 text-sm mt-2">
                                                Please select a room type with higher capacity or reduce the number of students.
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        
                    )}
                  
                    <div className="space-y-6">
                        <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                    <span className="text-primary font-semibold text-sm">1</span>
                                </div>
                                <h2 className="text-xl font-semibold">Academic Period</h2>
                            </div>
                            <p className="text-muted-foreground">
                                {hostel?.payment_period === 'year' 
                                    ? 'Select your academic year (full year booking)' 
                                    : hostel?.payment_period === 'month'
                                    ? 'Select your academic year (booking period determined by current semester)'
                                    : 'Select your academic year and semester'
                                }
                            </p>
                            
                            {/* Show current booking period info for monthly hostels */}
                            {hostel?.payment_period === 'month' && (
                                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                    <p className="text-sm text-blue-800">
                                        <strong>Booking Period:</strong> {getSemesterLabel(data.semester)}
                                    </p>
                                    <p className="text-xs text-blue-600 mt-1">
                                        This hostel charges monthly. The period is automatically determined based on the current semester.
                                    </p>
                                </div>
                            )}
                        </div>

                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <Label htmlFor="academic_year" className="text-sm font-medium">Academic Year</Label>
                                <div className="relative">
                                    <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                    <Select value={data.academic_year} onValueChange={(value) => setData('academic_year', value)}>
                                        <SelectTrigger className="h-11 pl-10">
                                            <SelectValue placeholder="Select academic year" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {academicYears?.map((academicYear) => (
                                                <SelectItem key={academicYear.id} value={academicYear.name}>
                                                    {academicYear.display_name}
                                                </SelectItem>
                                            )) || (
                                                <>
                                                    <SelectItem value="2024/2025">2024/2025 Academic Year</SelectItem>
                                                    <SelectItem value="2025/2026">2025/2026 Academic Year</SelectItem>
                                                </>
                                            )}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Room Selection */}
                    {hostel && (
                        <div className="space-y-6">
                            <div className="text-center">
                                <div className="flex items-center justify-center gap-2 mb-2">
                                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                        <span className="text-primary font-semibold text-sm">2</span>
                                    </div>
                                    <h2 className="text-xl font-semibold">Room Selection</h2>
                                </div>
                                <p className="text-muted-foreground">Choose your preferred room type and occupancy</p>
                            </div>

                            <div className="grid gap-6 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="room_type_id" className="text-sm font-medium">Room Type</Label>
                                    
                                    {room_type ? (
                                        /* Show pre-selected room type (read-only) */
                                        <div className="h-11 flex items-center justify-between px-3 py-2 border border-green-200 bg-green-50 rounded-md">
                                            <div className="flex items-center gap-2">
                                                <Check className="h-4 w-4 text-green-600" />
                                                <span className="font-medium text-green-800">{room_type.name}</span>
                                                <span className="text-sm text-green-600">
                                                    
                                                </span>
                                                <span className="text-xs text-green-700 ml-2">
                                                    Total: {formatPrice((hostel?.base_price || 0) * data.students)} for {data.students} {data.students === 1 ? 'student' : 'students'}
                                                </span>
                                            </div>
                                            <Badge variant="outline" className="border-green-300 text-green-700">
                                                Selected
                                            </Badge>
                                        </div>
                                    ) : (
                                        /* Show dropdown when no room type pre-selected */
                                        <Select value={data.room_type_id} onValueChange={(value) => setData('room_type_id', value)}>
                                            <SelectTrigger className="h-11">
                                                <SelectValue placeholder="Select a room type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {hostel?.activeRoomTypes && hostel.activeRoomTypes.length > 0 ? (
                                                    hostel.activeRoomTypes.filter(rt => {
                                                        const userGender = user?.gender;
                                                        const roomGenderType = rt.gender_type;
                                                        return roomGenderType === 'mixed' || roomGenderType === userGender;
                                                    }).map((roomType) => (
                                                        <SelectItem key={roomType.id} value={roomType.id}>
                                                            <div className="flex items-center justify-between w-full">
                                                                <div>
                                                                    <span className="font-medium">{roomType.name}</span>
                                                                    <span className="ml-2 text-sm text-gray-600">
                                                                        ({getFormattedBasePrice()})
                                                                    </span>
                                                                </div>
                                                                <Badge variant="outline" className="ml-2">
                                                                    {roomType.available_beds} beds
                                                                </Badge>
                                                            </div>
                                                        </SelectItem>
                                                    ))
                                                ) : (
                                                    <div className="px-2 py-1.5 text-sm text-muted-foreground">
                                                        No room types available for this hostel.
                                                    </div>
                                                )}
                                            </SelectContent>
                                        </Select>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="students" className="text-sm font-medium">Number of Students</Label>
                                    <div className="relative">
                                        <Users className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                        <div className="h-11 flex items-center px-3 py-2 border border-gray-200 bg-gray-50 rounded-md">
                                            <span className="text-gray-700 font-medium pl-7">1 Student</span>
                                            <span className="ml-auto text-sm text-gray-500">(Fixed - One booking per student)</span>
                                        </div>
                                    </div>
                                    {errors.students && (
                                        <p className="text-sm text-red-600">{errors.students}</p>
                                    )}
                                </div>
                            </div>

                            {/* Room Assignment Info */}
                            {data.room_type_id && (
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                    <div className="flex items-start gap-3">
                                        <div className="rounded-full bg-blue-100 p-2">
                                            <Home className="h-4 w-4 text-blue-600" />
                                        </div>
                                        <div className="flex-1">
                                            <h4 className="font-semibold text-blue-800">Automatic Room Assignment</h4>
                                            <p className="text-sm text-blue-700 mt-1">
                                                {user?.gender === 'male' 
                                                    ? 'You will be automatically assigned to an available room allocated for male students.'
                                                    : user?.gender === 'female' 
                                                    ? 'You will be automatically assigned to an available room allocated for female students.'
                                                    : 'You will be automatically assigned to an available room.'
                                                }
                                            </p>
                                            <p className="text-xs text-blue-600 mt-2">
                                                Room assignment will be confirmed after payment is processed.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            
                        </div>
                    )}

                    {/* Special Requests */}
                    <div className="space-y-3">
                        <Label htmlFor="special_requests" className="text-base font-medium">Special Requests (Optional)</Label>
                        <Textarea
                            id="special_requests"
                            value={data.special_requests}
                            onChange={(e) => setData('special_requests', e.target.value)}
                            rows={4}
                            className="w-full rounded-lg border-input bg-background px-4 py-3 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                            placeholder="e.g., accessibility needs, allergies, specific room location, etc."
                        />
                    </div>

                    {/* Payment Options */}
                    {hostel?.allow_deposit && selectedRoomType && (
                         <div className="space-y-6">
                            <div className="text-center">
                                <div className="flex items-center justify-center gap-2 mb-2">
                                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                        <span className="text-primary font-semibold text-sm">4</span>
                                    </div>
                                    <h2 className="text-xl font-semibold">Payment Options</h2>
                                </div>
                                <p className="text-muted-foreground">Choose how you'd like to pay</p>
                            </div>

                            <div className="grid gap-4 sm:grid-cols-2">
                                <button
                                    type="button"
                                    className={`rounded-lg border-2 p-6 text-left transition-all ${data.payment_type === 'full' ? 'border-primary bg-primary/5 ring-2 ring-primary' : 'border-gray-200 hover:border-gray-300'}`}
                                    onClick={() => setData('payment_type', 'full')}
                                >
                                    <h4 className="font-semibold">Pay in Full</h4>
                                    <p className="text-sm text-muted-foreground">Secure your room by paying the full amount now.</p>
                                </button>
                                <button
                                    type="button"
                                    className={`rounded-lg border-2 p-6 text-left transition-all ${data.payment_type === 'deposit' ? 'border-primary bg-primary/5 ring-2 ring-primary' : 'border-gray-200 hover:border-gray-300'}`}
                                    onClick={() => setData('payment_type', 'deposit')}
                                >
                                    <h4 className="font-semibold">Pay 30% Deposit</h4>
                                    <p className="text-sm text-muted-foreground">Pay a deposit now and the rest later.</p>
                                </button>
                            </div>

                            {/* Pricing Summary */}
                            <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
                                <h3 className="text-lg font-semibold mb-4">Pricing Summary</h3>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span>{getSemesterLabel(data.semester)} - {data.academic_year}</span>
                                        <span>{formatPrice(pricing.subtotal)}</span>
                                    </div>
                                    <div className="flex justify-between text-muted-foreground">
                                        <span>Charges (3%)</span>
                                        <span>{formatPrice(pricing.charges)}</span>
                                    </div>
                                    <div className="flex justify-between text-muted-foreground">
                                        <span>Service fee</span>
                                        <span>{formatPrice(pricing.serviceFee)}</span>
                                    </div>
                                    <Separator />
                                    <div className="flex justify-between font-semibold">
                                        <span>Total</span>
                                        <span>{formatPrice(pricing.total)}</span>
                                    </div>
                                    
                                    {data.payment_type === 'deposit' && (
                                        <>
                                            <Separator className="my-3" />
                                            <div className="space-y-2">
                                                <div className="flex justify-between text-primary font-semibold">
                                                    <span>Amount Due Now (30% Deposit)</span>
                                                    <span>{formatPrice(pricing.amountDueNow)}</span>
                                                </div>
                                                <div className="flex justify-between text-muted-foreground">
                                                    <span>Remaining Balance (Due later)</span>
                                                    <span>{formatPrice(pricing.amountDueLater)}</span>
                                                </div>
                                            </div>
                                        </>
                                    )}
                                    
                                    {data.payment_type === 'full' && (
                                        <>
                                            <Separator className="my-3" />
                                            <div className="flex justify-between text-primary font-semibold text-lg">
                                                <span>Amount Due Now</span>
                                                <span>{formatPrice(pricing.amountDueNow)}</span>
                                            </div>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Submit Button */}
                    <div className="space-y-4">

                        
                        <Button 
                            type="submit" 
                            disabled={processing || !selectedRoomType || pricing.total === 0}
                            className="w-full h-12 text-base font-semibold rounded-full"
                        >
                            <CreditCard className="mr-2 h-5 w-5" />
                            {processing
                                ? 'Processing...' 
                                : `Pay Now`
                            }
                        </Button>
                    </div>
                </form>
            </div>
            </div>
        </AppLayout>
    );
}