import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    CreditCard, Shield, Lock, CheckCircle, AlertCircle, 
    MapPin, Calendar, Users, Building2, ArrowLeft, Info, GraduationCap
} from 'lucide-react';
import { useState } from 'react';

interface User {
    id: string;
    name: string;
    email: string;
}

interface Hostel {
    id: string;
    name: string;
    city: string;
    state: string;
    photos: string[];
    payment_period: string;
}

interface RoomType {
    id: string;
    name: string;
    category: string;
    description: string;
}

interface Booking {
    id: string;
    booking_reference: string;
    user: User;
    hostel: Hostel;
    room_type: RoomType; 
    semester: string;
    academic_year: string;
    students: number;
    price_per_semester: number;
    subtotal: number;
    charges: number;
    service_fee: number;
    total_amount: number;
    amount_paid: number;
    amount_due: number;
    payment_type: 'full' | 'deposit';
    payment_status: string;
    status: string;
    room?: {
        id: string;
        room_number: string;
    };
}

interface Props {
    booking: Booking;
    payment_amount: number;
}



export default function BookingPayment({ booking, payment_amount }: Props) {
    const { auth } = usePage().props as any;
    const [isLoading, setIsLoading] = useState(false);

    // Helper function to get payment period text
    const getPaymentPeriodText = () => {
        switch(booking.hostel.payment_period) {
            case 'month': return 'monthly';
            case 'semester': return 'semester';
            case 'year': return 'academic year';
            default: return 'period';
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Bookings', href: '/bookings' },
        { title: booking?.booking_reference || 'Payment', href: `/bookings/${booking?.id}` },
        { title: 'Payment', href: '#' },
    ];

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 0,
        }).format(price);
    };

    const getSemesterLabel = (semester: string) => {
        // For monthly payment hostels, show different labels
        if (booking.hostel?.payment_period === 'month') {
            switch (semester) {
                case 'first': return 'September - December (4 months)';
                case 'second': return 'January - May (5 months)';
                case 'full_year': return 'Full Academic Year (9 months)';
                default: return semester;
            }
        }
        
        // For semester/year payment hostels, show traditional labels
        switch (semester) {
            case 'first': return 'First Semester';
            case 'second': return 'Second Semester';
            case 'full_year': return 'Full Academic Year';
            default: return semester;
        }
    };

    const handlePayment = () => {
        setIsLoading(true);
        
        // Use window.location for full page redirect to avoid CORS issues
        window.location.href = `/payments/${booking.id}/initialize`;
    };

    if (!booking) {
        return (
            <AppLayout>
                <Head title="Error" />
                <div className="min-h-screen bg-white">
                    <div className="container mx-auto px-4 py-8">
                        <div className="flex h-96 flex-col items-center justify-center gap-4">
                            <AlertCircle className="h-12 w-12 text-red-500" />
                            <h1 className="text-2xl font-bold">No Booking Data</h1>
                            <p className="text-center text-muted-foreground">
                                The booking data was not found. Please try again.
                            </p>
                            <Button onClick={() => router.visit('/bookings')} className="rounded-full">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to My Bookings
                            </Button>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (!booking.hostel) {
        return (
            <AppLayout>
                <Head title="Error" />
                <div className="min-h-screen bg-white">
                    <div className="container mx-auto px-4 py-8">
                        <div className="flex h-96 flex-col items-center justify-center gap-4">
                            <AlertCircle className="h-12 w-12 text-red-500" />
                            <h1 className="text-2xl font-bold">Hostel Details Missing</h1>
                            <p className="text-center text-muted-foreground">
                                The hostel information for this booking could not be loaded.
                            </p>
                            <Button onClick={() => router.visit('/bookings')} className="rounded-full">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to My Bookings
                            </Button>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (!booking.room_type) {
        return (
            <AppLayout>
                <Head title="Error" />
                <div className="min-h-screen bg-white">
                    <div className="container mx-auto px-4 py-8">
                        <div className="flex h-96 flex-col items-center justify-center gap-4">
                            <AlertCircle className="h-12 w-12 text-red-500" />
                            <h1 className="text-2xl font-bold">Room Type Details Missing</h1>
                            <p className="text-center text-muted-foreground">
                                The room type information for this booking could not be loaded.
                            </p>
                            <Button onClick={() => router.visit('/bookings')} className="rounded-full">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to My Bookings
                            </Button>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Payment - ${booking.booking_reference}`} />
            
            <div className="min-h-screen bg-white">
                <div className="container mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center gap-4">
                            <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => router.visit(`/bookings/${booking.id}`)}
                                className="rounded-full"
                            >
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                            <div>
                                <h1 className="text-3xl font-bold text-foreground">
                                    Complete Your Payment
                                </h1>
                                <p className="mt-2 text-muted-foreground">
                                    You'll be redirected to Paystack to securely complete your payment
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="grid gap-8 lg:grid-cols-3">
                        {/* Payment Form */}
                        <div className="lg:col-span-2">
                            <div className="p-6 space-y-6">
                                <div>
                                    <h2 className="text-lg font-semibold flex items-center gap-2">
                                        <CreditCard className="h-5 w-5" />
                                        Payment Details
                                    </h2>
                                    <p className="text-sm text-muted-foreground mt-1">
                                        Review and complete your payment
                                    </p>
                                </div>

                                {/* Payment Amount */}
                                <div className="rounded-lg bg-blue-50 p-4 border border-blue-200">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h3 className="font-medium text-blue-900">
                                                {booking.payment_type === 'deposit' ? 'Deposit Payment' : 'Full Payment'}
                                            </h3>
                                            <p className="text-sm text-blue-700">
                                                {booking.payment_type === 'deposit' 
                                                    ? 'Pay your deposit to secure your booking'
                                                    : 'Complete payment for your hostel booking'
                                                }
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-2xl font-bold text-blue-900">
                                                {formatPrice(payment_amount)}
                                            </div>
                                            <div className="text-sm text-blue-700">
                                                Total due now
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Security Notice */}
                                <Alert>
                                    <Shield className="h-4 w-4" />
                                    <AlertDescription>
                                        Your payment is secured by Paystack. We use industry-standard encryption to protect your financial information.
                                    </AlertDescription>
                                </Alert>

                                {/* Payment Button */}
                                <div className="space-y-4">
                                    <Button
                                        onClick={handlePayment}
                                        disabled={isLoading}
                                        className="w-full h-12 text-lg"
                                        size="lg"
                                    >
                                        {isLoading ? (
                                            <div className="flex items-center gap-2">
                                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                                                Processing...
                                            </div>
                                        ) : (
                                            <>
                                                <Lock className="mr-2 h-5 w-5" />
                                                Proceed to Payment ({formatPrice(payment_amount)})
                                            </>
                                        )}
                                    </Button>

                                    <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                                        <Lock className="h-4 w-4" />
                                        <span>Secured by 256-bit SSL encryption</span>
                                    </div>
                                </div>

                                {/* Terms */}
                                <div className="text-xs text-gray-600">
                                    <p>
                                        By proceeding with payment, you agree to our{' '}
                                        <a href="#" className="text-primary underline">
                                            Terms of Service
                                        </a>{' '}
                                        and{' '}
                                        <a href="#" className="text-primary underline">
                                            Cancellation Policy
                                        </a>
                                        .
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Booking Summary */}
                        <div className="space-y-6">
                            {/* Hostel Info */}
                            <div className="p-6 space-y-4">
                                {booking.hostel.photos && booking.hostel.photos.length > 0 ? (
                                    <img
                                        src={booking.hostel.photos[0]}
                                        alt={booking.hostel.name}
                                        className="aspect-[4/3] w-full rounded-lg object-cover"
                                    />
                                ) : (
                                    <div className="aspect-[4/3] flex items-center justify-center bg-gray-100 rounded-lg">
                                        <Building2 className="h-12 w-12 text-gray-400" />
                                    </div>
                                )}
                                
                                <div>
                                    <h3 className="text-lg font-semibold">{booking.hostel.name}</h3>
                                    <div className="mt-1 flex items-center gap-1 text-sm text-gray-600">
                                        <MapPin className="h-4 w-4" />
                                        <span>{booking.hostel.city}, {booking.hostel.state}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Booking Summary */}
                            <div className="p-6 space-y-4">
                                <h2 className="text-lg font-semibold">Booking Summary</h2>
                                
                                <div className="space-y-3">
                                    <div>
                                        <span className="text-sm text-gray-600">Booking Reference</span>
                                        <div className="font-medium">{booking.booking_reference}</div>
                                    </div>
                                    
                                    <div>
                                        <span className="text-sm text-gray-600">Room Type</span>
                                        <div className="font-medium">{booking.room_type?.name || 'N/A'}</div>
                                    </div>

                                    {booking.room && (
                                        <div>
                                            <span className="text-sm text-gray-600">Room Selected</span>
                                            <div className="font-medium">{booking.room.room_number || 'N/A'}</div>
                                        </div>
                                    )}
                                    
                                    <div className="flex items-center gap-2 text-sm">
                                        <Calendar className="h-4 w-4 text-gray-400" />
                                        <div>
                                            <div className="font-medium">
                                                {booking.semester ? getSemesterLabel(booking.semester) : 'N/A'}
                                            </div>
                                            {booking.hostel?.payment_period === 'month' && (
                                                <div className="text-xs text-gray-500">Monthly payment period</div>
                                            )}
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center gap-2 text-sm">
                                        <Users className="h-4 w-4 text-gray-400" />
                                        <span>
                                            {booking.students} {booking.students === 1 ? 'student' : 'students'}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Price Breakdown */}
                            <div className="p-6 space-y-4">
                                <h2 className="text-lg font-semibold">Price Breakdown</h2>
                                
                                <div className="space-y-3">
                                    <div className="flex justify-between text-sm">
                                        <span>Base Price ({getPaymentPeriodText()})</span>
                                        <span>{formatPrice(booking.price_per_semester)}</span>
                                    </div>
                                    
                                    <div className="flex justify-between text-sm">
                                        <span>Charges</span>
                                        <span>{formatPrice(booking.charges)}</span>
                                    </div>
                                    
                                    <div className="flex justify-between text-sm">
                                        <span>Service fee</span>
                                        <span>{formatPrice(booking.service_fee)}</span>
                                    </div>
                                    
                                    <Separator />
                                    
                                    <div className="flex justify-between font-semibold">
                                        <span>Total</span>
                                        <span>{formatPrice(booking.total_amount)}</span>
                                    </div>
                                    
                                    {booking.payment_type === 'deposit' && (
                                        <>
                                            <Separator />
                                            <div className="space-y-2 rounded-lg bg-blue-50 p-3 text-sm">
                                                <div className="flex justify-between font-medium text-blue-900">
                                                    <span>Due now (30%)</span>
                                                    <span>{formatPrice(payment_amount)}</span>
                                                </div>
                                                <div className="flex justify-between text-blue-700">
                                                    <span>Due on arrival</span>
                                                    <span>{formatPrice(booking.total_amount - payment_amount)}</span>
                                                </div>
                                            </div>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}