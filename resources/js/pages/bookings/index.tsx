import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    Calendar, MapPin, CreditCard, Eye, Edit,
    CheckCircle, Clock, XCircle, Building2,
    Filter, Search, Plus, GraduationCap, ArrowRight
} from 'lucide-react';
import { useState } from 'react';

interface User {
    id: string;
    name: string;
    email: string;
    role: string;
}

interface Hostel {
    id: string;
    name: string;
    city: string;
    state: string;
    photos: string[];
}

interface RoomType {
    id: string;
    name: string;
    category: string;
}

interface Room {
    id: string;
    room_number: string;
}

interface Booking {
    id: string;
    booking_reference: string;
    user: User;
    hostel: Hostel;
    room_type: RoomType;  // Changed from roomType to room_type
    room?: Room;
    semester: string;
    academic_year: string;
    students: number;
    total_amount: number;
    amount_paid: number;
    amount_due: number;
    payment_status: 'pending' | 'partially_paid' | 'paid' | 'failed' | 'refunded';
    status: 'pending' | 'confirmed' | 'moved_in' | 'active' | 'moved_out' | 'cancelled';
    payment_type: 'full' | 'deposit';
    created_at: string;
}

interface Props {
    bookings: {
        data: Booking[];
        links: any;
        meta: any;
    };
    filters: {
        status?: string;
        hostel_id?: string;
        date_from?: string;
        date_to?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Bookings', href: '/bookings' },
];

export default function BookingIndex({ bookings, filters }: Props) {
    const { auth } = usePage().props as any;
    const [showFilters, setShowFilters] = useState(false);
    const [localFilters, setLocalFilters] = useState(filters);

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 0,
        }).format(price);
    };

    const getSemesterLabel = (semester: string, booking?: any) => {
        // For monthly payment hostels, show different labels
        if (booking?.hostel?.payment_period === 'month') {
            switch (semester) {
                case 'first': return 'September - December (4 months)';
                case 'second': return 'January - May (5 months)';
                case 'full_year': return 'Full Academic Year (9 months)';
                default: return semester;
            }
        }
        
        // For semester/year payment hostels, show traditional labels
        switch (semester) {
            case 'first': return 'First Semester';
            case 'second': return 'Second Semester';
            case 'full_year': return 'Full Academic Year';
            default: return semester;
        }
    };

    const getStatusBadge = (status: string) => {
        const statusStyles = {
            pending: 'bg-yellow-50 text-yellow-700 border-yellow-200',
            confirmed: 'bg-blue-50 text-blue-700 border-blue-200',
            moved_in: 'bg-green-50 text-green-700 border-green-200',
            active: 'bg-emerald-50 text-emerald-700 border-emerald-200',
            moved_out: 'bg-gray-50 text-gray-700 border-gray-200',
            cancelled: 'bg-red-50 text-red-700 border-red-200',
        };
        
        const style = statusStyles[status as keyof typeof statusStyles] || 'bg-gray-50 text-gray-700 border-gray-200';
        const label = status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
        
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${style}`}>
                {label}
            </span>
        );
    };

    const getPaymentStatusBadge = (status: string) => {
        const statusStyles = {
            pending: 'bg-orange-50 text-orange-700 border-orange-200',
            partially_paid: 'bg-amber-50 text-amber-700 border-amber-200',
            paid: 'bg-green-50 text-green-700 border-green-200',
            failed: 'bg-red-50 text-red-700 border-red-200',
            refunded: 'bg-gray-50 text-gray-700 border-gray-200',
        };
        
        const style = statusStyles[status as keyof typeof statusStyles] || 'bg-gray-50 text-gray-700 border-gray-200';
        const label = status === 'partially_paid' ? 'Partially Paid' : status.charAt(0).toUpperCase() + status.slice(1);
        
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${style}`}>
                {label}
            </span>
        );
    };

    const applyFilters = () => {
        const params = new URLSearchParams();
        Object.entries(localFilters).forEach(([key, value]) => {
            if (value) params.append(key, value);
        });
        
        router.get('/bookings', Object.fromEntries(params), {
            preserveState: true,
            replace: true,
        });
    };

    const clearFilters = () => {
        setLocalFilters({});
        router.get('/bookings', {}, { preserveState: true, replace: true });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="My Bookings" />
            
            <div className="min-h-screen bg-white">
                <div className="container mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                            <div>
                                <h1 className="text-3xl font-bold text-foreground">
                                    {auth.user.role === 'student' ? 'My Bookings' : 'Manage Bookings'}
                                </h1>
                                <p className="mt-2 text-muted-foreground">
                                    {auth.user.role === 'student' 
                                        ? 'Track and manage your hostel reservations'
                                        : 'Manage bookings for your hostels'
                                    }
                                </p>
                            </div>
                            
                            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                                {auth.user.role === 'student' && (
                                    <Button onClick={() => router.visit('/search')} className="rounded-full w-full sm:w-auto">
                                        <Plus className="mr-2 h-4 w-4" />
                                        New Booking
                                    </Button>
                                )}
                                <Button variant="outline" onClick={() => setShowFilters(!showFilters)} className="rounded-full w-full sm:w-auto">
                                    <Filter className="mr-2 h-4 w-4" />
                                    Filters
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Filters */}
                    {showFilters && (
                        <div className="mb-8 rounded-xl bg-white border border-input p-6">
                            <h3 className="text-lg font-semibold mb-4">Filter Bookings</h3>
                            <div className="grid gap-4 md:grid-cols-4">
                                <div>
                                    <label className="text-sm font-medium text-foreground mb-2 block">Status</label>
                                    <Select 
                                        value={localFilters.status || ''} 
                                        onValueChange={(value) => setLocalFilters(prev => ({ ...prev, status: value }))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All statuses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">All statuses</SelectItem>
                                            <SelectItem value="pending">Pending</SelectItem>
                                            <SelectItem value="confirmed">Confirmed</SelectItem>
                                            <SelectItem value="moved_in">Moved In</SelectItem>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="moved_out">Completed</SelectItem>
                                            <SelectItem value="cancelled">Cancelled</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-foreground mb-2 block">From Date</label>
                                    <Input
                                        type="date"
                                        value={localFilters.date_from || ''}
                                        onChange={(e) => setLocalFilters(prev => ({ ...prev, date_from: e.target.value }))}
                                    />
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-foreground mb-2 block">To Date</label>
                                    <Input
                                        type="date"
                                        value={localFilters.date_to || ''}
                                        onChange={(e) => setLocalFilters(prev => ({ ...prev, date_to: e.target.value }))}
                                    />
                                </div>

                                <div className="flex items-end gap-2">
                                    <Button onClick={applyFilters} className="flex-1 rounded-full">
                                        <Search className="mr-2 h-4 w-4" />
                                        Apply
                                    </Button>
                                    <Button variant="outline" onClick={clearFilters} className="rounded-full">
                                        Clear
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Bookings List */}
                    <div className="space-y-4">
                        {bookings.data.length > 0 ? (
                            <>
                                {bookings.data.map((booking) => (
                                    <div 
                                        key={booking.id} 
                                        className="group cursor-pointer transition-all hover:-translate-y-1 rounded-xl overflow-hidden bg-white border border-input"
                                    >
                                        <div className="p-4 sm:p-6">
                                            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
                                                {/* Content */}
                                                <div className="flex-1">
                                                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                                                        <div className="flex-1">
                                                            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                                                                <h3 className="text-lg sm:text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
                                                                    {booking.hostel?.name || 'Hostel not available'}
                                                                </h3>
                                                                <div className="flex gap-2">
                                                                    {getStatusBadge(booking.status)}
                                                                    {/* Only show payment status if it's not pending, or if booking is confirmed but payment is pending */}
                                                                    {(booking.payment_status !== 'pending' || booking.status === 'confirmed') && getPaymentStatusBadge(booking.payment_status)}
                                                                </div>
                                                            </div>
                                                            
                                                            <div className="flex items-center gap-2 text-muted-foreground mb-4">
                                                                <MapPin className="h-4 w-4" />
                                                                <span>{booking.hostel?.city || 'City not available'}, {booking.hostel?.state || 'State not available'}</span>
                                                            </div>

                                                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                                                                <div>
                                                                    <div className="text-muted-foreground font-medium mb-1">Booking Reference</div>
                                                                    <div className="font-semibold text-primary">{booking.booking_reference}</div>
                                                                </div>
                                                                
                                                                <div>
                                                                    <div className="text-muted-foreground font-medium mb-1">Room Type</div>
                                                                    <div className="font-semibold text-foreground">{booking.room_type?.name || 'Room type unavailable'}</div>
                                                                </div>

                                                                <div className="sm:col-span-2 lg:col-span-1">
                                                                    <div className="text-muted-foreground font-medium mb-1">Academic Period</div>
                                                                    <div className="font-semibold text-foreground flex items-center gap-1">
                                                                        <GraduationCap className="h-4 w-4 text-primary" />
                                                                        {getSemesterLabel(booking.semester, booking)} {booking.academic_year}
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            {/* Payment Info */}
                                                            {booking.payment_status !== 'paid' && booking.amount_due > 0 && (
                                                                <div className="mt-4 p-3 bg-orange-50 rounded-lg border border-orange-200">
                                                                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                                                                        <div>
                                                                            <div className="font-semibold text-orange-800">Outstanding Balance</div>
                                                                            <div className="text-orange-700">{formatPrice(booking.amount_due)} remaining</div>
                                                                        </div>
                                                                        {auth.user.id === booking.user?.id && booking.status === 'pending' && (
                                                                            <Button 
                                                                                size="sm"
                                                                                onClick={(e) => {
                                                                                    e.stopPropagation();
                                                                                    router.visit(`/bookings/${booking.id}/payment`);
                                                                                }}
                                                                                className="bg-primary text-primary-foreground rounded-full hover:bg-primary/90 w-full sm:w-auto"
                                                                            >
                                                                                Pay Now
                                                                                <ArrowRight className="ml-1 h-4 w-4" />
                                                                            </Button>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* Actions & Price */}
                                                        <div className="flex flex-row sm:flex-col justify-between sm:items-end gap-4 sm:ml-6">
                                                            <div className="text-left sm:text-right">
                                                                <div className="text-xl sm:text-2xl font-bold text-foreground">
                                                                    {formatPrice(booking.total_amount)}
                                                                </div>
                                                                <div className="text-sm text-muted-foreground">
                                                                    {booking.students} {booking.students === 1 ? 'student' : 'students'}
                                                                </div>
                                                            </div>
                                                            
                                                            <div className="flex gap-2">
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        router.visit(`/bookings/${booking.id}`);
                                                                    }}
                                                                    className="text-xs rounded-full"
                                                                >
                                                                    <Eye className="h-3.5 w-3.5 mr-1.5" />
                                                                    View
                                                                </Button>
                                                                
                                                                {booking.status === 'pending' && (
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            router.visit(`/bookings/${booking.id}/edit`);
                                                                        }}
                                                                        className="text-xs rounded-full"
                                                                    >
                                                                        <Edit className="h-3.5 w-3.5 mr-1.5" />
                                                                        Edit
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                {/* Pagination */}
                                {bookings.meta && bookings.meta.total > bookings.meta.per_page && (
                                    <div className="flex items-center justify-center gap-2 mt-8">
                                        {bookings.links.map((link: any, index: number) => (
                                            <Button
                                                key={index}
                                                variant={link.active ? "default" : "outline"}
                                                size="sm"
                                                disabled={!link.url}
                                                onClick={() => link.url && router.visit(link.url)}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                                className="rounded-full"
                                            />
                                        ))}
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="rounded-xl bg-white border border-input">
                                <div className="flex h-80 flex-col items-center justify-center p-8">
                                    <div className="w-20 h-20 bg-gradient-to-br from-primary/20 to-primary/40 rounded-full flex items-center justify-center mb-6">
                                        <Calendar className="h-10 w-10 text-primary" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-foreground mb-2">
                                        No bookings found
                                    </h3>
                                    <p className="text-muted-foreground text-center mb-6 max-w-md">
                                        {auth.user.role === 'student' 
                                            ? "You haven't made any bookings yet. Start by searching for hostels in your area."
                                            : "No bookings match your current filters. Try adjusting your search criteria."
                                        }
                                    </p>
                                    {auth.user.role === 'student' && (
                                        <Button 
                                            onClick={() => router.visit('/search')} 
                                            className="bg-primary text-primary-foreground rounded-full hover:bg-primary/90"
                                            size="lg"
                                        >
                                            <Plus className="mr-2 h-5 w-5" />
                                            Make Your First Booking
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
} 