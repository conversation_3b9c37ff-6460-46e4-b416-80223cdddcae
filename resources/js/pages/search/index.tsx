import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import Navigation from '@/components/homepage/navigation';
import Footer from '@/components/homepage/footer';
import { Head, router, useForm } from '@inertiajs/react';
import { MapPin, Calendar, Users, Search, GraduationCap, FilterIcon, Clock, TrendingUp, AlertTriangle } from 'lucide-react';
import { useState } from 'react';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import { Slider } from '@/components/ui/slider';

interface SearchFilters {
    location: string;
    semester: string;
    academic_year: string;
    students: number;
    min_price: number;
    max_price: number;
    amenities: string[];
    university: string;
    distance_to_university: number;
    room_type: string;
    [key: string]: any; // Add index signature
}

interface Hostel {
    id: string;
    name: string;
    description: string;
    photos: string[];
    location: string;
    city: string;
    state: string;
    university_distance: number;
    total_beds: number;
    available_beds: number;
    amenities: string[];
    payment_period: string;
    base_price: number;
    service_fee: number;
    deposit_percentage: number;
    cancellation_fee: number;
    room_types: {
        id: string;
        name: string;
        available_count: number;
    }[];
}

interface SearchOptions {
    universities: { value: string; label: string; }[];
    academicYears: { value: string; label: string; }[];
    amenities: string[];
    roomTypes: string[];
    paymentPeriods: { value: string; label: string; }[];
}



export default function SearchIndex({ 
    hostels, 
    filters, 
    total,
    currentPage,
    lastPage,
    searchOptions
}: { 
    hostels: Hostel[];
    filters: SearchFilters;
    total: number;
    currentPage: number;
    lastPage: number;
    searchOptions: SearchOptions;
}) {
    const { auth } = usePage<SharedData>().props;
    const [showFilters, setShowFilters] = useState(false);

    const { data, setData, get, processing } = useForm<SearchFilters>({
        location: filters.location || '',
        semester: filters.semester || 'month',
        academic_year: filters.academic_year || (searchOptions.academicYears[0]?.value || '2024/2025'),
        students: filters.students || 1,
        min_price: filters.min_price || 500,
        max_price: filters.max_price || 5000,
        amenities: filters.amenities || [],
        university: filters.university || 'All Universities',
        distance_to_university: filters.distance_to_university || 10,
        room_type: filters.room_type || 'All Room Types',
    });

    const handleSearch = (e?: React.FormEvent) => {
        e?.preventDefault();
        get('/search', {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setData({
            location: '',
            semester: 'month',
            academic_year: searchOptions.academicYears[0]?.value || '2024/2025',
            students: 1,
            min_price: 500,
            max_price: 5000,
            amenities: [],
            university: 'All Universities',
            distance_to_university: 10,
            room_type: 'All Room Types',
        });
        handleSearch();
    };

    const getCurrentAcademicYear = () => {
        const now = new Date();
        const year = now.getFullYear();
        
        // Academic year runs from September to August
        if (now.getMonth() >= 8) { // September onwards
            return `${year}/${year + 1}`;
        } else {
            return `${year - 1}/${year}`;
        }
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 0,
        }).format(price);
    };

    const getSemesterLabel = (semester: string) => {
        switch (semester) {
            case 'first': return 'First Semester';
            case 'second': return 'Second Semester';
            case 'both': return 'Full Academic Year';
            default: return semester;
        }
    };

    const getSemesterDates = (academic_year: string, semester: string) => {
        const [startYear, endYear] = academic_year.split('/').map(y => parseInt(y));
        
        if (semester === 'first') {
            return {
                start: new Date(startYear, 8, 1), // September 1st
                end: new Date(startYear, 11, 31), // December 31st
            };
        } else if (semester === 'second') {
            return {
                start: new Date(endYear, 0, 15), // January 15th
                end: new Date(endYear, 4, 31), // May 31st
            };
        } else { // both
            return {
                start: new Date(startYear, 8, 1), // September 1st
                end: new Date(endYear, 4, 31), // May 31st
            };
        }
    };

    const getSelectedUniversityInfo = () => {
        return searchOptions.universities.find(uni => uni.value === data.university);
    };

    // Helper functions for pricing display
    const getPaymentPeriodText = (period: string) => {
        switch(period) {
            case 'month': return 'month';
            case 'semester': return 'semester';
            case 'year': return 'academic year';
            default: return 'period';
        }
    };

    const getFormattedPrice = (hostel: Hostel) => {
        if (!hostel.base_price) return 'Price not set';
        return `${formatPrice(hostel.base_price)} per ${getPaymentPeriodText(hostel.payment_period)}`;
    };

    return (
        <>
            <Head title="Search Student Hostels" />
            
            <div className="min-h-screen bg-background">
                {/* Navigation */}
                <Navigation />
                
                {/* Search Section */}
                <section className="py-12 bg-gradient-to-br from-white to-white">
                    <div className="container mx-auto px-4">

                        {/* Search Form */}
                        <form onSubmit={handleSearch} className="max-w-4xl mx-auto">
                            <div className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
                                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                    <div className="relative">
                                        <MapPin className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                                        <Input
                                            placeholder="Enter city or university"
                                            value={data.location}
                                            onChange={(e) => setData('location', e.target.value)}
                                            className="pl-11 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div className="relative">
                                        <Calendar className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                                        <Select value={data.semester} onValueChange={(value) => setData('semester', value)}>
                                            <SelectTrigger className="pl-11 h-12 border-gray-200 focus:border-blue-500">
                                                <SelectValue placeholder="Payment period" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="month">Per Month</SelectItem>
                                                <SelectItem value="semester">Per Semester</SelectItem>
                                                <SelectItem value="year">Per Academic Year</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="relative">
                                        <GraduationCap className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                                        <Select value={data.academic_year} onValueChange={(value) => setData('academic_year', value)}>
                                            <SelectTrigger className="pl-11 h-12 border-gray-200 focus:border-blue-500">
                                                <SelectValue placeholder="Academic year" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {searchOptions.academicYears.map((year) => (
                                                    <SelectItem key={year.value} value={year.value}>
                                                        {year.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <Button 
                                        type="submit" 
                                        disabled={processing}
                                        className="h-12 bg-blue-600 hover:bg-blue-700 rounded-full px-8 font-semibold"
                                    >
                                        <Search className="mr-2 h-5 w-5" />
                                        Search Hostels
                                    </Button>
                                </div>

                                {/* Advanced Filters Toggle */}
                                <div className="mt-6 pt-6 border-t border-gray-100">
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        onClick={() => setShowFilters(!showFilters)}
                                        className="flex items-center gap-2 text-gray-600 hover:text-blue-600"
                                    >
                                        <FilterIcon className="h-4 w-4" />
                                        {showFilters ? 'Hide Advanced Filters' : 'Show Advanced Filters'}
                                    </Button>
                                </div>
                            </div>

                            {/* Advanced Filters */}
                            {showFilters && (
                                <div className="mt-6 bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced Filters</h3>
                                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">University</label>
                                            <Select value={data.university} onValueChange={(value) => setData('university', value)}>
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {searchOptions.universities.map((uni) => (
                                                        <SelectItem key={uni.value} value={uni.value}>
                                                            {uni.label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Room Type</label>
                                            <Select value={data.room_type} onValueChange={(value) => setData('room_type', value)}>
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {searchOptions.roomTypes.map((type) => (
                                                        <SelectItem key={type} value={type}>
                                                            {type}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Price Range: {formatPrice(data.min_price)} - {formatPrice(data.max_price)}
                                            </label>
                                            <div className="mt-2 px-2">
                                                <Slider
                                                    value={[data.min_price, data.max_price]}
                                                    onValueChange={([min, max]) => {
                                                        setData('min_price', min);
                                                        setData('max_price', max);
                                                    }}
                                                    max={10000}
                                                    min={200}
                                                    step={100}
                                                    className="w-full"
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Amenities */}
                                    <div className="mt-6">
                                        <label className="block text-sm font-medium text-gray-700 mb-3">Amenities</label>
                                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                            {searchOptions.amenities.map((amenity) => (
                                                <div key={amenity} className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id={amenity}
                                                        checked={data.amenities.includes(amenity)}
                                                        onCheckedChange={(checked) => {
                                                            if (checked) {
                                                                setData('amenities', [...data.amenities, amenity]);
                                                            } else {
                                                                setData('amenities', data.amenities.filter(a => a !== amenity));
                                                            }
                                                        }}
                                                    />
                                                    <label htmlFor={amenity} className="text-sm text-gray-700 cursor-pointer">
                                                        {amenity}
                                                    </label>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-100">
                                        <Button type="button" variant="outline" onClick={clearFilters} className="rounded-full">
                                            Clear Filters
                                        </Button>
                                        <Button type="submit" disabled={processing} className="rounded-full">
                                            Apply Filters
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </form>
                    </div>
                </section>

                {/* Results Section */}
                <section className="py-12 bg-white">
                    <div className="container mx-auto px-4">
                        {/* Results Header */}
                        <div className="flex items-center justify-between mb-8">
                            <div>
                                <p className="text-gray-600 mt-1">
                                    {data.location && `in ${data.location} `}
                                    {data.university !== 'All Universities' && `near ${data.university}`}
                                </p>
                            </div>
                        </div>

                        {hostels.length === 0 ? (
                            <div className="text-center py-16">
                                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-white">
                                    <Search className="h-8 w-8 text-gray-400" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    No hostels found
                                </h3>
                                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                                    Try adjusting your search criteria or browse all available hostels.
                                </p>
                                <Button onClick={clearFilters} variant="outline" className="rounded-full">
                                    Clear all filters
                                </Button>
                            </div>
                        ) : (
                            <>
                                {/* Hostels Grid */}
                                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                                    {hostels.map((hostel) => (
                                        <Card 
                                            key={hostel.id} 
                                            className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border-0 p-0 gap-0 overflow-hidden"
                                        >
                                            <button
                                                onClick={() => router.get(`/hostels/${hostel.id}`)}
                                                className="w-full text-left h-full flex flex-col"
                                            >
                                                {/* Hostel Image */}
                                                <div className="relative h-56 overflow-hidden bg-gray-200">
                                                    {hostel.photos && hostel.photos[0] ? (
                                                        <img
                                                            src={hostel.photos[0]}
                                                            alt={hostel.name}
                                                            className="h-full w-full object-cover group-hover:scale-105 transition-transform duration-300"
                                                        />
                                                    ) : (
                                                        <div className="flex h-full items-center justify-center bg-gray-100">
                                                            <span className="text-gray-400 text-sm">No image available</span>
                                                        </div>
                                                    )}
                                                    
                                                    {/* Availability Badge */}
                                                    <div className="absolute top-4 right-4">
                                                        {hostel.available_beds === 0 ? (
                                                            <Badge variant="destructive" className="bg-red-500">Fully Booked</Badge>
                                                        ) : hostel.available_beds <= 5 ? (
                                                            <Badge className="bg-orange-500">Limited Spots</Badge>
                                                        ) : (
                                                            <Badge className="bg-green-500">Available</Badge>
                                                        )}
                                                    </div>
                                                </div>

                                                <CardContent className="p-6 flex-1">
                                                    {/* Hostel Info */}
                                                    <div className="mb-4">
                                                        <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                                                            {hostel.name}
                                                        </h3>
                                                    </div>

                                                    {/* Amenities */}
                                                    <div className="mb-4">
                                                        <div className="flex flex-wrap gap-1">
                                                            {hostel.amenities.slice(0, 3).map((amenity) => (
                                                                <Badge key={amenity} variant="secondary" className="text-xs bg-white text-blue-700">
                                                                    {amenity}
                                                                </Badge>
                                                            ))}
                                                            {hostel.amenities.length > 3 && (
                                                                <Badge variant="outline" className="text-xs">
                                                                    +{hostel.amenities.length - 3} more
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    </div>

                                                    {/* Availability Progress */}
                                                    <div className="mb-4">
                                                        <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
                                                            <span>Availability</span>
                                                            <span>{hostel.available_beds} of {hostel.total_beds} beds</span>
                                                        </div>
                                                        <Progress 
                                                            value={((hostel.total_beds - hostel.available_beds) / hostel.total_beds) * 100} 
                                                            className="h-2"
                                                        />
                                                    </div>

                                                    {/* Price and Action */}
                                                    <div className="flex items-center justify-between">
                                                        <div>
                                                            <div className="text-2xl font-bold text-gray-900">
                                                                {formatPrice(hostel.base_price || 0)}
                                                            </div>
                                                            <div className="text-sm text-gray-500">per {getPaymentPeriodText(hostel.payment_period)}</div>
                                                        </div>
                                                        <div className="text-blue-600 font-medium text-sm">
                                                            View Details →
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </button>
                                        </Card>
                                    ))}
                                </div>

                                {/* Pagination */}
                                {lastPage > 1 && (
                                    <div className="mt-12 flex justify-center">
                                        <div className="flex items-center gap-2">
                                            {currentPage > 1 && (
                                                <Button
                                                    variant="outline"
                                                    onClick={() => router.get('/search?' + new URLSearchParams({ ...data as any, page: (currentPage - 1).toString() }).toString())}
                                                    className="rounded-full"
                                                >
                                                    Previous
                                                </Button>
                                            )}
                                            
                                            <span className="px-4 py-2 text-sm text-gray-600">
                                                Page {currentPage} of {lastPage}
                                            </span>
                                            
                                            {currentPage < lastPage && (
                                                <Button
                                                    variant="outline"
                                                    onClick={() => router.get('/search?' + new URLSearchParams({ ...data as any, page: (currentPage + 1).toString() }).toString())}
                                                    className="rounded-full"
                                                >
                                                    Next
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </section>
                
                {/* Footer */}
                <Footer />
            </div>
        </>
    );
}