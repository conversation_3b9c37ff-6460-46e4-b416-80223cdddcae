<?php

namespace Tests\Feature;

use App\Services\PhotoUploadService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PhotoUploadServiceTest extends TestCase
{
    use RefreshDatabase;

    protected PhotoUploadService $photoService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->photoService = new PhotoUploadService();
        
        // Use fake storage for testing
        Storage::fake('r2');
    }

    /** @test */
    public function it_can_upload_a_single_photo_with_multiple_sizes()
    {
        // Create a fake image file
        $file = UploadedFile::fake()->image('test-photo.jpg', 800, 600);

        // Upload the photo
        $result = $this->photoService->uploadSinglePhoto($file, 'test-folder');

        // Assert the result structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('original_name', $result);
        $this->assertArrayHasKey('sizes', $result);

        // Assert all expected sizes are generated
        $expectedSizes = ['original', 'large', 'medium', 'thumbnail'];
        foreach ($expectedSizes as $size) {
            $this->assertArrayHasKey($size, $result['sizes']);
            $this->assertArrayHasKey('path', $result['sizes'][$size]);
            $this->assertArrayHasKey('url', $result['sizes'][$size]);
        }

        // Assert files were actually stored
        foreach ($expectedSizes as $size) {
            $path = $result['sizes'][$size]['path'];
            Storage::disk('r2')->assertExists($path);
        }
    }

    /** @test */
    public function it_can_upload_multiple_photos()
    {
        // Create fake image files
        $files = [
            UploadedFile::fake()->image('photo1.jpg', 800, 600),
            UploadedFile::fake()->image('photo2.png', 1200, 800),
        ];

        // Upload the photos
        $results = $this->photoService->uploadPhotos($files, 'test-folder');

        // Assert we got results for both photos
        $this->assertCount(2, $results);

        // Assert each result has the correct structure
        foreach ($results as $result) {
            $this->assertIsArray($result);
            $this->assertArrayHasKey('id', $result);
            $this->assertArrayHasKey('sizes', $result);
        }
    }

    /** @test */
    public function it_validates_file_size_and_type()
    {
        // Test with invalid file type
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1000);
        $result = $this->photoService->uploadSinglePhoto($invalidFile, 'test-folder');
        $this->assertNull($result);

        // Test with oversized file (mock a very large file - 60MB)
        $largeFile = UploadedFile::fake()->image('large.jpg', 2000, 2000)->size(60000); // 60MB
        $result = $this->photoService->uploadSinglePhoto($largeFile, 'test-folder');
        $this->assertNull($result);
    }

    /** @test */
    public function it_can_delete_photos()
    {
        // First upload a photo
        $file = UploadedFile::fake()->image('test-photo.jpg', 800, 600);
        $photoData = $this->photoService->uploadSinglePhoto($file, 'test-folder');

        // Verify files exist
        foreach ($photoData['sizes'] as $sizeData) {
            Storage::disk('r2')->assertExists($sizeData['path']);
        }

        // Delete the photos
        $this->photoService->deletePhotos([$photoData]);

        // Verify files are deleted
        foreach ($photoData['sizes'] as $sizeData) {
            Storage::disk('r2')->assertMissing($sizeData['path']);
        }
    }

    /** @test */
    public function it_can_get_photo_url_for_specific_size()
    {
        // Create mock photo data
        $photoData = [
            'id' => 'test-id',
            'sizes' => [
                'thumbnail' => ['url' => 'https://example.com/thumb.jpg'],
                'medium' => ['url' => 'https://example.com/medium.jpg'],
                'original' => ['url' => 'https://example.com/original.jpg'],
            ]
        ];

        // Test getting specific size
        $url = $this->photoService->getPhotoUrl($photoData, 'medium');
        $this->assertEquals('https://example.com/medium.jpg', $url);

        // Test fallback to original when size doesn't exist
        $url = $this->photoService->getPhotoUrl($photoData, 'nonexistent');
        $this->assertEquals('https://example.com/original.jpg', $url);
    }

    /** @test */
    public function it_can_check_if_photo_has_specific_size()
    {
        $photoData = [
            'sizes' => [
                'thumbnail' => ['url' => 'https://example.com/thumb.jpg'],
                'medium' => ['url' => 'https://example.com/medium.jpg'],
            ]
        ];

        $this->assertTrue($this->photoService->hasSize($photoData, 'thumbnail'));
        $this->assertTrue($this->photoService->hasSize($photoData, 'medium'));
        $this->assertFalse($this->photoService->hasSize($photoData, 'large'));
    }
}