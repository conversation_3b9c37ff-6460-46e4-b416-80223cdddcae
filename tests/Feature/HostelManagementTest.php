<?php

namespace Tests\Feature;

use App\Models\Hostel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class HostelManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_hostel_admin_can_view_hostels_index(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);
        $hostels = Hostel::factory()->count(3)->create(['owner_id' => $admin->id]);

        $response = $this->actingAs($admin)->get(route('hostels.index'));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('Hostels/Index')
                ->has('hostels.data', 3)
        );
    }

    public function test_student_can_view_public_hostels_index(): void
    {
        $student = User::factory()->create(['role' => 'student']);
        Hostel::factory()->count(3)->create(['is_active' => true, 'is_verified' => true]);

        $response = $this->actingAs($student)->get(route('hostels.index'));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('Hostels/Index')
                ->has('hostels.data', 3)
        );
    }

    public function test_hostel_admin_can_create_hostel(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);

        $hostelData = [
            'name' => 'Test University Hall',
            'description' => 'A modern residential hall for students',
            'address' => '123 University Avenue',
            'city' => 'Accra',
            'state' => 'Greater Accra',
            'country' => 'Ghana',
            'postal_code' => '00233',
            'phone' => '+233244123456',
            'email' => '<EMAIL>',
            'website' => 'https://testunihall.edu.gh',
            'amenities' => ['wifi', 'laundry', 'dining_hall'],
            'house_rules' => 'Quiet hours from 10 PM to 6 AM',
            'cancellation_policy' => 'Full refund if cancelled 2 weeks before',
            'is_active' => true,
        ];

        $response = $this->actingAs($admin)->post(route('hostels.store'), $hostelData);

        $response->assertRedirect();
        $this->assertDatabaseHas('hostels', [
            'name' => 'Test University Hall',
            'owner_id' => $admin->id,
            'city' => 'Accra',
        ]);
    }

    public function test_student_cannot_create_hostel(): void
    {
        $student = User::factory()->create(['role' => 'student']);

        $response = $this->actingAs($student)->post(route('hostels.store'), []);

        $response->assertForbidden();
    }

    public function test_hostel_admin_can_update_their_hostel(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);
        $hostel = Hostel::factory()->create(['owner_id' => $admin->id]);

        $updateData = [
            'name' => 'Updated Hostel Name',
            'description' => $hostel->description,
            'address' => $hostel->address,
            'city' => $hostel->city,
            'state' => $hostel->state,
            'country' => $hostel->country,
            'amenities' => ['wifi', 'laundry'],
        ];

        $response = $this->actingAs($admin)->put(route('hostels.update', $hostel), $updateData);

        $response->assertRedirect();
        $this->assertDatabaseHas('hostels', [
            'id' => $hostel->id,
            'name' => 'Updated Hostel Name',
        ]);
    }

    public function test_hostel_admin_cannot_update_other_admin_hostel(): void
    {
        $admin1 = User::factory()->create(['role' => 'hostel_admin']);
        $admin2 = User::factory()->create(['role' => 'hostel_admin']);
        $hostel = Hostel::factory()->create(['owner_id' => $admin1->id]);

        $response = $this->actingAs($admin2)->put(route('hostels.update', $hostel), [
            'name' => 'Unauthorized Update',
        ]);

        $response->assertForbidden();
    }

    public function test_super_admin_can_update_any_hostel(): void
    {
        $superAdmin = User::factory()->create(['role' => 'super_admin']);
        $admin = User::factory()->create(['role' => 'hostel_admin']);
        $hostel = Hostel::factory()->create(['owner_id' => $admin->id]);

        $updateData = [
            'name' => 'Super Admin Updated',
            'description' => $hostel->description,
            'address' => $hostel->address,
            'city' => $hostel->city,
            'state' => $hostel->state,
            'country' => $hostel->country,
            'amenities' => $hostel->amenities,
        ];

        $response = $this->actingAs($superAdmin)->put(route('hostels.update', $hostel), $updateData);

        $response->assertRedirect();
        $this->assertDatabaseHas('hostels', [
            'id' => $hostel->id,
            'name' => 'Super Admin Updated',
        ]);
    }

    public function test_hostel_admin_can_delete_their_hostel(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);
        $hostel = Hostel::factory()->create(['owner_id' => $admin->id]);

        $response = $this->actingAs($admin)->delete(route('hostels.destroy', $hostel));

        $response->assertRedirect();
        $this->assertSoftDeleted('hostels', ['id' => $hostel->id]);
    }

    public function test_hostel_show_displays_correct_information(): void
    {
        $user = User::factory()->create();
        $hostel = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);

        $response = $this->actingAs($user)->get(route('hostels.show', $hostel));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('Hostels/Show')
                ->has('hostel')
                ->where('hostel.id', $hostel->id)
                ->where('hostel.name', $hostel->name)
        );
    }

    public function test_inactive_hostel_not_visible_to_students(): void
    {
        $student = User::factory()->create(['role' => 'student']);
        $hostel = Hostel::factory()->create(['is_active' => false]);

        $response = $this->actingAs($student)->get(route('hostels.show', $hostel));

        $response->assertForbidden();
    }

    public function test_hostel_admin_can_toggle_hostel_status(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);
        $hostel = Hostel::factory()->create(['owner_id' => $admin->id, 'is_active' => true]);

        $response = $this->actingAs($admin)->patch(route('hostels.toggle-status', $hostel));

        $response->assertRedirect();
        $this->assertDatabaseHas('hostels', [
            'id' => $hostel->id,
            'is_active' => false,
        ]);
    }

    public function test_hostel_creation_validates_required_fields(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);

        $response = $this->actingAs($admin)->post(route('hostels.store'), []);

        $response->assertSessionHasErrors([
            'name',
            'description',
            'address',
            'city',
            'state',
            'country',
        ]);
    }

    public function test_hostel_search_functionality(): void
    {
        $user = User::factory()->create();
        Hostel::factory()->create([
            'name' => 'University of Ghana Hall',
            'city' => 'Accra',
            'is_active' => true,
            'is_verified' => true,
        ]);
        Hostel::factory()->create([
            'name' => 'KNUST Unity Hall',
            'city' => 'Kumasi',
            'is_active' => true,
            'is_verified' => true,
        ]);

        $response = $this->actingAs($user)->get(route('hostels.index', ['search' => 'University of Ghana']));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->has('hostels.data', 1)
                ->where('hostels.data.0.name', 'University of Ghana Hall')
        );
    }
}