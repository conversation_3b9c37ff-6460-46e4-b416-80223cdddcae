<?php

namespace Tests\Feature;

use App\Models\Bed;
use App\Models\Booking;
use App\Models\Hostel;
use App\Models\Room;
use App\Models\RoomType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchFunctionalityTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create(['role' => 'student']);
    }

    public function test_search_index_displays_correctly(): void
    {
        $response = $this->actingAs($this->user)->get(route('search.index'));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('Search/Index')
        );
    }

    public function test_search_by_location(): void
    {
        Hostel::factory()->create([
            'name' => 'University of Ghana Hall',
            'city' => 'Accra',
            'state' => 'Greater Accra',
            'is_active' => true,
            'is_verified' => true,
        ]);

        Hostel::factory()->create([
            'name' => 'KNUST Unity Hall',
            'city' => 'Kumasi',
            'state' => 'Ashanti',
            'is_active' => true,
            'is_verified' => true,
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search', [
            'location' => 'Accra',
        ]));

        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $response->assertJsonFragment(['city' => 'Accra']);
    }

    public function test_search_by_semester_dates(): void
    {
        $hostel = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        $roomType = RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'semester_type' => 'both',
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search', [
            'semester_start' => '2024-09-01',
            'semester_end' => '2024-12-20',
            'semester' => 'first',
        ]));

        $response->assertOk();
        $response->assertJsonCount(1, 'data');
    }

    public function test_search_by_price_range(): void
    {
        $hostel1 = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        RoomType::factory()->create([
            'hostel_id' => $hostel1->id,
            'price_per_semester' => 800,
        ]);

        $hostel2 = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        RoomType::factory()->create([
            'hostel_id' => $hostel2->id,
            'price_per_semester' => 1500,
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search', [
            'min_price' => 700,
            'max_price' => 1000,
        ]));

        $response->assertOk();
        $response->assertJsonCount(1, 'data');
    }

    public function test_search_by_room_type(): void
    {
        $hostel = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        
        RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'category' => 'private',
            'name' => 'Single Room',
        ]);

        RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'category' => 'dorm',
            'name' => '4-Person Room',
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search', [
            'room_type' => 'private',
        ]));

        $response->assertOk();
        $response->assertJsonPath('data.0.room_types.0.category', 'private');
    }

    public function test_search_by_gender_type(): void
    {
        $hostel = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        
        RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'gender_type' => 'female',
        ]);

        RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'gender_type' => 'male',
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search', [
            'gender' => 'female',
        ]));

        $response->assertOk();
        $response->assertJsonPath('data.0.room_types.0.gender_type', 'female');
    }

    public function test_search_by_amenities(): void
    {
        Hostel::factory()->create([
            'amenities' => ['wifi', 'laundry', 'dining_hall'],
            'is_active' => true,
            'is_verified' => true,
        ]);

        Hostel::factory()->create([
            'amenities' => ['parking', 'gym'],
            'is_active' => true,
            'is_verified' => true,
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search', [
            'amenities' => ['wifi', 'laundry'],
        ]));

        $response->assertOk();
        $response->assertJsonCount(1, 'data');
    }

    public function test_search_excludes_inactive_hostels(): void
    {
        Hostel::factory()->create([
            'name' => 'Active Hostel',
            'is_active' => true,
            'is_verified' => true,
        ]);

        Hostel::factory()->create([
            'name' => 'Inactive Hostel',
            'is_active' => false,
            'is_verified' => true,
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search'));

        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $response->assertJsonFragment(['name' => 'Active Hostel']);
    }

    public function test_search_excludes_unverified_hostels(): void
    {
        Hostel::factory()->create([
            'name' => 'Verified Hostel',
            'is_active' => true,
            'is_verified' => true,
        ]);

        Hostel::factory()->create([
            'name' => 'Unverified Hostel',
            'is_active' => true,
            'is_verified' => false,
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search'));

        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $response->assertJsonFragment(['name' => 'Verified Hostel']);
    }

    public function test_availability_check(): void
    {
        $hostel = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        $roomType = RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'capacity' => 4,
        ]);
        $room = Room::factory()->create(['room_type_id' => $roomType->id]);
        
        // Create 4 beds
        for ($i = 1; $i <= 4; $i++) {
            Bed::factory()->create(['room_id' => $room->id]);
        }

        $response = $this->actingAs($this->user)->post(route('api.search.availability'), [
            'hostel_id' => $hostel->id,
            'room_type_id' => $roomType->id,
            'semester_start' => '2024-09-01',
            'semester_end' => '2024-12-20',
            'students' => 2,
        ]);

        $response->assertOk();
        $response->assertJsonFragment(['available' => true]);
    }

    public function test_availability_check_with_existing_bookings(): void
    {
        $hostel = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        $roomType = RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'capacity' => 2,
        ]);
        $room = Room::factory()->create(['room_type_id' => $roomType->id]);
        
        // Create 2 beds
        $bed1 = Bed::factory()->create(['room_id' => $room->id]);
        $bed2 = Bed::factory()->create(['room_id' => $room->id]);

        // Create booking that occupies one bed
        Booking::factory()->create([
            'hostel_id' => $hostel->id,
            'room_type_id' => $roomType->id,
            'room_id' => $room->id,
            'bed_id' => $bed1->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
            'status' => 'confirmed',
        ]);

        $response = $this->actingAs($this->user)->post(route('api.search.availability'), [
            'hostel_id' => $hostel->id,
            'room_type_id' => $roomType->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
            'students' => 1,
        ]);

        $response->assertOk();
        $response->assertJsonFragment(['available' => true, 'available_beds' => 1]);
    }

    public function test_availability_check_no_availability(): void
    {
        $hostel = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        $roomType = RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'capacity' => 1,
        ]);
        $room = Room::factory()->create(['room_type_id' => $roomType->id]);
        $bed = Bed::factory()->create(['room_id' => $room->id]);

        // Create booking that occupies the only bed
        Booking::factory()->create([
            'hostel_id' => $hostel->id,
            'room_type_id' => $roomType->id,
            'room_id' => $room->id,
            'bed_id' => $bed->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
            'status' => 'confirmed',
        ]);

        $response = $this->actingAs($this->user)->post(route('api.search.availability'), [
            'hostel_id' => $hostel->id,
            'room_type_id' => $roomType->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
            'students' => 1,
        ]);

        $response->assertOk();
        $response->assertJsonFragment(['available' => false]);
    }

    public function test_search_suggestions(): void
    {
        Hostel::factory()->create([
            'name' => 'University of Ghana Hall',
            'city' => 'Accra',
            'is_active' => true,
            'is_verified' => true,
        ]);

        Hostel::factory()->create([
            'name' => 'KNUST Unity Hall',
            'city' => 'Kumasi',
            'is_active' => true,
            'is_verified' => true,
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search.suggestions', [
            'query' => 'Uni',
        ]));

        $response->assertOk();
        $response->assertJsonCount(2);
    }

    public function test_popular_destinations(): void
    {
        // Create hostels in different cities
        Hostel::factory()->count(3)->create([
            'city' => 'Accra',
            'is_active' => true,
            'is_verified' => true,
        ]);

        Hostel::factory()->count(2)->create([
            'city' => 'Kumasi',
            'is_active' => true,
            'is_verified' => true,
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search.popular'));

        $response->assertOk();
        $response->assertJsonStructure([
            '*' => ['city', 'count'],
        ]);
    }

    public function test_price_range_endpoint(): void
    {
        $hostel = Hostel::factory()->create(['is_active' => true, 'is_verified' => true]);
        
        RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'price_per_semester' => 500,
        ]);

        RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'price_per_semester' => 1500,
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search.price-range'));

        $response->assertOk();
        $response->assertJsonStructure([
            'min_price',
            'max_price',
        ]);
        $response->assertJsonFragment(['min_price' => 500, 'max_price' => 1500]);
    }

    public function test_search_with_multiple_filters(): void
    {
        $hostel = Hostel::factory()->create([
            'city' => 'Accra',
            'amenities' => ['wifi', 'laundry'],
            'is_active' => true,
            'is_verified' => true,
        ]);

        RoomType::factory()->create([
            'hostel_id' => $hostel->id,
            'category' => 'private',
            'price_per_semester' => 800,
            'gender_type' => 'female',
        ]);

        $response = $this->actingAs($this->user)->get(route('api.search', [
            'location' => 'Accra',
            'room_type' => 'private',
            'min_price' => 700,
            'max_price' => 900,
            'gender' => 'female',
            'amenities' => ['wifi'],
        ]));

        $response->assertOk();
        $response->assertJsonCount(1, 'data');
    }

    public function test_search_pagination(): void
    {
        Hostel::factory()->count(15)->create(['is_active' => true, 'is_verified' => true]);

        $response = $this->actingAs($this->user)->get(route('api.search', [
            'per_page' => 10,
        ]));

        $response->assertOk();
        $response->assertJsonCount(10, 'data');
        $response->assertJsonStructure([
            'data',
            'current_page',
            'last_page',
            'per_page',
            'total',
        ]);
    }
} 