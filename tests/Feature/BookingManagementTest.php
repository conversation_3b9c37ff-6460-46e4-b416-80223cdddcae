<?php

namespace Tests\Feature;

use App\Models\Bed;
use App\Models\Booking;
use App\Models\Hostel;
use App\Models\Room;
use App\Models\RoomType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BookingManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $student;
    protected User $admin;
    protected Hostel $hostel;
    protected RoomType $roomType;
    protected Room $room;
    protected Bed $bed;

    protected function setUp(): void
    {
        parent::setUp();

        $this->student = User::factory()->create(['role' => 'student']);
        $this->admin = User::factory()->create(['role' => 'hostel_admin']);
        $this->hostel = Hostel::factory()->create(['owner_id' => $this->admin->id]);
        $this->roomType = RoomType::factory()->create([
            'hostel_id' => $this->hostel->id,
            'price_per_semester' => 1000,
        ]);
        $this->room = Room::factory()->create(['room_type_id' => $this->roomType->id]);
        $this->bed = Bed::factory()->create(['room_id' => $this->room->id]);
    }

    public function test_student_can_view_bookings_index(): void
    {
        $bookings = Booking::factory()->count(3)->create(['user_id' => $this->student->id]);

        $response = $this->actingAs($this->student)->get(route('bookings.index'));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('Bookings/Index')
                ->has('bookings.data', 3)
        );
    }

    public function test_student_can_create_booking(): void
    {
        $bookingData = [
            'hostel_id' => $this->hostel->id,
            'room_type_id' => $this->roomType->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
            'students' => 1,
            'payment_type' => 'full',
            'special_requests' => 'Ground floor room preferred',
        ];

        $response = $this->actingAs($this->student)->post(route('bookings.store'), $bookingData);

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'user_id' => $this->student->id,
            'hostel_id' => $this->hostel->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
        ]);
    }

    public function test_booking_creation_calculates_correct_amounts(): void
    {
        $bookingData = [
            'hostel_id' => $this->hostel->id,
            'room_type_id' => $this->roomType->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
            'students' => 1,
            'payment_type' => 'full',
        ];

        $response = $this->actingAs($this->student)->post(route('bookings.store'), $bookingData);

        $booking = Booking::where('user_id', $this->student->id)->first();
        
        $this->assertEquals(1000, $booking->price_per_semester);
        $this->assertEquals(1000, $booking->subtotal);
        $this->assertEquals(30, $booking->charges); // 3% of 1000
        $this->assertEquals(50, $booking->service_fee);
        $this->assertEquals(1080, $booking->total_amount); // 1000 + 30 + 50
    }

    public function test_student_cannot_create_overlapping_bookings(): void
    {
        // Create first booking
        $firstBookingData = [
            'hostel_id' => $this->hostel->id,
            'room_type_id' => $this->roomType->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
            'students' => 1,
            'payment_type' => 'full',
        ];

        $this->actingAs($this->student)->post(route('bookings.store'), $firstBookingData);

        // Attempt to create overlapping booking for same semester
        $secondBookingData = [
            'hostel_id' => $this->hostel->id,
            'room_type_id' => $this->roomType->id,
            'semester' => 'first', // Same semester
            'academic_year' => '2024/2025', // Same academic year
            'students' => 1,
            'payment_type' => 'full',
        ];

        $response = $this->actingAs($this->student)->post(route('bookings.store'), $secondBookingData);

        $response->assertSessionHasErrors(['semester']);
    }

    public function test_student_can_view_their_booking(): void
    {
        $booking = Booking::factory()->create(['user_id' => $this->student->id]);

        $response = $this->actingAs($this->student)->get(route('bookings.show', $booking));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('Bookings/Show')
                ->where('booking.id', $booking->id)
        );
    }

    public function test_student_cannot_view_other_student_booking(): void
    {
        $otherStudent = User::factory()->create(['role' => 'student']);
        $booking = Booking::factory()->create(['user_id' => $otherStudent->id]);

        $response = $this->actingAs($this->student)->get(route('bookings.show', $booking));

        $response->assertForbidden();
    }

    public function test_hostel_admin_can_view_bookings_for_their_hostels(): void
    {
        $booking = Booking::factory()->create([
            'hostel_id' => $this->hostel->id,
            'user_id' => $this->student->id,
        ]);

        $response = $this->actingAs($this->admin)->get(route('bookings.show', $booking));

        $response->assertOk();
    }

    public function test_hostel_admin_can_confirm_booking(): void
    {
        $booking = Booking::factory()->create([
            'hostel_id' => $this->hostel->id,
            'status' => 'pending',
            'payment_status' => 'paid',
        ]);

        $response = $this->actingAs($this->admin)->post(route('bookings.confirm', $booking));

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'confirmed',
        ]);
        $this->assertNotNull($booking->fresh()->confirmed_at);
    }

    public function test_cannot_confirm_unpaid_booking(): void
    {
        $booking = Booking::factory()->create([
            'hostel_id' => $this->hostel->id,
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);

        $response = $this->actingAs($this->admin)->post(route('bookings.confirm', $booking));

        $response->assertSessionHasErrors();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'pending',
        ]);
    }

    public function test_student_can_cancel_pending_booking(): void
    {
        $booking = Booking::factory()->create([
            'user_id' => $this->student->id,
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);

        $response = $this->actingAs($this->student)->post(route('bookings.cancel', $booking));

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'cancelled',
        ]);
    }

    public function test_cannot_cancel_confirmed_booking_close_to_start_date(): void
    {
        $booking = Booking::factory()->create([
            'user_id' => $this->student->id,
            'status' => 'confirmed',
            'academic_year' => '2024/2025',
            'semester' => 'first',
        ]);

        $response = $this->actingAs($this->student)->post(route('bookings.cancel', $booking));

        $response->assertSessionHasErrors();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'confirmed',
        ]);
    }

    public function test_hostel_admin_can_check_in_student(): void
    {
        $booking = Booking::factory()->create([
            'hostel_id' => $this->hostel->id,
            'status' => 'confirmed',
            'bed_id' => $this->bed->id,
        ]);

        $response = $this->actingAs($this->admin)->post(route('bookings.checkin', $booking));

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'moved_in',
        ]);
        $this->assertNotNull($booking->fresh()->checked_in_at);
    }

    public function test_hostel_admin_can_check_out_student(): void
    {
        $booking = Booking::factory()->create([
            'hostel_id' => $this->hostel->id,
            'status' => 'moved_in',
            'bed_id' => $this->bed->id,
        ]);

        $response = $this->actingAs($this->admin)->post(route('bookings.checkout', $booking));

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'moved_out',
        ]);
        $this->assertNotNull($booking->fresh()->checked_out_at);
    }

    public function test_booking_validates_required_fields(): void
    {
        $response = $this->actingAs($this->student)->post(route('bookings.store'), []);

        $response->assertSessionHasErrors([
            'hostel_id',
            'room_type_id',
            'semester',
            'academic_year',
            'students',
            'payment_type',
        ]);
    }

    public function test_booking_validates_student_count(): void
    {
        $bookingData = [
            'hostel_id' => $this->hostel->id,
            'room_type_id' => $this->roomType->id,
            'semester' => 'first',
            'academic_year' => '2024/2025',
            'students' => 0, // Invalid
            'payment_type' => 'full',
        ];

        $response = $this->actingAs($this->student)->post(route('bookings.store'), $bookingData);

        $response->assertSessionHasErrors(['students']);
    }

    public function test_booking_analytics_endpoint(): void
    {
        Booking::factory()->count(5)->create([
            'hostel_id' => $this->hostel->id,
            'status' => 'confirmed',
        ]);

        $response = $this->actingAs($this->admin)->get(route('api.bookings.analytics'));

        $response->assertOk();
        $response->assertJsonStructure([
            'total_bookings',
            'confirmed_bookings',
            'pending_bookings',
            'revenue_data',
        ]);
    }
} 