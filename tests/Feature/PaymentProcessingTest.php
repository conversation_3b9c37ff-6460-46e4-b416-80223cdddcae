<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\User;
use App\Services\PaystackService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class PaymentProcessingTest extends TestCase
{
    use RefreshDatabase;

    protected User $student;
    protected Booking $booking;

    protected function setUp(): void
    {
        parent::setUp();

        $this->student = User::factory()->create(['role' => 'student']);
        $this->booking = Booking::factory()->create([
            'user_id' => $this->student->id,
            'total_amount' => 1100,
            'payment_status' => 'pending',
            'status' => 'pending',
        ]);
    }

    public function test_student_can_initialize_payment(): void
    {
        Http::fake([
            'api.paystack.co/transaction/initialize' => Http::response([
                'status' => true,
                'data' => [
                    'authorization_url' => 'https://checkout.paystack.com/abc123',
                    'access_code' => 'abc123',
                    'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
                ],
            ]),
        ]);

        $response = $this->actingAs($this->student)->get(route('payments.initialize', $this->booking));

        $response->assertRedirect('https://checkout.paystack.com/abc123');
    }

    public function test_cannot_initialize_payment_for_paid_booking(): void
    {
        $this->booking->update(['payment_status' => 'paid']);

        $response = $this->actingAs($this->student)->get(route('payments.initialize', $this->booking));

        $response->assertRedirect(route('bookings.show', $this->booking));
        $response->assertSessionHas('info', 'This booking has already been paid.');
    }

    public function test_cannot_initialize_payment_for_cancelled_booking(): void
    {
        $this->booking->update(['status' => 'cancelled']);

        $response = $this->actingAs($this->student)->get(route('payments.initialize', $this->booking));

        $response->assertRedirect(route('bookings.show', $this->booking));
        $response->assertSessionHas('error');
    }

    public function test_payment_callback_handles_successful_payment(): void
    {
        Http::fake([
            'api.paystack.co/transaction/verify/*' => Http::response([
                'status' => true,
                'data' => [
                    'status' => 'success',
                    'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
                    'amount' => 110000, // 1100 * 100 (kobo)
                    'metadata' => [
                        'booking_id' => $this->booking->id,
                    ],
                ],
            ]),
        ]);

        $response = $this->get(route('payments.callback', [
            'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
        ]));

        $response->assertRedirect(route('bookings.show', $this->booking));
        $response->assertSessionHas('success');

        $this->booking->refresh();
        $this->assertEquals('paid', $this->booking->payment_status);
        $this->assertEquals(1100, $this->booking->amount_paid);
    }

    public function test_payment_callback_handles_failed_payment(): void
    {
        Http::fake([
            'api.paystack.co/transaction/verify/*' => Http::response([
                'status' => true,
                'data' => [
                    'status' => 'failed',
                    'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
                    'amount' => 110000,
                    'metadata' => [
                        'booking_id' => $this->booking->id,
                    ],
                    'gateway_response' => 'Insufficient funds',
                ],
            ]),
        ]);

        $response = $this->get(route('payments.callback', [
            'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
        ]));

        $response->assertRedirect(route('bookings.payment', $this->booking));
        $response->assertSessionHas('error');

        $this->booking->refresh();
        $this->assertEquals('failed', $this->booking->payment_status);
    }

    public function test_payment_callback_handles_missing_reference(): void
    {
        $response = $this->get(route('payments.callback'));

        $response->assertRedirect(route('dashboard'));
        $response->assertSessionHas('error', 'Invalid payment reference.');
    }

    public function test_webhook_handles_successful_payment_event(): void
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'status' => 'success',
                'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
                'amount' => 110000, // 1100 * 100 (kobo)
                'metadata' => [
                    'booking_id' => $this->booking->id,
                ],
            ],
        ];

        // Mock the webhook signature verification
        $this->mock(PaystackService::class, function ($mock) {
            $mock->shouldReceive('verifyWebhookSignature')->andReturn(true);
            $mock->shouldReceive('processWebhook')->andReturn(true);
        });

        $response = $this->postJson(route('payments.webhook'), $payload, [
            'x-paystack-signature' => 'valid_signature',
        ]);

        $response->assertOk();
    }

    public function test_webhook_rejects_invalid_signature(): void
    {
        $this->mock(PaystackService::class, function ($mock) {
            $mock->shouldReceive('verifyWebhookSignature')->andReturn(false);
        });

        $response = $this->postJson(route('payments.webhook'), [], [
            'x-paystack-signature' => 'invalid_signature',
        ]);

        $response->assertStatus(400);
        $response->assertSeeText('Invalid signature');
    }

    public function test_payment_retry_resets_status(): void
    {
        $this->booking->update([
            'payment_status' => 'failed',
            'payment_reference' => 'old_reference',
        ]);

        $response = $this->actingAs($this->student)->get(route('payments.retry', $this->booking));

        $response->assertRedirect(route('payments.initialize', $this->booking));

        $this->booking->refresh();
        $this->assertEquals('pending', $this->booking->payment_status);
        $this->assertNull($this->booking->payment_reference);
    }

    public function test_payment_status_endpoint(): void
    {
        $response = $this->actingAs($this->student)->get(route('payments.status', $this->booking));

        $response->assertOk();
        $response->assertJsonStructure([
            'booking_id',
            'booking_reference',
            'payment_status',
            'amount_paid',
            'amount_due',
            'total_amount',
        ]);
    }

    public function test_admin_can_process_refund(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);
        $this->booking->update([
            'payment_status' => 'paid',
            'amount_paid' => 1100,
            'payment_reference' => 'HST_' . $this->booking->booking_reference . '_12345',
        ]);

        Http::fake([
            'api.paystack.co/refund' => Http::response([
                'status' => true,
                'data' => [
                    'status' => 'success',
                    'amount' => 110000, // Full refund in kobo
                ],
            ]),
        ]);

        $response = $this->actingAs($admin)->post(route('payments.refund', $this->booking), [
            'reason' => 'Student cancelled booking',
            'refund_amount' => 1100,
        ]);

        $response->assertRedirect(route('bookings.show', $this->booking));
        $response->assertSessionHas('success');

        $this->booking->refresh();
        $this->assertEquals('refunded', $this->booking->payment_status);
    }

    public function test_cannot_refund_unpaid_booking(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);

        $response = $this->actingAs($admin)->post(route('payments.refund', $this->booking), [
            'reason' => 'Test refund',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Only paid bookings can be refunded.');
    }

    public function test_refund_validates_amount(): void
    {
        $admin = User::factory()->create(['role' => 'hostel_admin']);
        $this->booking->update([
            'payment_status' => 'paid',
            'amount_paid' => 1100,
            'payment_reference' => 'test_ref',
        ]);

        $response = $this->actingAs($admin)->post(route('payments.refund', $this->booking), [
            'reason' => 'Test refund',
            'refund_amount' => 2000, // More than paid
        ]);

        $response->assertSessionHasErrors(['refund_amount']);
    }

    public function test_student_cannot_process_refund(): void
    {
        $this->booking->update([
            'payment_status' => 'paid',
            'payment_reference' => 'test_ref',
        ]);

        $response = $this->actingAs($this->student)->post(route('payments.refund', $this->booking), [
            'reason' => 'Test refund',
        ]);

        $response->assertForbidden();
    }

    public function test_public_key_endpoint(): void
    {
        $response = $this->get(route('api.payments.public-key'));

        $response->assertOk();
        $response->assertJsonStructure(['public_key']);
    }

    public function test_deposit_payment_calculation(): void
    {
        $this->booking->update(['payment_type' => 'deposit']);

        Http::fake([
            'api.paystack.co/transaction/initialize' => Http::response([
                'status' => true,
                'data' => [
                    'authorization_url' => 'https://checkout.paystack.com/abc123',
                    'access_code' => 'abc123',
                    'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
                ],
            ]),
        ]);

        // Simulate successful deposit payment callback
        Http::fake([
            'api.paystack.co/transaction/verify/*' => Http::response([
                'status' => true,
                'data' => [
                    'status' => 'success',
                    'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
                    'amount' => 33000, // 30% of 1100 = 330 in kobo
                    'metadata' => [
                        'booking_id' => $this->booking->id,
                    ],
                ],
            ]),
        ]);

        $response = $this->get(route('payments.callback', [
            'reference' => 'HST_' . $this->booking->booking_reference . '_12345',
        ]));

        $this->booking->refresh();
        $this->assertEquals('partially_paid', $this->booking->payment_status);
        $this->assertEquals(330, $this->booking->amount_paid);
        $this->assertEquals(770, $this->booking->amount_due);
    }
} 