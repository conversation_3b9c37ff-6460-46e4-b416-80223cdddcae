# Inertia.js Integration Patterns

## 🔄 Laravel-React Integration

### Controller Response Patterns
Always use Inertia::render() for page responses:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Hostel;
use Illuminate\Http\Request;
use Inertia\Inertia;

class HostelController extends Controller
{
    public function index(Request $request)
    {
        $hostels = Hostel::query()
            ->when($request->search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%");
            })
            ->with(['user', 'rooms'])
            ->paginate(12)
            ->withQueryString();

        return Inertia::render('Hostels/Index', [
            'hostels' => $hostels,
            'filters' => $request->only(['search', 'category', 'city']),
            'can' => [
                'create_hostel' => auth()->user()?->can('create', Hostel::class),
            ]
        ]);
    }

    public function show(Hostel $hostel)
    {
        $hostel->load(['rooms.roomType', 'rooms.beds', 'user']);

        return Inertia::render('Hostels/Show', [
            'hostel' => $hostel,
            'can' => [
                'update' => auth()->user()?->can('update', $hostel),
                'delete' => auth()->user()?->can('delete', $hostel),
            ]
        ]);
    }
}
```

### Form Handling with Inertia
Use Inertia's form helper for form submissions:

```tsx
import { useForm } from '@inertiajs/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface HostelFormData {
  name: string
  description: string
  address: string
  city: string
  state: string
  country: string
  category: 'budget' | 'mid-range' | 'luxury'
  amenities: string[]
  is_published: boolean
}

export function CreateHostelForm() {
  const { data, setData, post, processing, errors, reset } = useForm<HostelFormData>({
    name: '',
    description: '',
    address: '',
    city: '',
    state: '',
    country: '',
    category: 'budget',
    amenities: [],
    is_published: false
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    post(route('hostels.store'), {
      onSuccess: () => {
        reset()
      },
      onError: (errors) => {
        // Handle form submission errors
      }
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="name">Hostel Name</Label>
        <Input
          id="name"
          value={data.name}
          onChange={(e) => setData('name', e.target.value)}
          className={errors.name ? 'border-destructive' : ''}
        />
        {errors.name && (
          <p className="text-sm text-destructive">{errors.name}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={data.description}
          onChange={(e) => setData('description', e.target.value)}
          className={errors.description ? 'border-destructive' : ''}
          rows={4}
        />
        {errors.description && (
          <p className="text-sm text-destructive">{errors.description}</p>
        )}
      </div>

      <Button type="submit" disabled={processing} className="w-full">
        {processing ? 'Creating...' : 'Create Hostel'}
      </Button>
    </form>
  )
}
```

## 📝 TypeScript Definitions

### Page Props Interface
Define consistent page props interfaces:

```typescript
// resources/js/types/index.d.ts

export interface User {
  id: number
  name: string
  email: string
  email_verified_at: string | null
  phone: string | null
  date_of_birth: string | null
  gender: 'male' | 'female' | 'other' | null
  university: string | null
  student_id: string | null
  created_at: string
  updated_at: string
  roles: Role[]
}

export interface Role {
  id: number
  name: string
  guard_name: string
}

export interface Hostel {
  id: number
  name: string
  description: string
  address: string
  city: string
  state: string
  country: string
  phone: string | null
  email: string | null
  website: string | null
  category: 'budget' | 'mid-range' | 'luxury'
  amenities: string[]
  images: string[]
  is_published: boolean
  slug: string
  user_id: number
  created_at: string
  updated_at: string
  user?: User
  rooms?: Room[]
  rooms_count?: number
}

export interface Room {
  id: number
  hostel_id: number
  room_type_id: number
  room_number: string
  floor: number
  is_available: boolean
  created_at: string
  updated_at: string
  hostel?: Hostel
  room_type?: RoomType
  beds?: Bed[]
  beds_count?: number
}

export interface RoomType {
  id: number
  name: string
  description: string
  capacity: number
  price_per_night: number
  amenities: string[]
  created_at: string
  updated_at: string
}

export interface Bed {
  id: number
  room_id: number
  bed_number: string
  bed_type: 'single' | 'double' | 'bunk'
  is_available: boolean
  created_at: string
  updated_at: string
  room?: Room
}

export interface Booking {
  id: number
  user_id: number
  hostel_id: number
  room_id: number
  bed_id: number | null
  check_in: string
  check_out: string
  guests: number
  total_amount: number
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed'
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  special_requests: string | null
  created_at: string
  updated_at: string
  user?: User
  hostel?: Hostel
  room?: Room
  bed?: Bed
}

export interface PaginatedData<T> {
  data: T[]
  links: {
    first: string | null
    last: string | null
    prev: string | null
    next: string | null
  }
  meta: {
    current_page: number
    from: number
    last_page: number
    path: string
    per_page: number
    to: number
    total: number
  }
}

export interface PageProps<T extends Record<string, unknown> = Record<string, unknown>> {
  auth: {
    user: User | null
  }
  flash: {
    success?: string
    error?: string
    info?: string
    warning?: string
  }
  can?: Record<string, boolean>
  errors: Record<string, string>
} & T
```

### Component Props with Page Data
Use proper typing for page components:

```tsx
import { Head } from '@inertiajs/react'
import { PageProps, Hostel, PaginatedData } from '@/types'
import { AppLayout } from '@/layouts/app-layout'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface HostelIndexProps extends PageProps {
  hostels: PaginatedData<Hostel>
  filters: {
    search?: string
    category?: string
    city?: string
  }
}

export default function HostelIndex({ hostels, filters, can }: HostelIndexProps) {
  return (
    <AppLayout>
      <Head title="Hostels" />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Hostels</h1>
          {can?.create_hostel && (
            <Button href={route('hostels.create')}>
              Add New Hostel
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {hostels.data.map((hostel) => (
            <Card key={hostel.id} className="overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <h3 className="text-lg font-semibold">{hostel.name}</h3>
                  <Badge variant="secondary">
                    {hostel.category}
                  </Badge>
                </div>
                <p className="mt-2 text-sm text-muted-foreground">
                  {hostel.city}, {hostel.state}
                </p>
                <p className="mt-2 text-sm line-clamp-2">
                  {hostel.description}
                </p>
                <div className="mt-4 flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {hostel.rooms_count} rooms
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    href={route('hostels.show', hostel.slug)}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </AppLayout>
  )
}
```

## 🔄 Data Fetching Patterns

### Search and Filtering
Implement search with URL state management:

```tsx
import { useState, useEffect } from 'react'
import { router } from '@inertiajs/react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useDebouncedValue } from '@/hooks/use-debounced-value'

interface SearchFiltersProps {
  filters: {
    search?: string
    category?: string
    city?: string
  }
}

export function SearchFilters({ filters }: SearchFiltersProps) {
  const [search, setSearch] = useState(filters.search || '')
  const [category, setCategory] = useState(filters.category || '')
  const [city, setCity] = useState(filters.city || '')

  const debouncedSearch = useDebouncedValue(search, 300)

  useEffect(() => {
    const params = new URLSearchParams()
    
    if (debouncedSearch) params.set('search', debouncedSearch)
    if (category) params.set('category', category)
    if (city) params.set('city', city)

    router.get(route('hostels.index'), Object.fromEntries(params), {
      preserveState: true,
      preserveScroll: true,
      replace: true
    })
  }, [debouncedSearch, category, city])

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center">
      <div className="flex-1">
        <Input
          placeholder="Search hostels..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-sm"
        />
      </div>
      
      <Select value={category} onValueChange={setCategory}>
        <SelectTrigger className="w-full md:w-48">
          <SelectValue placeholder="Category" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">All Categories</SelectItem>
          <SelectItem value="budget">Budget</SelectItem>
          <SelectItem value="mid-range">Mid-range</SelectItem>
          <SelectItem value="luxury">Luxury</SelectItem>
        </SelectContent>
      </Select>
      
      <Select value={city} onValueChange={setCity}>
        <SelectTrigger className="w-full md:w-48">
          <SelectValue placeholder="City" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">All Cities</SelectItem>
          <SelectItem value="accra">Accra</SelectItem>
          <SelectItem value="kumasi">Kumasi</SelectItem>
          <SelectItem value="cape-coast">Cape Coast</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}
```

### Pagination Component
Create reusable pagination with Inertia:

```tsx
import { Link } from '@inertiajs/react'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface PaginationProps {
  links: {
    first: string | null
    last: string | null
    prev: string | null
    next: string | null
  }
  meta: {
    current_page: number
    from: number
    last_page: number
    path: string
    per_page: number
    to: number
    total: number
  }
}

export function Pagination({ links, meta }: PaginationProps) {
  if (meta.last_page <= 1) return null

  return (
    <div className="flex items-center justify-between">
      <div className="text-sm text-muted-foreground">
        Showing {meta.from} to {meta.to} of {meta.total} results
      </div>
      
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          disabled={!links.prev}
          asChild={!!links.prev}
        >
          {links.prev ? (
            <Link href={links.prev} preserveScroll>
              <ChevronLeft className="size-4" />
              Previous
            </Link>
          ) : (
            <>
              <ChevronLeft className="size-4" />
              Previous
            </>
          )}
        </Button>
        
        <span className="text-sm text-muted-foreground">
          Page {meta.current_page} of {meta.last_page}
        </span>
        
        <Button
          variant="outline"
          size="sm"
          disabled={!links.next}
          asChild={!!links.next}
        >
          {links.next ? (
            <Link href={links.next} preserveScroll>
              Next
              <ChevronRight className="size-4" />
            </Link>
          ) : (
            <>
              Next
              <ChevronRight className="size-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
```

## 🔄 Real-time Features

### Flash Messages
Handle Laravel flash messages in React:

```tsx
import { useEffect } from 'react'
import { usePage } from '@inertiajs/react'
import { toast } from 'sonner'
import { PageProps } from '@/types'

export function FlashMessages() {
  const { flash } = usePage<PageProps>().props

  useEffect(() => {
    if (flash.success) {
      toast.success(flash.success)
    }
    if (flash.error) {
      toast.error(flash.error)
    }
    if (flash.info) {
      toast.info(flash.info)
    }
    if (flash.warning) {
      toast.warning(flash.warning)
    }
  }, [flash])

  return null
}
```

### Error Boundary
Create error boundaries for better UX:

```tsx
import { Component, ErrorInfo, ReactNode } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertTriangle } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Error caught by boundary - handle appropriately
  }

  public render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Card className="p-6 text-center">
          <AlertTriangle className="mx-auto size-12 text-destructive" />
          <h2 className="mt-4 text-lg font-semibold">Something went wrong</h2>
          <p className="mt-2 text-sm text-muted-foreground">
            {this.state.error?.message || 'An unexpected error occurred'}
          </p>
          <Button
            onClick={() => this.setState({ hasError: false })}
            className="mt-4"
          >
            Try again
          </Button>
        </Card>
      )
    }

    return this.props.children
  }
}
```

## 🔧 Custom Hooks

### Debounced Value Hook
Create reusable debounced value hook:

```tsx
import { useState, useEffect } from 'react'

export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}
```

### Permission Hook
Create hook for checking permissions:

```tsx
import { usePage } from '@inertiajs/react'
import { PageProps } from '@/types'

export function usePermissions() {
  const { can } = usePage<PageProps>().props

  return {
    can: (permission: string) => can?.[permission] ?? false,
    canAny: (permissions: string[]) => permissions.some(p => can?.[p] ?? false),
    canAll: (permissions: string[]) => permissions.every(p => can?.[p] ?? false)
  }
}
```

## 📝 Best Practices

1. **Always type your page props** with proper interfaces
2. **Use Inertia's form helper** for form submissions
3. **Handle loading states** with proper UI feedback
4. **Implement error boundaries** for robust error handling
5. **Use debounced search** to avoid excessive API calls
6. **Preserve scroll position** when filtering/searching
7. **Handle flash messages** consistently across the app
8. **Use proper TypeScript** for all components and hooks
9. **Implement proper SEO** with Head component
10. **Follow the design system** for consistent UI
description:
globs:
alwaysApply: false
---
