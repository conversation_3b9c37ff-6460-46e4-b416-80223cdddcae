# Laravel Backend Development Rules

## 🏗️ Architecture Patterns

### Controller Structure
Follow RESTful conventions and keep controllers thin:

```php
<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreHostelRequest;
use App\Http\Requests\UpdateHostelRequest;
use App\Models\Hostel;
use App\Services\HostelService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class HostelController extends Controller
{
    public function __construct(
        private HostelService $hostelService
    ) {}

    public function index(Request $request)
    {
        $hostels = $this->hostelService->getPaginatedHostels($request);
        
        return Inertia::render('Hostels/Index', [
            'hostels' => $hostels,
            'filters' => $request->only(['search', 'category'])
        ]);
    }

    public function store(StoreHostelRequest $request)
    {
        $hostel = $this->hostelService->createHostel($request->validated());
        
        return redirect()
            ->route('hostels.show', $hostel)
            ->with('success', 'Hostel created successfully.');
    }

    public function show(Hostel $hostel)
    {
        $hostel->load(['rooms.roomType', 'rooms.beds']);
        
        return Inertia::render('Hostels/Show', [
            'hostel' => $hostel
        ]);
    }

    public function update(UpdateHostelRequest $request, Hostel $hostel)
    {
        $this->authorize('update', $hostel);
        
        $hostel = $this->hostelService->updateHostel($hostel, $request->validated());
        
        return redirect()
            ->route('hostels.show', $hostel)
            ->with('success', 'Hostel updated successfully.');
    }

    public function destroy(Hostel $hostel)
    {
        $this->authorize('delete', $hostel);
        
        $this->hostelService->deleteHostel($hostel);
        
        return redirect()
            ->route('hostels.index')
            ->with('success', 'Hostel deleted successfully.');
    }
}
```

### Service Layer Pattern
Extract business logic into service classes:

```php
<?php

namespace App\Services;

use App\Models\Hostel;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class HostelService
{
    public function getPaginatedHostels(Request $request): LengthAwarePaginator
    {
        return Hostel::query()
            ->when($request->search, function (Builder $query, string $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
            })
            ->when($request->category, function (Builder $query, string $category) {
                $query->where('category', $category);
            })
            ->with(['user', 'rooms'])
            ->latest()
            ->paginate(12)
            ->withQueryString();
    }

    public function createHostel(array $data): Hostel
    {
        return Hostel::create([
            ...$data,
            'user_id' => auth()->id(),
            'slug' => str($data['name'])->slug()
        ]);
    }

    public function updateHostel(Hostel $hostel, array $data): Hostel
    {
        $hostel->update([
            ...$data,
            'slug' => str($data['name'])->slug()
        ]);

        return $hostel->fresh();
    }

    public function deleteHostel(Hostel $hostel): bool
    {
        return $hostel->delete();
    }
}
```

## 🔐 Authentication & Authorization

### Policy-Based Authorization
Use Laravel Policies for authorization logic:

```php
<?php

namespace App\Policies;

use App\Models\Hostel;
use App\Models\User;

class HostelPolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Hostel $hostel): bool
    {
        return $hostel->is_published || $user->id === $hostel->user_id;
    }

    public function create(User $user): bool
    {
        return $user->hasRole('hostel_admin');
    }

    public function update(User $user, Hostel $hostel): bool
    {
        return $user->id === $hostel->user_id || $user->hasRole('admin');
    }

    public function delete(User $user, Hostel $hostel): bool
    {
        return $user->id === $hostel->user_id || $user->hasRole('admin');
    }
}
```

### Role-Based Middleware
Create middleware for role-based access control:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        if (!$user->hasAnyRole($roles)) {
            abort(403, 'Unauthorized access.');
        }

        return $next($request);
    }
}
```

## 🗃️ Database Patterns

### Model Relationships
Define clear and efficient relationships:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Hostel extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'address',
        'city',
        'state',
        'country',
        'phone',
        'email',
        'website',
        'category',
        'amenities',
        'images',
        'is_published',
        'user_id',
        'slug'
    ];

    protected $casts = [
        'amenities' => 'array',
        'images' => 'array',
        'is_published' => 'boolean'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function rooms(): HasMany
    {
        return $this->hasMany(Room::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    // Accessors & Mutators
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
```

### Migration Best Practices
Write clear and reversible migrations:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('hostels', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('address');
            $table->string('city');
            $table->string('state');
            $table->string('country');
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('website')->nullable();
            $table->string('category');
            $table->json('amenities')->nullable();
            $table->json('images')->nullable();
            $table->boolean('is_published')->default(false);
            $table->string('slug')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['is_published', 'created_at']);
            $table->index(['category', 'city']);
            $table->index('slug');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('hostels');
    }
};
```

## 📝 Request Validation

### Form Request Classes
Use Form Request classes for validation:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreHostelRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasRole('hostel_admin');
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:2000'],
            'address' => ['required', 'string', 'max:500'],
            'city' => ['required', 'string', 'max:100'],
            'state' => ['required', 'string', 'max:100'],
            'country' => ['required', 'string', 'max:100'],
            'phone' => ['nullable', 'string', 'max:20'],
            'email' => ['nullable', 'email', 'max:255'],
            'website' => ['nullable', 'url', 'max:255'],
            'category' => ['required', Rule::in(['budget', 'mid-range', 'luxury'])],
            'amenities' => ['nullable', 'array'],
            'amenities.*' => ['string', 'max:100'],
            'images' => ['nullable', 'array', 'max:10'],
            'images.*' => ['image', 'mimes:jpeg,png,jpg,webp', 'max:51200'], // 50MB
            'is_published' => ['boolean']
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Hostel name is required.',
            'description.required' => 'Please provide a description of your hostel.',
            'category.in' => 'Please select a valid category.',
            'images.*.image' => 'Only image files are allowed.',
            'images.*.max' => 'Each image must be less than 50MB.'
        ];
    }
}
```

## 🔄 API Resources

### Resource Classes for API Responses
Use Eloquent API Resources for consistent API responses:

```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HostelResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
            'phone' => $this->phone,
            'email' => $this->email,
            'website' => $this->website,
            'category' => $this->category,
            'amenities' => $this->amenities,
            'images' => $this->images,
            'is_published' => $this->is_published,
            'slug' => $this->slug,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relationships
            'owner' => new UserResource($this->whenLoaded('user')),
            'rooms' => RoomResource::collection($this->whenLoaded('rooms')),
            'rooms_count' => $this->whenCounted('rooms'),
            'available_beds' => $this->when(
                $this->relationLoaded('rooms'),
                fn () => $this->rooms->sum('beds_count')
            )
        ];
    }
}
```

## 🧪 Testing Patterns

### Feature Tests
Write comprehensive feature tests:

```php
<?php

namespace Tests\Feature;

use App\Models\Hostel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HostelManagementTest extends TestCase
{
    use RefreshDatabase;

    public function test_hostel_admin_can_create_hostel(): void
    {
        $user = User::factory()->create();
        $user->assignRole('hostel_admin');

        $hostelData = [
            'name' => 'Test Hostel',
            'description' => 'A great place to stay',
            'address' => '123 Test Street',
            'city' => 'Test City',
            'state' => 'Test State',
            'country' => 'Test Country',
            'category' => 'budget',
            'is_published' => true
        ];

        $response = $this->actingAs($user)
            ->post(route('hostels.store'), $hostelData);

        $response->assertRedirect();
        $this->assertDatabaseHas('hostels', [
            'name' => 'Test Hostel',
            'user_id' => $user->id
        ]);
    }

    public function test_regular_user_cannot_create_hostel(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->post(route('hostels.store'), []);

        $response->assertForbidden();
    }

    public function test_hostel_owner_can_update_their_hostel(): void
    {
        $user = User::factory()->create();
        $user->assignRole('hostel_admin');
        
        $hostel = Hostel::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)
            ->patch(route('hostels.update', $hostel), [
                'name' => 'Updated Hostel Name'
            ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('hostels', [
            'id' => $hostel->id,
            'name' => 'Updated Hostel Name'
        ]);
    }
}
```

## 🔧 Configuration & Environment

### Service Configuration
Configure services properly in `config/services.php`:

```php
<?php

return [
    'paystack' => [
        'public_key' => env('PAYSTACK_PUBLIC_KEY'),
        'secret_key' => env('PAYSTACK_SECRET_KEY'),
        'payment_url' => env('PAYSTACK_PAYMENT_URL', 'https://api.paystack.co'),
        'merchant_email' => env('PAYSTACK_MERCHANT_EMAIL'),
    ],

    'mail' => [
        'from' => [
            'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
            'name' => env('MAIL_FROM_NAME', 'OSDAN'),
        ],
    ],
];
```

## 📊 Performance Optimization

### Database Query Optimization
Use eager loading and query optimization:

```php
// ✅ Good - Eager loading to prevent N+1 queries
$hostels = Hostel::with(['user', 'rooms.roomType'])
    ->published()
    ->latest()
    ->paginate(12);

// ✅ Good - Select only needed columns
$hostels = Hostel::select(['id', 'name', 'city', 'category', 'created_at'])
    ->published()
    ->paginate(12);

// ✅ Good - Use database-level filtering
$availableHostels = Hostel::whereHas('rooms.beds', function ($query) {
    $query->where('is_available', true);
})->get();

// ❌ Bad - N+1 query problem
$hostels = Hostel::all();
foreach ($hostels as $hostel) {
    echo $hostel->user->name; // This creates N+1 queries
}
```

### Caching Strategies
Implement proper caching:

```php
<?php

namespace App\Services;

use App\Models\Hostel;
use Illuminate\Support\Facades\Cache;

class HostelService
{
    public function getFeaturedHostels(): Collection
    {
        return Cache::remember('featured_hostels', 3600, function () {
            return Hostel::published()
                ->where('is_featured', true)
                ->with(['user', 'rooms'])
                ->take(6)
                ->get();
        });
    }

    public function getHostelStats(): array
    {
        return Cache::remember('hostel_stats', 1800, function () {
            return [
                'total_hostels' => Hostel::published()->count(),
                'total_beds' => Hostel::published()->withCount('beds')->sum('beds_count'),
                'cities_count' => Hostel::published()->distinct('city')->count(),
            ];
        });
    }
}
```

## 🛡️ Security Best Practices

### Input Sanitization
Always sanitize and validate input:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBookingRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'check_in' => ['required', 'date', 'after:today'],
            'check_out' => ['required', 'date', 'after:check_in'],
            'guests' => ['required', 'integer', 'min:1', 'max:10'],
            'special_requests' => ['nullable', 'string', 'max:500'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'special_requests' => strip_tags($this->special_requests),
        ]);
    }
}
```

### Rate Limiting
Implement rate limiting for API endpoints:

```php
// In routes/api.php
Route::middleware(['throttle:60,1'])->group(function () {
    Route::post('/bookings', [BookingController::class, 'store']);
    Route::post('/payments', [PaymentController::class, 'process']);
});

// Custom rate limiting in controller
public function store(Request $request)
{
    RateLimiter::hit('booking:' . $request->ip(), 300); // 5 minutes
    
    if (RateLimiter::tooManyAttempts('booking:' . $request->ip(), 3)) {
        return response()->json(['error' => 'Too many booking attempts'], 429);
    }
    
    // Process booking...
}
```

## 📝 Best Practices Summary

1. **Keep controllers thin** - Move business logic to services
2. **Use Form Requests** for validation and authorization
3. **Implement proper authorization** with Policies
4. **Write comprehensive tests** for all features
5. **Use Eloquent relationships** efficiently with eager loading
6. **Implement caching** for frequently accessed data
7. **Follow PSR standards** for code formatting
8. **Use meaningful variable names** and add docblocks
9. **Handle exceptions gracefully** with proper error responses
10. **Keep sensitive data in environment variables**
description:
globs:
alwaysApply: false
---
