# Component Implementation Patterns

## 🔧 React Component Structure

### Component Organization
```tsx
// 1. Imports (external libraries first, then internal)
import React from 'react'
import { cn } from '@/lib/utils'

// 2. Type definitions
interface ComponentProps {
  className?: string
  children?: React.ReactNode
}

// 3. Component implementation
export function Component({ className, children, ...props }: ComponentProps) {
  return (
    <div className={cn("base-classes", className)} {...props}>
      {children}
    </div>
  )
}
```

### Variant Patterns
Use `cva` (Class Variance Authority) for component variants:

```tsx
import { cva, type VariantProps } from 'class-variance-authority'

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-full text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-white hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline"
      },
      size: {
        default: "h-10 px-5 py-2.5",
        sm: "h-9 px-4 py-2",
        lg: "h-11 px-6 py-3",
        icon: "size-10 rounded-full"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
)

interface ButtonProps 
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
```

## 🎨 Styling Patterns

### Consistent Class Names
Always use the design system classes from [components-ui.md](mdc:components-ui.md):

```tsx
// ✅ Good - Using design system classes
<div className="rounded-xl bg-card text-card-foreground p-6 shadow-sm">
  <h3 className="text-lg font-semibold">Card Title</h3>
  <p className="text-sm text-muted-foreground">Description</p>
</div>

// ❌ Bad - Arbitrary values
<div className="rounded-[12px] bg-white p-[24px] shadow-[0_1px_3px_rgba(0,0,0,0.1)]">
  <h3 className="text-[18px] font-[600]">Card Title</h3>
  <p className="text-[14px] text-gray-500">Description</p>
</div>
```

### Layout Patterns
Use consistent layout patterns:

```tsx
// Card layout
<div className="flex flex-col gap-6 rounded-xl bg-card p-6 shadow-sm">
  <div className="flex items-center justify-between">
    <h3 className="text-lg font-semibold">Header</h3>
    <Button variant="ghost" size="icon">
      <Icon className="size-4" />
    </Button>
  </div>
  <div className="space-y-4">
    {/* Content */}
  </div>
</div>

// Form layout
<form className="space-y-6">
  <div className="space-y-2">
    <Label htmlFor="field">Field Label</Label>
    <Input id="field" placeholder="Enter value..." />
  </div>
  <Button type="submit" className="w-full">
    Submit
  </Button>
</form>
```

## 🔄 State Management

### Form Handling
Use React Hook Form with proper validation:

```tsx
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

const formSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters')
})

type FormData = z.infer<typeof formSchema>

export function LoginForm() {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: ''
    }
  })

  const onSubmit = (data: FormData) => {
    // Handle form submission
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          {...form.register('email')}
          aria-invalid={!!form.formState.errors.email}
        />
        {form.formState.errors.email && (
          <p className="text-sm text-destructive">
            {form.formState.errors.email.message}
          </p>
        )}
      </div>
      <Button type="submit" className="w-full">
        Sign In
      </Button>
    </form>
  )
}
```

## 🎯 Accessibility Patterns

### Focus Management
```tsx
// Proper focus management for dialogs
export function Dialog({ open, onClose, children }) {
  const dialogRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (open) {
      dialogRef.current?.focus()
    }
  }, [open])

  return (
    <div
      ref={dialogRef}
      role="dialog"
      aria-modal="true"
      aria-labelledby="dialog-title"
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/60"
      tabIndex={-1}
    >
      <div className="rounded-xl bg-card p-6 shadow-lg">
        {children}
      </div>
    </div>
  )
}
```

### Keyboard Navigation
```tsx
// Keyboard navigation for interactive elements
export function MenuButton() {
  const [isOpen, setIsOpen] = useState(false)

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      setIsOpen(!isOpen)
    }
    if (event.key === 'Escape') {
      setIsOpen(false)
    }
  }

  return (
    <div className="relative">
      <Button
        onClick={() => setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        aria-expanded={isOpen}
        aria-haspopup="menu"
      >
        Menu
      </Button>
      {isOpen && (
        <div role="menu" className="absolute top-full mt-2 rounded-md bg-card shadow-lg">
          {/* Menu items */}
        </div>
      )}
    </div>
  )
}
```

## 📱 Responsive Patterns

### Mobile-First Design
```tsx
// Responsive grid layout
<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
  {items.map((item) => (
    <Card key={item.id} className="p-4">
      <h3 className="text-base font-medium md:text-lg">{item.title}</h3>
      <p className="text-sm text-muted-foreground">{item.description}</p>
    </Card>
  ))}
</div>

// Responsive navigation
<nav className="flex flex-col gap-2 md:flex-row md:gap-4">
  <Button variant="ghost" className="justify-start md:justify-center">
    Dashboard
  </Button>
  <Button variant="ghost" className="justify-start md:justify-center">
    Settings
  </Button>
</nav>
```

## 🔧 Performance Patterns

### Lazy Loading
```tsx
import { lazy, Suspense } from 'react'

const LazyComponent = lazy(() => import('./LazyComponent'))

export function App() {
  return (
    <Suspense fallback={<div className="animate-pulse">Loading...</div>}>
      <LazyComponent />
    </Suspense>
  )
}
```

### Memoization
```tsx
import { memo, useMemo } from 'react'

// Memoize expensive calculations
export const ExpensiveComponent = memo(function ExpensiveComponent({ data }) {
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      processed: expensiveOperation(item)
    }))
  }, [data])

  return (
    <div>
      {processedData.map(item => (
        <div key={item.id}>{item.processed}</div>
      ))}
    </div>
  )
})
```

## 🎨 Animation Patterns

### Smooth Transitions
```tsx
// Use consistent transition classes
<div className="transform transition-all duration-300 ease-in-out hover:scale-105">
  <Card className="hover:shadow-lg">
    Content
  </Card>
</div>

// Loading states
<Button disabled={isLoading} className="relative">
  {isLoading && (
    <div className="absolute inset-0 flex items-center justify-center">
      <div className="size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
    </div>
  )}
  <span className={isLoading ? 'opacity-0' : 'opacity-100'}>
    Submit
  </span>
</Button>
```

## 📝 Best Practices

1. **Always** use TypeScript for type safety
2. **Forward refs** for reusable components
3. **Use semantic HTML** elements when possible
4. **Include proper ARIA attributes** for accessibility
5. **Test components** across different screen sizes
6. **Follow the design system** color and spacing guidelines
7. **Use consistent naming conventions** for props and classes
8. **Implement proper error boundaries** for robust UX
description:
globs:
alwaysApply: false
---
