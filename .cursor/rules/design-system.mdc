## 🎨 Design Philosophy

Follow these core principles for all UI components and pages:

- **Minimalist aesthetic** with purposeful use of white space
- **Consistent visual hierarchy** through typography and spacing
- **Subtle animations and transitions** for enhanced user experience
- **Mobile-first responsive design** that works across all devices
- **High contrast ratios** for accessibility compliance

## 🌈 Color System

### Primary Colors (OKLCH)
Use these exact color values from [components-ui.md](mdc:components-ui.md):

```css
--primary: oklch(0.55 0.22 265);           /* Vibrant blue - main brand color */
--primary-foreground: oklch(0.985 0 0);    /* Near-white text on primary */
--secondary: oklch(0.92 0.08 265);         /* Light purple/lavender */
--secondary-foreground: oklch(0.145 0 0);  /* Dark text on secondary */
--accent: oklch(0.85 0.12 265);            /* Medium purple for highlights */
--destructive: oklch(0.65 0.28 25);        /* Vibrant red for errors */
```

### Sidebar Colors
```css
--sidebar: #f7f8fc;                       /* Light blue-gray sidebar background */
--sidebar-foreground: #44474e;            /* Dark gray sidebar text */
--sidebar-primary: #6750a4;               /* Purple sidebar primary elements */
```

## 🔤 Typography

### Font Family
Always use **Urbanist** as the primary font:
```css
font-family: 'Urbanist', ui-sans-serif, system-ui, sans-serif;
```

### Typography Scale
Use consistent typography classes:
- `text-7xl` (72px) - Hero headings
- `text-6xl` (60px) - Main headings  
- `text-5xl` (48px) - Section headings
- `text-4xl` (36px) - Subsection headings
- `text-3xl` (30px) - Card titles
- `text-2xl` (24px) - Component titles
- `text-xl` (20px) - Large text
- `text-lg` (18px) - Large body text
- `text-base` (16px) - Default body text
- `text-sm` (14px) - Small text
- `text-xs` (12px) - Captions

## 📐 Spacing & Layout

### Border Radius
Use consistent border radius values:
- `rounded-xl` (12px) - Primary border radius for cards
- `rounded-md` (6px) - Medium radius for inputs
- `rounded-full` - For buttons and circular elements

### Spacing Scale
Follow the 4px base spacing system:
- `gap-1` (4px) - Tight spacing
- `gap-2` (8px) - Default spacing
- `gap-4` (16px) - Comfortable spacing
- `gap-6` (24px) - Generous spacing
- `gap-8` (32px) - Section spacing
- `gap-12` (48px) - Large section spacing

## 🎯 Component Guidelines

### Buttons
- Always use `rounded-full` for pill-shaped buttons
- Standard height: `h-10` (40px)
- Include `transition-colors` for smooth hover effects
- Use `focus-visible:ring-ring/50 focus-visible:ring-[3px]` for accessibility

### Cards
- Use `rounded-xl` corners and `py-6` padding
- Apply `bg-card text-card-foreground` for consistent theming
- Include `flex flex-col gap-6` for content layout
- Add subtle shadows with `shadow-sm` or `shadow`

### Inputs
- Standard height: `h-9` (36px)
- Use `rounded-md border border-input` styling
- Include focus states: `focus-visible:border-ring focus-visible:ring-ring/50`
- Add error states: `aria-invalid:ring-destructive/20 aria-invalid:border-destructive`

### Sidebar
- Desktop width: `w-64` (256px)
- Mobile width: `w-72` (288px)
- Use `transition-[width] duration-200 ease-linear` for smooth animations
- Background: `bg-[#f7f8fc]` (light blue-gray)

## 🎨 Visual Effects

### Shadows
Use subtle shadows for depth:
- `shadow-xs` - Minimal depth
- `shadow-sm` - Light elevation
- `shadow` - Standard elevation
- `shadow-md` - Medium elevation
- `shadow-lg` - High elevation

### Transitions
Always include smooth transitions:
- `transition-colors` - For color changes
- `transition-transform` - For scale/movement
- `duration-300` - Standard timing (300ms)
- `ease-in-out` - Smooth easing

## 📱 Responsive Design

### Breakpoints
Use mobile-first approach:
- `sm:` (640px) - Small devices
- `md:` (768px) - Medium devices  
- `lg:` (1024px) - Large devices
- `xl:` (1280px) - Extra large devices

### Grid Patterns
- `grid-cols-1` - Mobile single column
- `md:grid-cols-2` - Medium two columns
- `lg:grid-cols-3` - Large three columns

## 🎯 Accessibility

### Focus Management
- Always include `focus-visible:ring-ring/50 focus-visible:ring-[3px]`
- Use `sr-only` for screen reader only content
- Ensure proper color contrast ratios

### Interactive States
- `disabled:pointer-events-none disabled:opacity-50` for disabled elements
- `aria-invalid` states for form validation
- Proper ARIA labels and descriptions

## 🔧 Implementation Rules

1. **Always** use Tailwind CSS classes following this design system
2. **Never** use arbitrary values unless absolutely necessary
3. **Prefer** composition over custom CSS
4. **Include** proper TypeScript types for all props
5. **Test** components across all breakpoints
6. **Ensure** accessibility compliance with WCAG AA standards

Reference the complete design system documentation in [components-ui.md](mdc:components-ui.md) for detailed specifications.
description:
globs:
alwaysApply: false
---
