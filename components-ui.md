## 🎨 Design Philosophy

The UI follows a **clean, modern, and accessible** design approach with:
- **Minimalist aesthetic** with purposeful use of white space
- **Consistent visual hierarchy** through typography and spacing
- **Subtle animations and transitions** for enhanced user experience
- **Mobile-first responsive design** that works across all devices
- **High contrast ratios** for accessibility compliance

## 🌈 Color Palette

### Primary Colors (OKLCH Color Space)
The system uses the modern OKLCH color space for better color consistency and accessibility:

```css
/* Primary Brand Colors */
--primary: oklch(0.55 0.22 265);           /* Vibrant blue - main brand color */
--primary-foreground: oklch(0.985 0 0);    /* Near-white text on primary */

/* Secondary Colors */
--secondary: oklch(0.92 0.08 265);         /* Light purple/lavender */
--secondary-foreground: oklch(0.145 0 0);  /* Dark text on secondary */

/* Accent Colors */
--accent: oklch(0.85 0.12 265);            /* Medium purple for highlights */
--accent-foreground: oklch(0.145 0 0);     /* Dark text on accent */

/* Destructive/Error Colors */
--destructive: oklch(0.65 0.28 25);        /* Vibrant red for errors */
--destructive-foreground: oklch(0.985 0 0); /* White text on destructive */
```

### Neutral Colors
```css
/* Background & Surface Colors */
--background: oklch(1 0 0);                /* Pure white background */
--foreground: oklch(0.145 0 0);           /* Near-black text */
--card: oklch(1 0 0);                     /* White card backgrounds */
--card-foreground: oklch(0.145 0 0);      /* Dark text on cards */

/* Muted/Subtle Colors */
--muted: oklch(0.97 0.02 265);            /* Very light purple-tinted background */
--muted-foreground: oklch(0.45 0.05 265); /* Medium gray text */

/* Border & Input Colors */
--border: oklch(0.9 0.02 265);            /* Light purple-tinted borders */
--input: oklch(0.9 0.02 265);             /* Input field borders */
--ring: oklch(0.75 0.1 265);              /* Focus ring color */
```

### Sidebar Colors
```css
/* Sidebar Specific Colors */
--sidebar: #f7f8fc;                       /* Light blue-gray sidebar background */
--sidebar-foreground: #44474e;            /* Dark gray sidebar text */
--sidebar-primary: #6750a4;               /* Purple sidebar primary elements */
--sidebar-accent: #c4caf9;                /* Light purple sidebar accents */
--sidebar-border: #b6c4ff;                /* Light blue sidebar borders */
```

### Chart Colors
```css
/* Data Visualization Colors */
--chart-1: oklch(0.6 0.22 265);           /* Primary blue for charts */
--chart-2: oklch(0.65 0.18 220);          /* Secondary blue */
--chart-3: oklch(0.7 0.15 290);           /* Purple */
--chart-4: oklch(0.75 0.12 180);          /* Teal */
--chart-5: oklch(0.8 0.15 140);           /* Green */
```

## 🔤 Typography

### Font Family
```css
--font-sans: 'Urbanist', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
```

**Urbanist** is the primary font - a modern, geometric sans-serif that provides:
- Excellent readability at all sizes
- 9 weight variations (100-900)
- Support for both regular and italic styles
- Optimized for web with variable font technology

### Typography Scale
The system uses a consistent typography scale with proper line heights and spacing:

```css
/* Heading Styles */
.text-7xl { font-size: 4.5rem; line-height: 1; }      /* 72px - Hero headings */
.text-6xl { font-size: 3.75rem; line-height: 1; }     /* 60px - Main headings */
.text-5xl { font-size: 3rem; line-height: 1; }        /* 48px - Section headings */
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; } /* 36px - Subsection headings */
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; } /* 30px - Card titles */
.text-2xl { font-size: 1.5rem; line-height: 2rem; }   /* 24px - Component titles */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; } /* 20px - Large text */

/* Body Text */
.text-lg { font-size: 1.125rem; line-height: 1.75rem; } /* 18px - Large body */
.text-base { font-size: 1rem; line-height: 1.5rem; }   /* 16px - Default body */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; } /* 14px - Small text */
.text-xs { font-size: 0.75rem; line-height: 1rem; }    /* 12px - Captions */
```

### Font Weights
```css
.font-black { font-weight: 900; }      /* Extra bold for impact */
.font-extrabold { font-weight: 800; }  /* Very bold headings */
.font-bold { font-weight: 700; }       /* Bold headings */
.font-semibold { font-weight: 600; }   /* Semi-bold subheadings */
.font-medium { font-weight: 500; }     /* Medium weight for emphasis */
.font-normal { font-weight: 400; }     /* Regular body text */
.font-light { font-weight: 300; }      /* Light text for quotes */
```

## 📐 Spacing System

### Border Radius
```css
--radius: 0.75rem;                     /* 12px - Primary border radius */
--radius-lg: var(--radius);            /* 12px - Large radius */
--radius-md: calc(var(--radius) - 2px); /* 10px - Medium radius */
--radius-sm: calc(var(--radius) - 4px); /* 8px - Small radius */
```

### Spacing Scale
The system uses a consistent 4px base spacing scale:

```css
/* Padding & Margin Scale */
p-1 = 4px    p-2 = 8px     p-3 = 12px    p-4 = 16px
p-5 = 20px   p-6 = 24px    p-8 = 32px    p-10 = 40px
p-12 = 48px  p-16 = 64px   p-20 = 80px   p-24 = 96px
```

### Component Spacing
```css
/* Common spacing patterns */
gap-1 = 4px     /* Tight spacing for related elements */
gap-2 = 8px     /* Default spacing between elements */
gap-4 = 16px    /* Comfortable spacing */
gap-6 = 24px    /* Generous spacing */
gap-8 = 32px    /* Section spacing */
gap-12 = 48px   /* Large section spacing */
gap-16 = 64px   /* Extra large spacing */
```

## 🎭 Shadows & Elevation

### Shadow System
```css
/* Subtle shadows for depth */
.shadow-xs { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }
```

### Text Shadows
```css
/* Subtle text shadows for better readability */
.drop-shadow-sm { filter: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05)); }
.drop-shadow { filter: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06)); }
.drop-shadow-md { filter: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06)); }
.drop-shadow-lg { filter: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1)); }
```

## 🎯 Component Styles

### Buttons
```tsx
// Button variants with rounded-full styling
const buttonVariants = {
  default: "bg-primary text-primary-foreground hover:bg-primary/90",
  destructive: "bg-destructive text-white hover:bg-destructive/90",
  outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
  secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
  ghost: "hover:bg-accent hover:text-accent-foreground",
  link: "text-primary underline-offset-4 hover:underline"
}

// Button sizes
const buttonSizes = {
  default: "h-10 px-5 py-2.5",  // 40px height, comfortable padding
  sm: "h-9 px-4 py-2",          // 36px height, compact
  lg: "h-11 px-6 py-3",         // 44px height, spacious
  icon: "size-10 rounded-full"   // Perfect circle for icons
}
```

**Key Button Features:**
- **Rounded-full** design for modern, pill-shaped buttons
- **Smooth transitions** with `transition-colors`
- **Focus-visible rings** for accessibility
- **Hover states** with opacity changes
- **Icon integration** with proper sizing

### Cards
```tsx
// Card styling with subtle shadows and rounded corners
const cardStyles = {
  base: "bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6",
  enhanced: {
    primary: "border-primary/20 bg-primary/5",
    secondary: "border-secondary/20 bg-secondary/5",
    outline: "border-2",
    ghost: "border-none bg-transparent shadow-none"
  }
}
```

**Card Features:**
- **Rounded-xl** corners (12px border radius)
- **Subtle background tinting** for variants
- **Consistent padding** and gap spacing
- **Flexible content layout** with header, content, footer
- **Enhanced variants** with icon support

### Inputs
```tsx
// Input styling with focus states
const inputStyles = `
  border-input 
  file:text-foreground 
  placeholder:text-muted-foreground 
  selection:bg-primary 
  selection:text-primary-foreground 
  flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 
  text-base shadow-xs transition-[color,box-shadow] outline-none
  focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]
  aria-invalid:ring-destructive/20 aria-invalid:border-destructive
`
```

**Input Features:**
- **Subtle shadows** for depth
- **Focus rings** with 3px width
- **Smooth transitions** for color and shadow
- **Accessible error states** with red rings
- **Consistent height** (36px) across components

### Sidebar
```tsx
// Sidebar styling with backdrop blur and transitions
const sidebarStyles = {
  width: "16rem",        // 256px desktop width
  widthMobile: "18rem",  // 288px mobile width
  widthIcon: "3rem",     // 48px collapsed width
  background: "#f7f8fc", // Light blue-gray background
  transition: "transition-[width] duration-200 ease-linear"
}
```

**Sidebar Features:**
- **Collapsible design** with smooth animations
- **Mobile-responsive** with sheet overlay
- **Keyboard shortcuts** (Cmd/Ctrl + B)
- **Tooltip integration** for collapsed state
- **Consistent spacing** and typography

## 🎨 Visual Effects

### Gradients
```css
/* Background gradients for visual interest */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }

/* Text gradients for emphasis */
.bg-clip-text { background-clip: text; }
.text-transparent { color: transparent; }
```

### Backdrop Effects
```css
/* Backdrop blur for modern glass effect */
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur { backdrop-filter: blur(8px); }

/* Background opacity for overlays */
.bg-black/60 { background-color: rgb(0 0 0 / 0.6); }
.bg-white/80 { background-color: rgb(255 255 255 / 0.8); }
```

### Hover Effects
```css
/* Smooth hover transitions */
.hover:scale-105 { transform: scale(1.05); }
.hover:scale-110 { transform: scale(1.1); }
.hover:-translate-y-2 { transform: translateY(-0.5rem); }

/* Transition timing */
.transition-all { transition-property: all; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.ease-linear { transition-timing-function: linear; }
```

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile-first breakpoints */
sm: 640px    /* Small devices */
md: 768px    /* Medium devices */
lg: 1024px   /* Large devices */
xl: 1280px   /* Extra large devices */
2xl: 1536px  /* 2X large devices */
```

### Responsive Patterns
```css
/* Grid layouts that adapt */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.md:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.lg:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

/* Text scaling */
.text-4xl { font-size: 2.25rem; }
.sm:text-5xl { font-size: 3rem; }
.md:text-6xl { font-size: 3.75rem; }
.lg:text-7xl { font-size: 4.5rem; }
```

## 🎯 Accessibility Features

### Focus Management
```css
/* Focus rings for keyboard navigation */
.focus-visible:ring-ring/50 { --tw-ring-color: rgb(var(--ring) / 0.5); }
.focus-visible:ring-[3px] { --tw-ring-offset-width: 3px; }

/* Screen reader support */
.sr-only { 
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

### Color Contrast
- **Primary colors** meet WCAG AA standards
- **Text colors** provide sufficient contrast ratios
- **Error states** use distinct colors and patterns
- **Focus indicators** are clearly visible

### Interactive States
```css
/* Disabled states */
.disabled:pointer-events-none { pointer-events: none; }
.disabled:opacity-50 { opacity: 0.5; }

/* Aria states */
.aria-invalid:ring-destructive/20 { --tw-ring-color: rgb(var(--destructive) / 0.2); }
.aria-invalid:border-destructive { border-color: rgb(var(--destructive)); }
```

## 🎨 Animation & Transitions

### Micro-interactions
```css
/* Smooth property transitions */
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; }
.transition-transform { transition-property: transform; }
.transition-all { transition-property: all; }

/* Easing functions */
.ease-linear { transition-timing-function: linear; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }
```

### Loading States
```css
/* Skeleton loading animations */
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: .5; }
}
```

## 🔧 Utility Classes

### Layout Utilities
```css
/* Flexbox utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
```

### Sizing Utilities
```css
/* Width and height */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }
.max-w-7xl { max-width: 80rem; }

/* Aspect ratios */
.aspect-square { aspect-ratio: 1 / 1; }
.aspect-video { aspect-ratio: 16 / 9; }
```

## 🎨 Design Tokens Summary

### Core Values
```css
/* Primary design tokens */
--primary-hue: 265deg;        /* Purple-blue hue */
--border-radius: 0.75rem;     /* 12px rounded corners */
--font-family: 'Urbanist';    /* Primary typeface */
--shadow-color: rgb(0 0 0);   /* Black shadows */
--transition-duration: 300ms;  /* Standard animation time */
```

### Component Tokens
```css
/* Button tokens */
--button-height: 2.5rem;      /* 40px default height */
--button-padding: 1.25rem;    /* 20px horizontal padding */
--button-border-radius: 9999px; /* Fully rounded */

/* Card tokens */
--card-padding: 1.5rem;       /* 24px internal padding */
--card-gap: 1.5rem;          /* 24px content spacing */
--card-border-radius: 0.75rem; /* 12px corners */

/* Input tokens */
--input-height: 2.25rem;      /* 36px height */
--input-padding: 0.75rem;     /* 12px horizontal padding */
--input-border-width: 1px;    /* Thin borders */
```

This design system creates a cohesive, modern, and accessible user interface that feels premium and professional while maintaining excellent usability across all devices and user contexts.
