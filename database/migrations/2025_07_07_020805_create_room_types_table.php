<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('room_types', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('hostel_id')->constrained('hostels')->onDelete('cascade');
            $table->string('name'); // e.g., "4-Bed Dorm", "Private Single", "Private Double"
            $table->text('description')->nullable();
            $table->enum('category', ['dorm', 'private', 'family'])->default('dorm');
            $table->enum('gender_type', ['male', 'female', 'mixed'])->default('mixed');
            $table->enum('semester_type', ['both', 'first_only', 'second_only'])->default('both'); // Academic terms
            $table->integer('capacity'); // Maximum number of students
            $table->integer('max_occupancy'); // Maximum students that can be assigned to rooms of this type
            $table->integer('beds'); // Number of beds
            // REMOVED: price_per_semester - pricing is now handled at hostel level
            $table->integer('available_rooms'); // Number of rooms of this type
            $table->json('amenities')->nullable(); // Room-specific amenities
            $table->json('photos')->nullable(); // Room photos
            $table->json('youtube_links')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['hostel_id', 'category', 'is_active']);
            // REMOVED: price_per_semester index
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('room_types');
    }
};
