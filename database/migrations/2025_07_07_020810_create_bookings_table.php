<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('booking_reference')->unique(); // Human-readable booking reference
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignUuid('hostel_id')->constrained('hostels')->onDelete('cascade');
            $table->foreignUuid('room_type_id')->constrained('room_types')->onDelete('cascade');
            $table->foreignUuid('room_id')->nullable()->constrained('rooms')->onDelete('set null'); // Assigned room
            $table->foreignUuid('bed_id')->nullable()->constrained('beds')->onDelete('set null'); // Assigned bed
            
            // Semester details (Ghana university system)
            $table->date('semester_start_date');
            $table->date('semester_end_date');
            $table->enum('semester', ['first', 'second', 'full_year']);
            $table->string('academic_year', 9); // e.g., "2024/2025"
            $table->integer('students'); // Number of students
            
            // Pricing (semester-based)
            $table->decimal('price_per_semester', 10, 2);
            $table->decimal('subtotal', 10, 2); // Price per semester * students
            $table->decimal('taxes', 10, 2)->default(0);
            $table->decimal('service_fee', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            
            // Payment
            $table->enum('payment_type', ['full', 'deposit'])->default('full');
            $table->decimal('amount_paid', 10, 2)->default(0);
            $table->decimal('amount_due', 10, 2)->default(0);
            $table->enum('payment_status', ['pending', 'partially_paid', 'paid', 'refunded', 'failed'])->default('pending');
            $table->string('payment_reference')->nullable(); // Paystack reference
            $table->json('payment_data')->nullable(); // Store payment gateway response data
            
            // Status tracking (university appropriate)
            $table->enum('status', ['pending', 'confirmed', 'moved_in', 'active', 'moved_out', 'cancelled', 'no_show'])->default('pending');
            $table->text('special_requests')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('checked_in_at')->nullable();
            $table->timestamp('checked_out_at')->nullable();
            
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index(['hostel_id', 'semester_start_date', 'semester_end_date']);
            $table->index(['room_type_id', 'semester_start_date', 'semester_end_date']);
            $table->index(['payment_status', 'status']);
            $table->index(['semester', 'academic_year']);
            $table->index('booking_reference');
            $table->index('bed_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
