<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\RoomType;
use App\Models\Room;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix room numbering to be sequential within each room type
        $roomTypes = RoomType::with('rooms')->get();
        
        foreach ($roomTypes as $roomType) {
            $rooms = $roomType->rooms()->orderBy('room_number')->get();
            
            foreach ($rooms as $index => $room) {
                $newRoomNumber = $index + 1; // Sequential numbering starting from 1
                $room->update(['room_number' => $newRoomNumber]);
            }
            
            // Update male_rooms, female_rooms, mixed_rooms based on gender_type
            // Clear existing gender room counts
            $roomType->rooms()->update([
                'male_rooms' => 0,
                'female_rooms' => 0,
                'mixed_rooms' => 0,
            ]);
            
            // Set appropriate room counts based on room type gender_type
            $availableRooms = $roomType->available_rooms;
            
            switch ($roomType->gender_type) {
                case 'male':
                    $roomType->rooms()->update(['male_rooms' => 1]);
                    break;
                case 'female':
                    $roomType->rooms()->update(['female_rooms' => 1]);
                    break;
                case 'mixed':
                    $roomType->rooms()->update(['mixed_rooms' => 1]);
                    break;
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We won't reverse the room numbering fix as it's an improvement
        // But we can reset the gender room counts if needed
        \App\Models\Room::query()->update([
            'male_rooms' => 0,
            'female_rooms' => 0,
            'mixed_rooms' => 0,
        ]);
    }
};
