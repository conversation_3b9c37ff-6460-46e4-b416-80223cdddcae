<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['student', 'hostel_admin', 'super_admin'])->default('student')->after('email');
            $table->string('student_id')->nullable()->after('postal_code');
            $table->string('university')->nullable()->after('student_id');
            $table->string('program')->nullable()->after('university');
            $table->enum('year_of_study', ['1', '2', '3', '4', '5', '6'])->nullable()->after('program');
            $table->enum('gender', ['male', 'female'])->nullable()->after('year_of_study');
            $table->string('emergency_contact_name')->nullable()->after('date_of_birth');
            $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
            $table->boolean('is_verified')->default(false)->after('role');
            $table->string('phone', 20)->nullable()->after('is_verified');
            $table->date('date_of_birth')->nullable()->after('phone');
            $table->text('bio')->nullable()->after('date_of_birth');
            $table->string('profile_photo')->nullable()->after('bio');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['role', 'is_verified', 'phone', 'date_of_birth', 'bio', 'profile_photo']);
        });
    }
};
