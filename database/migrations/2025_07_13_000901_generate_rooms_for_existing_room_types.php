<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('room_types', function (Blueprint $table) {
            // No schema changes needed
        });

        \App\Models\RoomType::all()->each(function ($roomType) {
            $existingRoomsCount = $roomType->rooms()->count();
            $roomsToCreate = $roomType->available_rooms - $existingRoomsCount;

            if ($roomsToCreate > 0) {
                for ($i = 1; $i <= $roomsToCreate; $i++) {
                    $roomNumber = $roomType->rooms()->max('room_number') + 1 ?? 101;
                    
                    $roomData = [
                        'hostel_id' => $roomType->hostel_id,
                        'room_number' => $roomNumber,
                        'status' => 'available',
                    ];
                    
                    // Only add capacity if the column exists
                    if (Schema::hasColumn('rooms', 'capacity')) {
                        $roomData['capacity'] = $roomType->capacity;
                    }
                    
                    $roomType->rooms()->create($roomData);
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Optionally, delete generated rooms, but this might be dangerous
        // We can leave it empty or add logic if needed
    }
};
