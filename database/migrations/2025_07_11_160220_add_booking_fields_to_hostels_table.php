<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hostels', function (Blueprint $table) {
            $table->boolean('allow_deposit')->default(false)->after('is_verified');
            $table->boolean('is_per_semester')->default(true)->after('allow_deposit');
            $table->enum('payment_period', ['month', 'semester', 'year'])->default('semester');
            $table->enum('hostel_type', ['university', 'homestel', 'other'])->default('other');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hostels', function (Blueprint $table) {
            $table->dropColumn('allow_deposit');
            $table->dropColumn('is_per_semester');
        });
    }
};
