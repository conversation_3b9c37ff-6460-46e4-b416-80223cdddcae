<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hostels', function (Blueprint $table) {
            $table->decimal('base_price', 10, 2)->nullable()->after('payment_period');
            $table->decimal('service_fee', 10, 2)->default(0)->after('base_price');
            $table->decimal('deposit_percentage', 5, 2)->default(30)->after('service_fee');
            $table->decimal('cancellation_fee', 10, 2)->default(0)->after('deposit_percentage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hostels', function (Blueprint $table) {
            $table->dropColumn(['base_price', 'service_fee', 'deposit_percentage', 'cancellation_fee']);
        });
    }
};
