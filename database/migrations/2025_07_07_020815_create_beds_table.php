<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('beds', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('room_id')->constrained('rooms')->onDelete('cascade');
            $table->string('bed_number'); // e.g., "B1", "B2", "Upper-A", "Lower-A"
            $table->enum('bed_type', ['single', 'bunk_upper', 'bunk_lower'])->default('single');
            $table->enum('status', ['available', 'occupied', 'reserved', 'maintenance', 'blocked'])->default('available');
            $table->foreignId('current_student_id')->nullable()->constrained('users')->onDelete('set null'); // Currently assigned student
            $table->text('notes')->nullable(); // Internal notes for housekeeping/management
            $table->timestamps();
            
            $table->index(['room_id', 'status']);
            $table->unique(['room_id', 'bed_number']);
            $table->index('current_student_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('beds');
    }
}; 