<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rooms', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('room_type_id')->constrained('room_types')->onDelete('cascade');
            $table->string('room_number')->nullable(); // e.g., "101", "A1", "Dorm 1"
            $table->string('floor')->nullable();
            $table->enum('status', ['available', 'occupied', 'maintenance', 'blocked'])->default('available');
            $table->text('notes')->nullable(); // Internal notes for housekeeping/management
            $table->integer('male_rooms')->default(0);
            $table->integer('female_rooms')->default(0);
            $table->integer('mixed_rooms')->default(0);
            $table->timestamps();
            
            $table->index(['room_type_id', 'status']);
            $table->unique(['room_type_id', 'room_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rooms');
    }
};
