<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\RoomType;
use App\Models\Room;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rooms', function (Blueprint $table) {
            $table->enum('gender_allocated', ['male', 'female', 'mixed'])->default('mixed')->after('status');
        });

        // Populate gender allocation based on room type and existing allocation numbers
        $roomTypes = RoomType::with('rooms')->get();
        
        foreach ($roomTypes as $roomType) {
            $rooms = $roomType->rooms()->orderBy('room_number')->get();
            $totalRooms = $rooms->count();
            
            // Allocate rooms based on the gender allocation numbers set by super admin
            $maleRooms = $roomType->male_rooms ?? 0;
            $femaleRooms = $roomType->female_rooms ?? 0;
            $mixedRooms = $roomType->mixed_rooms ?? 0;
            
            $roomIndex = 0;
            
            // Allocate male rooms first
            for ($i = 0; $i < $maleRooms && $roomIndex < $totalRooms; $i++) {
                $rooms[$roomIndex]->update(['gender_allocated' => 'male']);
                $roomIndex++;
            }
            
            // Allocate female rooms
            for ($i = 0; $i < $femaleRooms && $roomIndex < $totalRooms; $i++) {
                $rooms[$roomIndex]->update(['gender_allocated' => 'female']);
                $roomIndex++;
            }
            
            // Allocate mixed rooms
            for ($i = 0; $i < $mixedRooms && $roomIndex < $totalRooms; $i++) {
                $rooms[$roomIndex]->update(['gender_allocated' => 'mixed']);
                $roomIndex++;
            }
            
            // If there are remaining rooms and no specific allocation was set, 
            // default based on room type gender_type
            while ($roomIndex < $totalRooms) {
                $gender = $roomType->gender_type === 'mixed' ? 'mixed' : $roomType->gender_type;
                $rooms[$roomIndex]->update(['gender_allocated' => $gender]);
                $roomIndex++;
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rooms', function (Blueprint $table) {
            $table->dropColumn('gender_allocated');
        });
    }
};
