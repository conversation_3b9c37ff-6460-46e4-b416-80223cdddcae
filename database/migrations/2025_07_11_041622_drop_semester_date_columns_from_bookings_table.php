<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Drop indexes that include the date columns
            $table->dropIndex(['hostel_id', 'semester_start_date', 'semester_end_date']);
            $table->dropIndex(['room_type_id', 'semester_start_date', 'semester_end_date']);
            
            // Drop the date columns
            $table->dropColumn(['semester_start_date', 'semester_end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add back the date columns
            $table->date('semester_start_date')->after('bed_id');
            $table->date('semester_end_date')->after('semester_start_date');
            
            // Add back the indexes
            $table->index(['hostel_id', 'semester_start_date', 'semester_end_date']);
            $table->index(['room_type_id', 'semester_start_date', 'semester_end_date']);
        });
    }
};
