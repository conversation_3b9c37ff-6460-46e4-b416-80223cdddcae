<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super admin
        User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'super_admin',
            'phone' => '+233244000000',
            'gender' => 'male',
            'is_verified' => true,
            'email_verified_at' => now(),
        ]);

        // Create a sample hostel admin
        User::create([
            'name' => 'Hostel Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'hostel_admin',
            'phone' => '+233244111111',
            'gender' => 'female',
            'is_verified' => true,
            'email_verified_at' => now(),
        ]);

        // Create a sample student
        User::create([
            'name' => 'John Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
            'phone' => '+233244222222',
            'gender' => 'male',
            'is_verified' => true,
            'email_verified_at' => now(),
        ]);

        // Create a female student for testing gender filtering
        User::create([
            'name' => 'Jane Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
            'phone' => '+233244333333',
            'gender' => 'female',
            'is_verified' => true,
            'email_verified_at' => now(),
        ]);
    }
}
