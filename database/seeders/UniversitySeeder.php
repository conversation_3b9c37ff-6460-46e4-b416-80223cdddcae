<?php

namespace Database\Seeders;

use App\Models\University;
use Illuminate\Database\Seeder;

class UniversitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $universities = [
            'University of Ghana',
            'Kwame Nkrumah University of Science and Technology',
            'University of Cape Coast',
            'University for Development Studies',
            'University of Education, Winneba',
            'Ghana Institute of Management and Public Administration',
            'Ashesi University',
            'Central University',
            'Valley View University',
            'Academic City University College',
            'Presbyterian University College',
            'Ghana Communication Technology University',
            'University of Professional Studies',
            'All Nations University',
            'Methodist University College Ghana',
        ];

        foreach ($universities as $university) {
            University::firstOrCreate(['name' => $university]);
        }
    }
}