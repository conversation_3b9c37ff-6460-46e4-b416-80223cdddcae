<?php

namespace Database\Seeders;

use App\Models\AcademicYear;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class AcademicYearSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $academicYears = [
            [
                'name' => '2023/2024',
                'display_name' => '2023/2024 Academic Year',
                'start_date' => Carbon::create(2023, 9, 1),
                'end_date' => Carbon::create(2024, 8, 31),
                'is_active' => true,
                'is_current' => false,
            ],
            [
                'name' => '2024/2025',
                'display_name' => '2024/2025 Academic Year',
                'start_date' => Carbon::create(2024, 9, 1),
                'end_date' => Carbon::create(2025, 8, 31),
                'is_active' => true,
                'is_current' => true,
            ],
            [
                'name' => '2025/2026',
                'display_name' => '2025/2026 Academic Year',
                'start_date' => Carbon::create(2025, 9, 1),
                'end_date' => Carbon::create(2026, 8, 31),
                'is_active' => true,
                'is_current' => false,
            ],
            [
                'name' => '2026/2027',
                'display_name' => '2026/2027 Academic Year',
                'start_date' => Carbon::create(2026, 9, 1),
                'end_date' => Carbon::create(2027, 8, 31),
                'is_active' => true,
                'is_current' => false,
            ],
        ];

        foreach ($academicYears as $year) {
            AcademicYear::firstOrCreate(
                ['name' => $year['name']],
                $year
            );
        }
    }
}