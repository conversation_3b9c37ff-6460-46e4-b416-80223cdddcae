<?php

use App\Helpers\ImageHelper;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\HostelController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\RoomTypeController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\HostelAdmin\DashboardController as HostelAdminDashboardController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

Route::get('/', function () {
    $featuredHostels = \App\Models\Hostel::with(['roomTypes'])
        ->orderBy('created_at', 'desc')
        ->limit(9)
        ->get()
        ->map(function ($hostel) {
            return [
                'id' => $hostel->id,
                'name' => $hostel->name,
                'city' => $hostel->city,
                'state' => $hostel->state,
                'image_url' => ImageHelper::getImageUrl($hostel->photos[0] ?? null),
                'amenities' => $hostel->amenities ?? [],
                'hostel_type' => $hostel->hostel_type,
            ];
        });

    return Inertia::render('welcome', [
        'featured_hostels' => $featuredHostels
    ]);
})->name('home');

// Search routes (public)
Route::get('/search', [SearchController::class, 'index'])->name('search.index');
Route::get('/api/search', [SearchController::class, 'search'])->name('api.search');
Route::get('/api/search/suggestions', [SearchController::class, 'suggestions'])->name('api.search.suggestions');
Route::get('/api/search/popular-destinations', [SearchController::class, 'popularDestinations'])->name('api.search.popular');
Route::get('/api/search/price-range', [SearchController::class, 'priceRange'])->name('api.search.price-range');
Route::post('/api/search/availability', [SearchController::class, 'checkAvailability'])->name('api.search.availability');

// Public hostel routes
Route::get('/hostels', [HostelController::class, 'index'])->name('hostels.index');
Route::get('/hostels/{hostel}', [HostelController::class, 'show'])->name('hostels.show');

// API routes for public hostel data
Route::get('/api/hostels', [HostelController::class, 'api'])->name('api.hostels');



// Error logging endpoint
Route::post('/api/client-errors', [App\Http\Controllers\ErrorController::class, 'logClientError'])->name('api.client-errors');

// Payment routes (public for callbacks and webhooks)
Route::get('/payments/callback', [PaymentController::class, 'callback'])->name('payments.callback');
Route::post('/payments/webhook', [PaymentController::class, 'webhook'])->name('payments.webhook');
Route::get('/api/payments/public-key', [PaymentController::class, 'publicKey'])->name('api.payments.public-key');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Hostel Admin Dashboard
    Route::middleware(['role:hostel_admin'])->group(function () {
        Route::get('/admin/dashboard', [HostelAdminDashboardController::class, 'index'])->name('admin.dashboard');
    });

    // Super Admin Routes
    Route::middleware(['role:super_admin'])->prefix('admin')->group(function () {
        // User Management
        Route::get('/users', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'users'])->name('admin.users');
        Route::post('/users', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'createUser'])->name('admin.users.create');
        Route::put('/users/{user}', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'updateUser'])->name('admin.users.update');
        Route::post('/users/{user}/toggle-verification', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'toggleUserVerification'])->name('admin.users.toggle-verification');
        Route::delete('/users/{user}', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'deleteUser'])->name('admin.users.delete');
        
        // Platform Analytics
        Route::get('/analytics', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'analytics'])->name('admin.analytics');
        
        // Hostel Management
        Route::get('/hostels', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'hostels'])->name('admin.hostels');
        Route::get('/hostels/create', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'createHostelForm'])->name('admin.hostels.create.form');
        Route::post('/hostels', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'createHostel'])->name('admin.hostels.create');
        Route::get('/hostels/{hostel}/edit', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'editHostelForm'])->name('admin.hostels.edit');
        Route::put('/hostels/{hostel}', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'updateHostel'])->name('admin.hostels.update');
        Route::delete('/hostels/{hostel}', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'deleteHostel'])->name('admin.hostels.delete');
        Route::post('/hostels/{hostel}/toggle-verification', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'toggleHostelVerification'])->name('admin.hostels.toggle-verification');
        
        // Payment Management
        Route::get('/payments', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'payments'])->name('admin.payments');
        
        // System Settings
        Route::get('/settings', [\App\Http\Controllers\SuperAdmin\AdminController::class, 'settings'])->name('admin.settings');

        // University Management
        Route::get('/universities', [\App\Http\Controllers\SuperAdmin\UniversityController::class, 'index'])->name('admin.universities');
        Route::post('/universities', [\App\Http\Controllers\SuperAdmin\UniversityController::class, 'store'])->name('admin.universities.store');
        Route::put('/universities/{university}', [\App\Http\Controllers\SuperAdmin\UniversityController::class, 'update'])->name('admin.universities.update');
        Route::delete('/universities/{university}', [\App\Http\Controllers\SuperAdmin\UniversityController::class, 'destroy'])->name('admin.universities.destroy');

        // Academic Year Management
        Route::get('/academic-years', [\App\Http\Controllers\SuperAdmin\AcademicYearController::class, 'index'])->name('admin.academic-years');
        Route::post('/academic-years', [\App\Http\Controllers\SuperAdmin\AcademicYearController::class, 'store'])->name('admin.academic-years.store');
        Route::put('/academic-years/{academicYear}', [\App\Http\Controllers\SuperAdmin\AcademicYearController::class, 'update'])->name('admin.academic-years.update');
        Route::delete('/academic-years/{academicYear}', [\App\Http\Controllers\SuperAdmin\AcademicYearController::class, 'destroy'])->name('admin.academic-years.destroy');
        Route::post('/academic-years/{academicYear}/set-current', [\App\Http\Controllers\SuperAdmin\AcademicYearController::class, 'setCurrent'])->name('admin.academic-years.set-current');
    });

    // Booking routes (for authenticated users)
    Route::get('/bookings', [BookingController::class, 'index'])->name('bookings.index');
    Route::get('/bookings/create', [BookingController::class, 'create'])->name('bookings.create');
    Route::post('/bookings', [BookingController::class, 'store'])->name('bookings.store');
    Route::get('/bookings/{booking}', [BookingController::class, 'show'])->name('bookings.show');
    Route::get('/bookings/{booking}/edit', [BookingController::class, 'edit'])->name('bookings.edit');
    Route::put('/bookings/{booking}', [BookingController::class, 'update'])->name('bookings.update');
    Route::get('/bookings/room-types/{roomType}/available-rooms', [BookingController::class, 'getAvailableRooms'])->name('bookings.available-rooms');
    
    // Booking management actions
    Route::get('/bookings/{booking}/payment', [BookingController::class, 'payment'])->name('bookings.payment');
    Route::post('/bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');
    
    // Payment routes (authenticated)
    Route::get('/payments/{booking}/initialize', [PaymentController::class, 'initialize'])->name('payments.initialize');
    Route::get('/payments/{booking}/retry', [PaymentController::class, 'retry'])->name('payments.retry');
    Route::get('/payments/{booking}/status', [PaymentController::class, 'status'])->name('payments.status');
    
    // Booking actions for hostel owners
    Route::middleware(['role:hostel_admin,super_admin'])->group(function () {
        Route::post('/bookings/{booking}/confirm', [BookingController::class, 'confirm'])->name('bookings.confirm');
        Route::post('/bookings/{booking}/check-in', [BookingController::class, 'checkIn'])->name('bookings.checkin');
        Route::post('/bookings/{booking}/check-out', [BookingController::class, 'checkOut'])->name('bookings.checkout');
        Route::post('/payments/{booking}/refund', [PaymentController::class, 'refund'])->name('payments.refund');
    });
    
    // Booking analytics API
    Route::get('/api/bookings/analytics', [BookingController::class, 'analytics'])->name('api.bookings.analytics');

    // Hostel management routes (for hostel owners)
    Route::middleware(['role:hostel_admin,super_admin'])->group(function () {
        Route::get('/hostels/create', [HostelController::class, 'create'])->name('hostels.create');
        Route::post('/hostels', [HostelController::class, 'store'])->name('hostels.store');
        Route::get('/hostels/{hostel}/edit', [HostelController::class, 'edit'])->name('hostels.edit');
        Route::put('/hostels/{hostel}', [HostelController::class, 'update'])->name('hostels.update');
        Route::delete('/hostels/{hostel}', [HostelController::class, 'destroy'])->name('hostels.destroy');
        Route::patch('/hostels/{hostel}/toggle-status', [HostelController::class, 'toggleStatus'])->name('hostels.toggle-status');
        
        // Room type management (nested under hostels)
        Route::get('/hostels/{hostel}/room-types', [RoomTypeController::class, 'index'])->name('hostels.room-types.index');
        Route::get('/hostels/{hostel}/room-types/create', [RoomTypeController::class, 'create'])->name('hostels.room-types.create');
        Route::post('/hostels/{hostel}/room-types', [RoomTypeController::class, 'store'])->name('hostels.room-types.store');
        Route::get('/hostels/{hostel}/room-types/{roomType}', [RoomTypeController::class, 'show'])->name('hostels.room-types.show');
        Route::get('/hostels/{hostel}/room-types/{roomType}/edit', [RoomTypeController::class, 'edit'])->name('hostels.room-types.edit');
        Route::put('/hostels/{hostel}/room-types/{roomType}', [RoomTypeController::class, 'update'])->name('hostels.room-types.update');
        Route::delete('/hostels/{hostel}/room-types/{roomType}', [RoomTypeController::class, 'destroy'])->name('hostels.room-types.destroy');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
